// Transactions Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initTransactionsPage();
});

let currentPage = 0;
let currentLimit = 10;

function initTransactionsPage() {
    setupTransactionFilters();
    setupPagination();
    loadTransactions();
}

function setupTransactionFilters() {
    // Transaction filters
    const typeFilter = document.getElementById('transactionType');
    const statusFilter = document.getElementById('transactionStatus');
    
    if (typeFilter) {
        typeFilter.addEventListener('change', loadTransactions);
    }
    
    if (statusFilter) {
        statusFilter.addEventListener('change', loadTransactions);
    }
}

function setupPagination() {
    // Pagination
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (currentPage > 0) {
                currentPage--;
                loadTransactions();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            currentPage++;
            loadTransactions();
        });
    }
}

async function loadTransactions() {
    const typeFilter = document.getElementById('transactionType');
    const statusFilter = document.getElementById('transactionStatus');
    const transactionsList = document.getElementById('transactionsList');
    
    if (!transactionsList) return;
    
    transactionsList.innerHTML = '<div class="loading">Loading transactions...</div>';
    
    try {
        const filters = {
            type: typeFilter ? typeFilter.value : '',
            status: statusFilter ? statusFilter.value : '',
            page: currentPage,
            limit: currentLimit
        };
        
        const response = await apiCall('get_transactions', filters);
        
        if (response.success) {
            displayTransactions(response.transactions);
            updatePagination(response.pagination);
        } else {
            transactionsList.innerHTML = '<div class="no-data">Failed to load transactions</div>';
        }
    } catch (error) {
        console.error('Error loading transactions:', error);
        transactionsList.innerHTML = '<div class="no-data">Error loading transactions</div>';
    }
}

function displayTransactions(transactions) {
    const transactionsList = document.getElementById('transactionsList');
    
    if (!transactions || transactions.length === 0) {
        transactionsList.innerHTML = '<div class="no-data">No transactions found</div>';
        return;
    }
    
    const transactionsHTML = transactions.map(tx => `
        <div class="transaction-item">
            <div class="transaction-header">
                <span class="transaction-type ${tx.type}">${formatTransactionType(tx.type)}</span>
                <span class="transaction-status ${tx.status}">${formatStatus(tx.status)}</span>
            </div>
            <div class="transaction-details">
                <div class="transaction-amount ${tx.type === 'withdrawal' ? 'negative' : 'positive'}">
                    ${tx.type === 'withdrawal' ? '-' : '+'}${tx.amount_formatted} TRX
                </div>
                <div class="transaction-info">
                    <div class="transaction-hash">
                        <span class="label">Hash:</span>
                        <span class="hash">${tx.hash || 'Pending'}</span>
                    </div>
                    <div class="transaction-date">
                        <span class="label">Date:</span>
                        <span class="date">${formatDate(tx.created_at)}</span>
                    </div>
                    ${tx.to_address ? `
                    <div class="transaction-address">
                        <span class="label">To:</span>
                        <span class="address">${tx.to_address}</span>
                    </div>
                    ` : ''}
                    ${tx.from_address ? `
                    <div class="transaction-address">
                        <span class="label">From:</span>
                        <span class="address">${tx.from_address}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('');
    
    transactionsList.innerHTML = transactionsHTML;
}

function updatePagination(pagination) {
    const pageInfo = document.getElementById('pageInfo');
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');
    
    if (pageInfo) {
        pageInfo.textContent = `Page ${currentPage + 1}`;
    }
    
    if (prevBtn) {
        prevBtn.disabled = currentPage === 0;
    }
    
    if (nextBtn) {
        nextBtn.disabled = !pagination.has_more;
    }
}

function formatTransactionType(type) {
    const types = {
        'deposit': 'Deposit',
        'withdrawal': 'Withdrawal',
        'sweep': 'Sweep'
    };
    return types[type] || type;
}

function formatStatus(status) {
    const statuses = {
        'pending': 'Pending',
        'confirmed': 'Confirmed',
        'failed': 'Failed'
    };
    return statuses[status] || status;
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// Shared utility functions
async function apiCall(endpoint, data = null) {
    const response = await fetch('ajax.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: endpoint,
            ...(data || {})
        })
    });
    return await response.json();
}

function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';
    
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 3000);
}
