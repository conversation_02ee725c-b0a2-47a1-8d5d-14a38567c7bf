<?php
/**
 * Check Sweep Configuration
 * Verifies that all required configuration for sweep functionality is present
 */

require_once 'vendor/autoload.php';

use Simbi\Tls\Config\Config;

echo "🔍 Checking Sweep Funds Configuration\n";
echo "=====================================\n\n";

try {
    // Load configuration
    Config::load();
    
    // Check required environment variables
    $requiredVars = [
        'MASTER_ADDRESS' => 'Master wallet address for receiving swept funds',
        'MASTER_PRIVATE_KEY' => 'Master wallet private key for transactions',
        'TRON_NETWORK' => 'TRON network (mainnet, nile, shasta)',
        'USDT_CONTRACT' => 'USDT contract address for token sweeping'
    ];
    
    $allConfigured = true;
    
    foreach ($requiredVars as $var => $description) {
        $value = Config::get($var, '');
        
        if (empty($value)) {
            echo "❌ {$var}: NOT CONFIGURED\n";
            echo "   Description: {$description}\n\n";
            $allConfigured = false;
        } else {
            // Mask sensitive data
            if (strpos($var, 'PRIVATE_KEY') !== false) {
                $displayValue = substr($value, 0, 8) . '...' . substr($value, -8);
            } elseif (strlen($value) > 50) {
                $displayValue = substr($value, 0, 20) . '...' . substr($value, -10);
            } else {
                $displayValue = $value;
            }
            
            echo "✅ {$var}: {$displayValue}\n";
        }
    }
    
    echo "\n";
    
    if ($allConfigured) {
        echo "🎉 All configuration variables are set!\n\n";
        
        // Test TRON service initialization
        echo "Testing TRON Service initialization...\n";
        try {
            $tronService = new \Simbi\Tls\Services\TronService();
            echo "✅ TRON Service initialized successfully\n";
        } catch (Exception $e) {
            echo "❌ TRON Service initialization failed: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "⚠️  Some configuration variables are missing!\n";
        echo "Please add them to your .env file:\n\n";
        
        echo "# Master wallet configuration\n";
        echo "MASTER_ADDRESS=your_master_wallet_address_here\n";
        echo "MASTER_PRIVATE_KEY=your_master_wallet_private_key_here\n\n";
        
        echo "# TRON network configuration\n";
        echo "TRON_NETWORK=nile  # or mainnet for production\n";
        echo "USDT_CONTRACT=TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf  # Nile testnet USDT\n";
        echo "# USDT_CONTRACT=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t  # Mainnet USDT\n\n";
    }
    
    // Additional checks
    echo "Additional Configuration Checks:\n";
    echo "--------------------------------\n";
    
    $network = Config::get('TRON_NETWORK', 'nile');
    echo "Current Network: {$network}\n";
    
    if ($network === 'mainnet') {
        echo "⚠️  You are configured for MAINNET - ensure you're using real funds!\n";
    } else {
        echo "ℹ️  You are configured for TESTNET - safe for testing\n";
    }
    
    // Check database connection
    echo "\nTesting database connection...\n";
    try {
        $database = new \Simbi\Tls\Config\Database();
        $pdo = $database->getConnection();
        echo "✅ Database connection successful\n";
        
        // Check if wallets table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'wallets'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Wallets table exists\n";
            
            // Check wallet count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM wallets");
            $result = $stmt->fetch();
            echo "ℹ️  Total wallets in database: " . $result['count'] . "\n";
        } else {
            echo "❌ Wallets table not found - run database setup\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Configuration check failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🔧 Configuration check complete!\n";
?>
