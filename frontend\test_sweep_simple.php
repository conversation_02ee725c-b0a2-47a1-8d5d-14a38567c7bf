<?php
/**
 * Simple Sweep Test - Minimal test to isolate the issue
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Set JSON header
header('Content-Type: application/json');

// Check if this is an AJAX request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (isset($input['action']) && $input['action'] === 'test_sweep') {
        
        // Check session
        if (!isset($_SESSION['token']) || !isset($_SESSION['user_id'])) {
            echo json_encode(['success' => false, 'error' => 'Not authenticated']);
            exit;
        }
        
        try {
            // Include API wrapper
            require_once 'api_psr4.php';
            
            $api = new APIWrapper();
            $api->setToken($_SESSION['token']);
            
            // Call sweep funds
            $result = $api->sweepFunds();
            
            // Log the raw result for debugging
            error_log("Sweep result: " . print_r($result, true));
            
            // Ensure we have a proper response
            if (!is_array($result)) {
                echo json_encode(['success' => false, 'error' => 'Invalid API response', 'raw_result' => $result]);
                exit;
            }
            
            // Return the result
            echo json_encode($result);
            
        } catch (Exception $e) {
            error_log("Sweep test error: " . $e->getMessage());
            echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
        }
        
        exit;
    }
}

// If not AJAX, show test page
?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Sweep Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .error { background: #ffe6e6; }
        .success { background: #e6ffe6; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🧪 Simple Sweep Test</h1>
    
    <?php if (isset($_SESSION['token'])): ?>
        <p>✅ Session active for user ID: <?= $_SESSION['user_id'] ?></p>
        <button onclick="testSweep()">Test Sweep Funds</button>
        <div id="result"></div>
        
        <script>
        async function testSweep() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '⏳ Testing...';
            
            try {
                const response = await fetch('test_sweep_simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'test_sweep'
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const responseText = await response.text();
                console.log('Raw response:', responseText);
                
                resultDiv.innerHTML = '<div class="result"><h3>Raw Response:</h3><pre>' + responseText + '</pre></div>';
                
                try {
                    const jsonData = JSON.parse(responseText);
                    resultDiv.innerHTML += '<div class="result success"><h3>✅ Parsed JSON:</h3><pre>' + JSON.stringify(jsonData, null, 2) + '</pre></div>';
                } catch (jsonError) {
                    resultDiv.innerHTML += '<div class="result error"><h3>❌ JSON Parse Error:</h3><pre>' + jsonError.message + '</pre></div>';
                    
                    // Check for common issues
                    if (responseText.includes('Fatal error')) {
                        resultDiv.innerHTML += '<div class="result error">⚠️ PHP Fatal Error detected in response</div>';
                    }
                    if (responseText.includes('Warning:')) {
                        resultDiv.innerHTML += '<div class="result error">⚠️ PHP Warning detected in response</div>';
                    }
                    if (responseText.includes('Notice:')) {
                        resultDiv.innerHTML += '<div class="result error">⚠️ PHP Notice detected in response</div>';
                    }
                    if (responseText.includes('<?php')) {
                        resultDiv.innerHTML += '<div class="result error">⚠️ PHP code detected in response</div>';
                    }
                }
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error">Network Error: ' + error.message + '</div>';
            }
        }
        </script>
        
    <?php else: ?>
        <p>❌ No session found. Please login first.</p>
        <a href="index.php">Go to Login</a>
    <?php endif; ?>
    
</body>
</html>
