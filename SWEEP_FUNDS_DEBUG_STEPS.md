# 🔍 Sweep Funds "Invalid JSON Response" - Debug Steps

## 🚨 Current Issue
The `sweep_funds` functionality is returning "Invalid JSON response" error. This means the backend is not returning valid JSON, which could be due to PHP errors, configuration issues, or other problems.

## 🛠️ Step-by-Step Debugging Process

### Step 1: Check Your Session
1. **Login to the frontend** first to establish a session
2. Make sure you're logged in successfully

### Step 2: Use the Simple Test Tool
1. **Navigate to**: `http://localhost/tls/frontend/test_sweep_simple.php`
2. **Click "Test Sweep Funds"**
3. **Check the console** (F12 → Console) for detailed error messages
4. **Look at the raw response** to see what's actually being returned

### Step 3: Check Backend Configuration
1. **Run the configuration check**:
   ```bash
   cd backend
   php check_sweep_config.php
   ```
2. **Verify all required settings are configured**:
   - ✅ MASTER_ADDRESS
   - ✅ MASTER_PRIVATE_KEY  
   - ✅ TRON_NETWORK
   - ✅ USDT_CONTRACT

### Step 4: Test Backend Directly
1. **Run the backend test**:
   ```bash
   php test_backend_direct.php
   ```
2. **Check if the backend API is responding correctly**

### Step 5: Check PHP Error Logs
1. **Check your web server error logs**:
   - Apache: `/var/log/apache2/error.log`
   - Nginx: `/var/log/nginx/error.log`
   - XAMPP: `xampp/apache/logs/error.log`
   - WAMP: `wamp/logs/apache_error.log`

2. **Look for PHP errors** around the time you test sweep_funds

### Step 6: Enable Debug Mode
1. **Add to your `backend/.env` file**:
   ```env
   API_DEBUG=true
   ```
2. **This will enable more detailed error logging**

## 🔧 Common Issues & Solutions

### Issue 1: "Master address not configured"
**Cause**: Missing MASTER_ADDRESS in .env file
**Solution**: Add `MASTER_ADDRESS=your_wallet_address` to backend/.env

### Issue 2: "Wallet not found"
**Cause**: User doesn't have a wallet created
**Solution**: Create a wallet first using the "Create Wallet" button

### Issue 3: PHP Fatal Error
**Cause**: Missing dependencies or syntax errors
**Solution**: 
- Run `composer install` in backend directory
- Check PHP error logs for specific error

### Issue 4: TRON API Connection Error
**Cause**: Network issues or invalid TRON configuration
**Solution**: 
- Check internet connection
- Verify TRON_NETWORK setting (nile/mainnet)
- Check if TronGrid API is accessible

### Issue 5: Database Connection Error
**Cause**: Database not accessible
**Solution**:
- Check database credentials in .env
- Ensure database server is running
- Verify database exists

## 📋 Quick Fix Checklist

- [ ] **Session Active**: User is logged in
- [ ] **Configuration**: All .env variables set
- [ ] **Database**: Connection working
- [ ] **Wallet**: User has a wallet created
- [ ] **Network**: TRON API accessible
- [ ] **Logs**: No PHP errors in logs

## 🧪 Test Files Created

1. **`frontend/test_sweep_simple.php`** - Simple isolated test
2. **`frontend/debug_sweep_raw.php`** - Comprehensive debug tool
3. **`backend/check_sweep_config.php`** - Configuration checker
4. **`test_backend_direct.php`** - Direct backend API test

## 📞 Getting Help

If you're still having issues after following these steps:

1. **Run all test files** and collect the output
2. **Check PHP error logs** for any errors
3. **Note the exact error messages** you're seeing
4. **Provide the raw response** from the test tools

## 🎯 Expected Working Response

When working correctly, sweep_funds should return:

```json
{
  "success": true,
  "message": "Funds swept successfully",
  "transaction_hash": "abc123...",
  "amount": "100.000000",
  "status": "swept"
}
```

Or if no funds:

```json
{
  "success": true,
  "message": "No funds available to sweep",
  "amount": "0",
  "status": "no_funds"
}
```

## 🚀 Next Steps

1. **Follow the debugging steps above**
2. **Use the test tools to identify the exact issue**
3. **Fix the root cause based on the findings**
4. **Test again to verify the fix**

The "Invalid JSON response" error means something is preventing the backend from returning proper JSON. The debugging tools will help identify exactly what that is.
