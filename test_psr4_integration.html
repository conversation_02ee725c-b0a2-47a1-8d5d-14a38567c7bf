<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PSR-4 Frontend Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-container { max-width: 1200px; margin: 0 auto; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin: 20px 0; }
        .test-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: #f9f9f9; }
        .test-card h3 { margin-top: 0; color: #333; }
        .status { padding: 8px 12px; border-radius: 4px; font-weight: bold; margin: 5px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #b8daff; }
        .loading { background: #e2e3e5; color: #383d41; border: 1px solid #d6d8db; }
        .test-button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
        .test-button:disabled { background: #6c757d; cursor: not-allowed; }
        .code { font-family: monospace; background: #f8f9fa; padding: 2px 6px; border-radius: 3px; }
        .log { max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 12px; }
        .endpoint-fix { background: #fff; border: 1px solid #0066cc; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .endpoint-fix h4 { color: #0066cc; margin: 0 0 10px 0; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 PSR-4 Frontend Integration Test</h1>
        <p>Testing the fixed PSR-4 API endpoints in real user-facing pages</p>
        
        <!-- Endpoint Fixes Summary -->
        <div class="endpoint-fix">
            <h4>🎯 Endpoints Fixed:</h4>
            <ul>
                <li><strong>get_balance:</strong> <span class="code">GET /api/balance</span> → <span class="code">POST /api/balance</span></li>
                <li><strong>get_active_investments:</strong> <span class="code">/api/active-investments</span> → <span class="code">/api/investments/active</span></li>
                <li><strong>Payment endpoints:</strong> <span class="code">/api/confirm-payment</span> → <span class="code">/api/payment/confirm</span></li>
                <li><strong>Investment methods:</strong> <span class="code">/api/my-investments</span> → <span class="code">/api/investments/active</span></li>
            </ul>
        </div>

        <div class="test-grid">
            <!-- Test 1: Balance API -->
            <div class="test-card">
                <h3>🏦 Balance API Test</h3>
                <p>Testing the corrected balance endpoint</p>
                <button class="test-button" onclick="testBalanceAPI()">Test Balance API</button>
                <div id="balanceStatus" class="status loading" style="display: none;">Testing...</div>
                <div id="balanceLog" class="log" style="display: none;"></div>
            </div>

            <!-- Test 2: Active Investments API -->
            <div class="test-card">
                <h3>📈 Active Investments API Test</h3>
                <p>Testing the corrected active investments endpoint</p>
                <button class="test-button" onclick="testActiveInvestmentsAPI()">Test Active Investments API</button>
                <div id="investmentsStatus" class="status loading" style="display: none;">Testing...</div>
                <div id="investmentsLog" class="log" style="display: none;"></div>
            </div>

            <!-- Test 3: Active Plan Page -->
            <div class="test-card">
                <h3>📋 Active Plan Page Test</h3>
                <p>Testing the actual user page that was experiencing errors</p>
                <input type="number" id="investmentId" placeholder="Investment ID" value="1" style="margin: 5px; padding: 5px;">
                <button class="test-button" onclick="testActivePlanPage()">Test Active Plan Page</button>
                <div id="planPageStatus" class="status loading" style="display: none;">Testing...</div>
                <div id="planPageLog" class="log" style="display: none;"></div>
            </div>

            <!-- Test 4: Dashboard Balance Display -->
            <div class="test-card">
                <h3>📊 Dashboard Integration Test</h3>
                <p>Testing balance and investments on dashboard</p>
                <button class="test-button" onclick="testDashboardIntegration()">Test Dashboard APIs</button>
                <div id="dashboardStatus" class="status loading" style="display: none;">Testing...</div>
                <div id="dashboardLog" class="log" style="display: none;"></div>
            </div>

            <!-- Test 5: Investment Page -->
            <div class="test-card">
                <h3>💰 Investment Page Test</h3>
                <p>Testing invest.php and make_investment.php pages</p>
                <button class="test-button" onclick="testInvestmentPages()">Test Investment Pages</button>
                <div id="investPageStatus" class="status loading" style="display: none;">Testing...</div>
                <div id="investPageLog" class="log" style="display: none;"></div>
            </div>

            <!-- Test 6: Backend Route Verification -->
            <div class="test-card">
                <h3>🛤️ Backend Route Verification</h3>
                <p>Direct testing of backend API routes</p>
                <button class="test-button" onclick="testBackendRoutes()">Test Backend Routes</button>
                <div id="backendStatus" class="status loading" style="display: none;">Testing...</div>
                <div id="backendLog" class="log" style="display: none;"></div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button" onclick="runAllTests()" style="background: #28a745; font-size: 16px; padding: 15px 30px;">
                🚀 Run All Tests
            </button>
            <button class="test-button" onclick="clearResults()" style="background: #6c757d;">
                🧹 Clear Results
            </button>
        </div>

        <!-- Test Summary -->
        <div id="testSummary" style="display: none; margin-top: 30px; padding: 20px; border: 2px solid #28a745; border-radius: 8px; background: #f8fff9;">
            <h3>📋 Test Summary</h3>
            <div id="summaryContent"></div>
        </div>
    </div>

    <script>
        let testResults = {
            balance: null,
            activeInvestments: null,
            activePlanPage: null,
            dashboard: null,
            investmentPages: null,
            backendRoutes: null
        };

        // API call function that matches the frontend pattern
        async function apiCall(action, data = {}) {
            try {
                const response = await fetch('/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: action,
                        ...data
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                console.error('API call error:', error);
                return { success: false, error: error.message };
            }
        }

        function showStatus(testName, message, type) {
            const statusElement = document.getElementById(`${testName}Status`);
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.style.display = 'block';
        }

        function addLog(testName, message) {
            const logElement = document.getElementById(`${testName}Log`);
            logElement.style.display = 'block';
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearResults() {
            Object.keys(testResults).forEach(key => {
                testResults[key] = null;
                const statusElement = document.getElementById(`${key}Status`);
                const logElement = document.getElementById(`${key}Log`);
                if (statusElement) {
                    statusElement.style.display = 'none';
                }
                if (logElement) {
                    logElement.style.display = 'none';
                    logElement.innerHTML = '';
                }
            });
            document.getElementById('testSummary').style.display = 'none';
        }

        async function testBalanceAPI() {
            showStatus('balance', 'Testing balance API...', 'loading');
            addLog('balance', 'Testing POST /api/balance endpoint fix...');

            try {
                const result = await apiCall('get_balance');
                
                if (result.success) {
                    testResults.balance = true;
                    showStatus('balance', `✅ Balance API working! Balance: ${result.balance} USDT`, 'success');
                    addLog('balance', `✅ SUCCESS: Balance retrieved - ${result.balance} USDT`);
                } else {
                    testResults.balance = false;
                    showStatus('balance', `❌ Balance API failed: ${result.error || result.message}`, 'error');
                    addLog('balance', `❌ FAILED: ${result.error || result.message}`);
                }
            } catch (error) {
                testResults.balance = false;
                showStatus('balance', `❌ Error: ${error.message}`, 'error');
                addLog('balance', `❌ ERROR: ${error.message}`);
            }
        }

        async function testActiveInvestmentsAPI() {
            showStatus('investments', 'Testing active investments API...', 'loading');
            addLog('investments', 'Testing GET /api/investments/active endpoint fix...');

            try {
                const result = await apiCall('get_active_investments');
                
                if (result.success) {
                    testResults.activeInvestments = true;
                    const count = result.investments ? result.investments.length : 0;
                    showStatus('investments', `✅ Active Investments API working! Found ${count} investments`, 'success');
                    addLog('investments', `✅ SUCCESS: Found ${count} active investments`);
                    if (count > 0) {
                        addLog('investments', `First investment: ${JSON.stringify(result.investments[0])}`);
                    }
                } else {
                    testResults.activeInvestments = false;
                    showStatus('investments', `❌ Active Investments API failed: ${result.error || result.message}`, 'error');
                    addLog('investments', `❌ FAILED: ${result.error || result.message}`);
                }
            } catch (error) {
                testResults.activeInvestments = false;
                showStatus('investments', `❌ Error: ${error.message}`, 'error');
                addLog('investments', `❌ ERROR: ${error.message}`);
            }
        }

        async function testActivePlanPage() {
            const investmentId = document.getElementById('investmentId').value;
            showStatus('planPage', 'Testing active plan page...', 'loading');
            addLog('planPage', `Testing active_plan.php?id=${investmentId}...`);

            try {
                // Test page accessibility
                const pageResponse = await fetch(`/frontend/user/active_plan.php?id=${investmentId}`);
                
                if (pageResponse.ok) {
                    addLog('planPage', '✅ Active plan page accessible');
                    
                    // Test the APIs that the page uses
                    const detailsResult = await apiCall('get_investment_details', { investment_id: investmentId });
                    const earningsResult = await apiCall('get_investment_earnings', { investment_id: investmentId });
                    
                    if (detailsResult.success && earningsResult.success) {
                        testResults.activePlanPage = true;
                        showStatus('planPage', '✅ Active plan page and APIs working!', 'success');
                        addLog('planPage', '✅ SUCCESS: Investment details and earnings APIs working');
                    } else {
                        testResults.activePlanPage = false;
                        showStatus('planPage', '⚠️ Page loads but APIs failing', 'warning');
                        addLog('planPage', `⚠️ Details API: ${detailsResult.success ? 'OK' : 'FAILED'}`);
                        addLog('planPage', `⚠️ Earnings API: ${earningsResult.success ? 'OK' : 'FAILED'}`);
                    }
                } else {
                    testResults.activePlanPage = false;
                    showStatus('planPage', `❌ Page not accessible: HTTP ${pageResponse.status}`, 'error');
                    addLog('planPage', `❌ Page error: HTTP ${pageResponse.status}`);
                }
            } catch (error) {
                testResults.activePlanPage = false;
                showStatus('planPage', `❌ Error: ${error.message}`, 'error');
                addLog('planPage', `❌ ERROR: ${error.message}`);
            }
        }

        async function testDashboardIntegration() {
            showStatus('dashboard', 'Testing dashboard integration...', 'loading');
            addLog('dashboard', 'Testing dashboard APIs...');

            try {
                // Test the main APIs used by dashboard
                const balanceResult = await apiCall('get_balance');
                const investmentsResult = await apiCall('get_active_investments');
                const statsResult = await apiCall('get_investment_statistics');

                let successCount = 0;
                let totalTests = 3;

                if (balanceResult.success) {
                    successCount++;
                    addLog('dashboard', '✅ Dashboard balance API working');
                } else {
                    addLog('dashboard', `❌ Dashboard balance API failed: ${balanceResult.error}`);
                }

                if (investmentsResult.success) {
                    successCount++;
                    addLog('dashboard', '✅ Dashboard investments API working');
                } else {
                    addLog('dashboard', `❌ Dashboard investments API failed: ${investmentsResult.error}`);
                }

                if (statsResult.success) {
                    successCount++;
                    addLog('dashboard', '✅ Dashboard statistics API working');
                } else {
                    addLog('dashboard', `❌ Dashboard statistics API failed: ${statsResult.error}`);
                }

                if (successCount === totalTests) {
                    testResults.dashboard = true;
                    showStatus('dashboard', '✅ All dashboard APIs working!', 'success');
                } else {
                    testResults.dashboard = false;
                    showStatus('dashboard', `⚠️ ${successCount}/${totalTests} dashboard APIs working`, 'warning');
                }
            } catch (error) {
                testResults.dashboard = false;
                showStatus('dashboard', `❌ Error: ${error.message}`, 'error');
                addLog('dashboard', `❌ ERROR: ${error.message}`);
            }
        }

        async function testInvestmentPages() {
            showStatus('investPage', 'Testing investment pages...', 'loading');
            addLog('investPage', 'Testing invest.php and make_investment.php APIs...');

            try {
                // Test APIs used by investment pages
                const balanceResult = await apiCall('get_balance');
                const plansResult = await apiCall('get_investment_plans');
                const activeInvestmentsResult = await apiCall('get_active_investments');

                let successCount = 0;
                let totalTests = 3;

                if (balanceResult.success) {
                    successCount++;
                    addLog('investPage', '✅ Investment page balance API working');
                } else {
                    addLog('investPage', `❌ Investment page balance API failed: ${balanceResult.error}`);
                }

                if (plansResult.success || (plansResult.plans && plansResult.plans.length > 0)) {
                    successCount++;
                    addLog('investPage', '✅ Investment plans API working');
                } else {
                    addLog('investPage', `❌ Investment plans API failed: ${plansResult.error}`);
                }

                if (activeInvestmentsResult.success) {
                    successCount++;
                    addLog('investPage', '✅ Investment page active investments API working');
                } else {
                    addLog('investPage', `❌ Investment page active investments API failed: ${activeInvestmentsResult.error}`);
                }

                if (successCount === totalTests) {
                    testResults.investmentPages = true;
                    showStatus('investPage', '✅ All investment page APIs working!', 'success');
                } else {
                    testResults.investmentPages = false;
                    showStatus('investPage', `⚠️ ${successCount}/${totalTests} investment page APIs working`, 'warning');
                }
            } catch (error) {
                testResults.investmentPages = false;
                showStatus('investPage', `❌ Error: ${error.message}`, 'error');
                addLog('investPage', `❌ ERROR: ${error.message}`);
            }
        }

        async function testBackendRoutes() {
            showStatus('backend', 'Testing backend routes...', 'loading');
            addLog('backend', 'Testing direct backend route accessibility...');

            const routes = [
                { method: 'POST', path: '/api/balance', name: 'Balance Route' },
                { method: 'GET', path: '/api/investments/active', name: 'Active Investments Route' },
                { method: 'GET', path: '/api/investment-plans', name: 'Investment Plans Route' }
            ];

            let successCount = 0;

            for (const route of routes) {
                try {
                    const response = await fetch(`http://localhost:8000${route.path}`, {
                        method: route.method,
                        headers: { 'Content-Type': 'application/json' }
                    });

                    if (response.status === 200 || response.status === 401) {
                        successCount++;
                        addLog('backend', `✅ ${route.name}: Route exists (HTTP ${response.status})`);
                    } else if (response.status === 404) {
                        addLog('backend', `❌ ${route.name}: Route not found (HTTP 404)`);
                    } else {
                        addLog('backend', `⚠️ ${route.name}: Unexpected response (HTTP ${response.status})`);
                    }
                } catch (error) {
                    addLog('backend', `❌ ${route.name}: Connection failed - ${error.message}`);
                }
            }

            if (successCount === routes.length) {
                testResults.backendRoutes = true;
                showStatus('backend', '✅ All backend routes accessible!', 'success');
            } else {
                testResults.backendRoutes = false;
                showStatus('backend', `⚠️ ${successCount}/${routes.length} backend routes accessible`, 'warning');
            }
        }

        async function runAllTests() {
            clearResults();
            
            const tests = [
                testBalanceAPI,
                testActiveInvestmentsAPI,
                testActivePlanPage,
                testDashboardIntegration,
                testInvestmentPages,
                testBackendRoutes
            ];

            for (const test of tests) {
                await test();
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
            }

            showTestSummary();
        }

        function showTestSummary() {
            const summaryElement = document.getElementById('testSummary');
            const contentElement = document.getElementById('summaryContent');
            
            let passedTests = 0;
            let totalTests = 0;
            let content = '<ul>';

            Object.keys(testResults).forEach(key => {
                if (testResults[key] !== null) {
                    totalTests++;
                    if (testResults[key]) {
                        passedTests++;
                        content += `<li style="color: green;">✅ ${key}: PASSED</li>`;
                    } else {
                        content += `<li style="color: red;">❌ ${key}: FAILED</li>`;
                    }
                }
            });

            content += '</ul>';

            if (passedTests === totalTests && totalTests > 0) {
                content = `<div style="color: green; font-weight: bold; margin-bottom: 10px;">🎉 ALL TESTS PASSED! (${passedTests}/${totalTests})</div>` + content;
                content += '<p><strong>✅ PSR-4 endpoint fixes are working correctly!</strong></p>';
            } else {
                content = `<div style="color: orange; font-weight: bold; margin-bottom: 10px;">⚠️ ${passedTests}/${totalTests} tests passed</div>` + content;
                if (passedTests > 0) {
                    content += '<p>Some endpoints are fixed, but there may be remaining issues.</p>';
                }
            }

            contentElement.innerHTML = content;
            summaryElement.style.display = 'block';
        }

        // Auto-test on page load if user is authenticated
        window.addEventListener('load', function() {
            console.log('PSR-4 Frontend Integration Test loaded');
            
            // Test if user is authenticated by making a simple API call
            apiCall('get_balance').then(result => {
                if (result.error && result.error.includes('authenticated')) {
                    alert('Please login first to run the tests. Redirecting to login page...');
                    window.location.href = '/frontend/login.php';
                }
            }).catch(error => {
                console.log('Initial auth check failed:', error);
            });
        });
    </script>
</body>
</html>
