<?php
/**
 * <PERSON><PERSON> Runner for Payment Confirmation
 * This script should be executed by a cron job to automatically confirm payments
 * 
 * Usage:
 * php cron_runner.php [--hours=1] [--dry-run] [--verbose]
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load configuration and services
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/TronGridService.php';
require_once __DIR__ . '/PaymentConfirmationService.php';

/**
 * Parse command line arguments
 */
function parseArguments($argv) {
    $options = [
        'hours' => 1,
        'dry_run' => false,
        'verbose' => false,
        'help' => false
    ];
    
    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];
        
        if ($arg === '--help' || $arg === '-h') {
            $options['help'] = true;
        } elseif (strpos($arg, '--hours=') === 0) {
            $options['hours'] = (int)substr($arg, 8);
        } elseif ($arg === '--dry-run') {
            $options['dry_run'] = true;
        } elseif ($arg === '--verbose' || $arg === '-v') {
            $options['verbose'] = true;
        }
    }
    
    return $options;
}

/**
 * Display help information
 */
function showHelp() {
    echo "Payment Confirmation Cron Job Runner\n";
    echo "Usage: php cron_runner.php [options]\n\n";
    echo "Options:\n";
    echo "  --hours=N     Scan for transactions from the last N hours (default: 1)\n";
    echo "  --dry-run     Show what would be done without actually processing\n";
    echo "  --verbose     Enable verbose output\n";
    echo "  --help, -h    Show this help message\n\n";
    echo "Examples:\n";
    echo "  php cron_runner.php --hours=2 --verbose\n";
    echo "  php cron_runner.php --dry-run\n";
    echo "  php cron_runner.php --hours=6\n\n";
}

/**
 * Log message with timestamp
 */
function logMessage($message, $verbose = false, $isVerbose = false) {
    if (!$isVerbose || $verbose) {
        echo "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
    }
}

/**
 * Main execution
 */
try {
    $options = parseArguments($argv);
    
    if ($options['help']) {
        showHelp();
        exit(0);
    }
    
    $verbose = $options['verbose'];
    $dryRun = $options['dry_run'];
    $hours = max(1, $options['hours']);
    
    logMessage("Starting payment confirmation cron job", $verbose);
    logMessage("Scan period: {$hours} hour(s)", $verbose);
    
    if ($dryRun) {
        logMessage("DRY RUN MODE - No actual changes will be made", $verbose);
    }
    
    // Initialize services
    $paymentService = new PaymentConfirmationService();
    $tronGridService = new TronGridService();
    
    // Health check
    logMessage("Performing TronGrid health check...", $verbose, true);
    $healthCheck = $tronGridService->healthCheck();
    
    if (!$healthCheck['api_accessible']) {
        logMessage("ERROR: TronGrid API is not accessible: " . $healthCheck['error'], $verbose);
        exit(1);
    }
    
    logMessage("TronGrid API is healthy", $verbose, true);
    
    if ($dryRun) {
        logMessage("Would scan for deposits in the last {$hours} hour(s)", $verbose);
        logMessage("DRY RUN complete - no changes made", $verbose);
        exit(0);
    }
    
    // Auto-confirm recent deposits
    logMessage("Scanning for recent deposits...", $verbose);
    $result = $paymentService->autoConfirmRecentDeposits($hours);
    
    if ($result['success']) {
        logMessage("Scan completed successfully", $verbose);
        logMessage("Processed wallets: " . $result['processed_wallets'], $verbose);
        logMessage("Confirmed deposits: " . $result['confirmed_count'], $verbose);
        
        if (!empty($result['errors'])) {
            logMessage("Errors encountered: " . count($result['errors']), $verbose);
            
            if ($verbose) {
                foreach ($result['errors'] as $error) {
                    logMessage("  Error for wallet {$error['wallet_id']}: {$error['error']}", $verbose, true);
                }
            }
        }
        
        // Log success to file
        $logEntry = [
            'timestamp' => date('c'),
            'scan_hours' => $hours,
            'processed_wallets' => $result['processed_wallets'],
            'confirmed_count' => $result['confirmed_count'],
            'errors_count' => count($result['errors']),
            'status' => 'success'
        ];
        
        file_put_contents(
            __DIR__ . '/logs/cron_' . date('Y-m-d') . '.log',
            json_encode($logEntry) . "\n",
            FILE_APPEND | LOCK_EX
        );
        
        logMessage("Cron job completed successfully", $verbose);
        exit(0);
        
    } else {
        logMessage("ERROR: " . $result['message'], $verbose);
        
        // Log error to file
        $logEntry = [
            'timestamp' => date('c'),
            'scan_hours' => $hours,
            'status' => 'error',
            'message' => $result['message']
        ];
        
        file_put_contents(
            __DIR__ . '/logs/cron_' . date('Y-m-d') . '.log',
            json_encode($logEntry) . "\n",
            FILE_APPEND | LOCK_EX
        );
        
        exit(1);
    }
    
} catch (Exception $e) {
    logMessage("FATAL ERROR: " . $e->getMessage(), true);
    
    // Log fatal error
    $logEntry = [
        'timestamp' => date('c'),
        'status' => 'fatal_error',
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ];
    
    file_put_contents(
        __DIR__ . '/logs/cron_' . date('Y-m-d') . '.log',
        json_encode($logEntry) . "\n",
        FILE_APPEND | LOCK_EX
    );
    
    exit(1);
}
