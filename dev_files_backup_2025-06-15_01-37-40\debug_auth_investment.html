<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication & Investment Debug</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .btn { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 14px;
            background: #007bff;
            color: white;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        input, select { 
            padding: 8px; 
            margin: 5px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Authentication & Investment Flow Debug</h1>
        <p>Complete test to resolve the "User not authenticated" issue</p>

        <div class="test-section">
            <h3>Step 1: Login and Establish Session</h3>
            <div>
                <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
                <input type="password" id="loginPassword" placeholder="Password" value="password123">
                <button class="btn" onclick="performLogin()">Login</button>
            </div>
            <div id="loginResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 2: Verify Authentication State</h3>
            <button class="btn" onclick="checkAuthState()">Check Authentication</button>
            <div id="authResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 3: Test Balance API (Authenticated Endpoint)</h3>
            <button class="btn" onclick="testBalanceAPI()">Test Get Balance</button>
            <div id="balanceResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 4: Test Investment Creation</h3>
            <div>
                <input type="number" id="investAmount" placeholder="Amount" value="700" min="600">
                <select id="investPlan">
                    <option value="basic">Basic Plan</option>
                </select>
                <button class="btn" onclick="testInvestmentCreation()">Create Investment</button>
            </div>
            <div id="investmentResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 5: Debug Session Information</h3>
            <button class="btn" onclick="getSessionDebug()">Get Session Debug Info</button>
            <div id="sessionResult"></div>
        </div>

        <div class="test-section">
            <h3>Test Results Summary</h3>
            <div id="summaryResult"></div>
        </div>
    </div>

    <script>
        let testResults = {
            login: false,
            auth: false,
            balance: false,
            investment: false
        };

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function updateSummary() {
            const summary = Object.entries(testResults)
                .map(([test, passed]) => `${test}: ${passed ? '✅' : '❌'}`)
                .join('<br>');
            showResult('summaryResult', summary, 'info');
        }

        async function performLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                showResult('loginResult', 'Attempting login...', 'info');
                
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    testResults.login = true;
                    showResult('loginResult', 
                        `✅ Login successful!
                        User ID: ${result.user?.id}
                        Email: ${result.user?.email}
                        Token: ${result.token ? 'Present' : 'Missing'}`, 
                        'success'
                    );
                } else {
                    testResults.login = false;
                    showResult('loginResult', `❌ Login failed: ${result.error}`, 'error');
                }
            } catch (error) {
                testResults.login = false;
                showResult('loginResult', `❌ Login error: ${error.message}`, 'error');
            }
            
            updateSummary();
        }

        async function checkAuthState() {
            try {
                showResult('authResult', 'Checking authentication state...', 'info');
                
                const response = await fetch('/debug_session.php');
                const text = await response.text();
                
                testResults.auth = text.includes('✅ AJAX should pass authentication checks');
                
                showResult('authResult', 
                    `Authentication State:
                    <pre>${text}</pre>`, 
                    testResults.auth ? 'success' : 'warning'
                );
            } catch (error) {
                testResults.auth = false;
                showResult('authResult', `❌ Error checking auth: ${error.message}`, 'error');
            }
            
            updateSummary();
        }

        async function testBalanceAPI() {
            try {
                showResult('balanceResult', 'Testing balance API...', 'info');
                
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_balance'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    testResults.balance = true;
                    showResult('balanceResult', 
                        `✅ Balance API successful!
                        Balance: ${result.balance} USDT`, 
                        'success'
                    );
                } else if (result.error && result.error.includes('authenticated')) {
                    testResults.balance = false;
                    showResult('balanceResult', `❌ Balance API failed: ${result.error}`, 'error');
                } else {
                    testResults.balance = false;
                    showResult('balanceResult', `❌ Balance API error: ${JSON.stringify(result)}`, 'error');
                }
            } catch (error) {
                testResults.balance = false;
                showResult('balanceResult', `❌ Balance API error: ${error.message}`, 'error');
            }
            
            updateSummary();
        }

        async function testInvestmentCreation() {
            const amount = document.getElementById('investAmount').value;
            const plan = document.getElementById('investPlan').value;

            try {
                showResult('investmentResult', 'Testing investment creation...', 'info');
                
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'create_investment',
                        amount: parseFloat(amount),
                        plan: plan
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    testResults.investment = true;
                    showResult('investmentResult', 
                        `✅ Investment creation successful!
                        Investment ID: ${result.investment_id || 'N/A'}
                        Message: ${result.message}`, 
                        'success'
                    );
                } else if (result.error && result.error.includes('authenticated')) {
                    testResults.investment = false;
                    showResult('investmentResult', 
                        `❌ Investment creation failed: ${result.error}
                        
                        This is the exact error users are experiencing!`, 
                        'error'
                    );
                } else {
                    testResults.investment = false;
                    showResult('investmentResult', `❌ Investment error: ${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                testResults.investment = false;
                showResult('investmentResult', `❌ Investment error: ${error.message}`, 'error');
            }
            
            updateSummary();
        }

        async function getSessionDebug() {
            try {
                showResult('sessionResult', 'Getting session debug info...', 'info');
                
                const response = await fetch('/test_auth_flow.php');
                const text = await response.text();
                
                showResult('sessionResult', 
                    `Session Debug Information:
                    <pre>${text}</pre>`, 
                    'info'
                );
            } catch (error) {
                showResult('sessionResult', `❌ Session debug error: ${error.message}`, 'error');
            }
        }

        // Initialize
        updateSummary();
        console.log('Authentication & Investment Debug Tool loaded');
        console.log('Use the buttons above to test each step in order');
    </script>
</body>
</html>
