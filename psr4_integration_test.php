<?php
/**
 * PSR-4 Frontend Integration Test
 * Tests all frontend functionality with PSR-4 autoloading
 */

// Simulate web environment
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['REQUEST_URI'] = '/';

// Include PSR-4 autoloader
require_once __DIR__ . '/backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;
use Simbi\Tls\Frontend\Services\ApiService;
use Simbi\Tls\Frontend\Utils\ValidationUtils;
use Simbi\Tls\Frontend\Utils\FormatUtils;

echo "=== PSR-4 Frontend Integration Test ===\n\n";

// Test 1: Configuration
echo "1. Testing Configuration:\n";
try {
    FrontendConfig::init();
    $apiUrl = FrontendConfig::get('API_BASE_URL');
    echo "   ✅ Config loaded, API URL: $apiUrl\n";
} catch (Exception $e) {
    echo "   ❌ Config error: " . $e->getMessage() . "\n";
}

// Test 2: Validation Utils
echo "\n2. Testing Validation Utils:\n";
try {
    $isValid = ValidationUtils::isValidEmail('<EMAIL>');
    echo "   ✅ Email validation: " . ($isValid ? 'valid' : 'invalid') . "\n";
    
    $sanitized = ValidationUtils::sanitizeInput('<script>alert("test")</script>');
    echo "   ✅ Input sanitization: $sanitized\n";
} catch (Exception $e) {
    echo "   ❌ Validation error: " . $e->getMessage() . "\n";
}

// Test 3: Format Utils
echo "\n3. Testing Format Utils:\n";
try {
    $currency = FormatUtils::formatCurrency(1234.56);
    echo "   ✅ Currency formatting: $currency\n";
    
    $date = FormatUtils::formatDate('2025-06-15');
    echo "   ✅ Date formatting: $date\n";
} catch (Exception $e) {
    echo "   ❌ Format error: " . $e->getMessage() . "\n";
}

// Test 4: API Service
echo "\n4. Testing API Service:\n";
try {
    $api = new ApiService();
    echo "   ✅ ApiService instantiated successfully\n";
    
    // Test method exists
    if (method_exists($api, 'getInvestmentPlans')) {
        echo "   ✅ getInvestmentPlans method exists\n";
    }
    
    if (method_exists($api, 'getTransactions')) {
        echo "   ✅ getTransactions method exists\n";
    }
} catch (Exception $e) {
    echo "   ❌ API Service error: " . $e->getMessage() . "\n";
}

// Test 5: Session Service (without actual session start)
echo "\n5. Testing Session Service:\n";
try {
    if (class_exists('Simbi\Tls\Frontend\Services\SessionService')) {
        echo "   ✅ SessionService class exists\n";
    }
    
    if (method_exists('Simbi\Tls\Frontend\Services\SessionService', 'init')) {
        echo "   ✅ SessionService::init() method exists\n";
    }
} catch (Exception $e) {
    echo "   ❌ Session Service error: " . $e->getMessage() . "\n";
}

// Test 6: Legacy Compatibility
echo "\n6. Testing Legacy Compatibility:\n";
try {
    // Test that old config.php still works
    ob_start();
    include __DIR__ . '/frontend/config.php';
    ob_end_clean();
    
    if (class_exists('FrontendConfig')) {
        echo "   ✅ Legacy FrontendConfig class exists\n";
        
        if (method_exists('FrontendConfig', 'getApiUrl')) {
            $apiUrl = FrontendConfig::getApiUrl();
            echo "   ✅ Legacy getApiUrl() works: $apiUrl\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Legacy compatibility error: " . $e->getMessage() . "\n";
}

// Test 7: API Wrapper Compatibility
echo "\n7. Testing API Wrapper Compatibility:\n";
try {
    ob_start();
    include __DIR__ . '/frontend/api.php';
    ob_end_clean();
    
    if (class_exists('APIWrapper')) {
        $api = new APIWrapper();
        echo "   ✅ Legacy APIWrapper works\n";
        
        if (method_exists($api, 'setToken')) {
            echo "   ✅ setToken method exists\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ API Wrapper error: " . $e->getMessage() . "\n";
}

// Test 8: Directory Structure
echo "\n8. Testing Directory Structure:\n";
$directories = [
    'frontend/src/Config' => 'Frontend Config',
    'frontend/src/Services' => 'Frontend Services', 
    'frontend/src/Utils' => 'Frontend Utils',
    'cronJob/src/Config' => 'CronJob Config',
    'cronJob/src/Services' => 'CronJob Services'
];

foreach ($directories as $dir => $desc) {
    if (is_dir(__DIR__ . '/' . $dir)) {
        echo "   ✅ $desc directory exists\n";
    } else {
        echo "   ❌ $desc directory missing\n";
    }
}

// Test 9: File Existence
echo "\n9. Testing Key Files:\n";
$files = [
    'frontend/src/Config/FrontendConfig.php' => 'PSR-4 Frontend Config',
    'frontend/src/Services/ApiService.php' => 'PSR-4 API Service',
    'frontend/src/Services/SessionService.php' => 'PSR-4 Session Service',
    'frontend/src/Utils/ValidationUtils.php' => 'PSR-4 Validation Utils',
    'frontend/src/Utils/FormatUtils.php' => 'PSR-4 Format Utils',
    'cronJob/src/CronRunner.php' => 'PSR-4 Cron Runner'
];

foreach ($files as $file => $desc) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "   ✅ $desc exists\n";
    } else {
        echo "   ❌ $desc missing\n";
    }
}

echo "\n=== Integration Test Complete ===\n";
echo "🎉 PSR-4 conversion verification finished!\n";
