<?php

declare(strict_types=1);

namespace Simbi\Tls\Frontend\Config;

use PDO;
use PDOException;

/**
 * Frontend Configuration Class
 * 
 * Configuration settings for the TLS Crypto Wallet Frontend
 * Handles session management, API configuration, and application settings
 */
class FrontendConfig
{
    private static array $config = [];
    private static bool $loaded = false;
    private static ?array $currentUser = null;
    
    // Default Configuration Values
    private const DEFAULTS = [
        'API_BASE_URL' => 'http://localhost:8000',
        'API_TIMEOUT' => 30,
        'SESSION_LIFETIME' => 3600,
        'SESSION_NAME' => 'tls_session',
        'ENABLE_HTTPS_ONLY' => false,
        'SECURE_COOKIES' => false,
        'APP_NAME' => 'TLS Crypto Wallet',
        'APP_VERSION' => '1.0.0',
        'DEFAULT_CURRENCY' => 'TRX',
        'DEFAULT_PAGE_SIZE' => 20,
        'MAX_PAGE_SIZE' => 100,
        'MAX_UPLOAD_SIZE' => 5242880, // 5MB
        'ALLOWED_UPLOAD_TYPES' => ['jpg', 'jpeg', 'png', 'gif']
    ];
      /**
     * Initialize configuration (alias for load)
     */
    public static function init(): void
    {
        self::load();
    }
    
    /**
     * Load configuration
     */
    public static function load(): void
    {
        if (self::$loaded) {
            return;
        }
        
        // Load environment variables
        self::loadEnvironment();
        
        // Set default values
        foreach (self::DEFAULTS as $key => $value) {
            if (!isset(self::$config[$key])) {
                self::$config[$key] = $value;
            }
        }
        
        self::$loaded = true;
    }
    
    /**
     * Get configuration value
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        self::load();
        return self::$config[$key] ?? $default;
    }
    
    /**
     * Set configuration value
     */
    public static function set(string $key, mixed $value): void
    {
        self::$config[$key] = $value;
    }
    
    /**
     * Initialize session management
     */
    public static function initSession(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            // Configure session parameters
            ini_set('session.cookie_lifetime', (string)self::get('SESSION_LIFETIME'));
            ini_set('session.cookie_httponly', '1');
            ini_set('session.use_strict_mode', '1');
            ini_set('session.cookie_samesite', 'Strict');
            
            if (self::get('SECURE_COOKIES')) {
                ini_set('session.cookie_secure', '1');
            }
            
            session_name(self::get('SESSION_NAME'));
            session_start();
            
            // Regenerate session ID periodically for security
            if (!isset($_SESSION['created'])) {
                $_SESSION['created'] = time();
            } elseif (time() - $_SESSION['created'] > 1800) { // 30 minutes
                session_regenerate_id(true);
                $_SESSION['created'] = time();
            }
        }
    }
    
    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated(): bool
    {
        self::initSession();
        
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_email'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity'])) {
            $inactive = time() - $_SESSION['last_activity'];
            if ($inactive >= self::get('SESSION_LIFETIME')) {
                self::logout();
                return false;
            }
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * Login user
     */
    public static function login(array $userData): void
    {
        self::initSession();
        
        $_SESSION['user_id'] = $userData['id'];
        $_SESSION['user_email'] = $userData['email'];
        $_SESSION['user_name'] = $userData['name'] ?? '';
        $_SESSION['user_role'] = $userData['role'] ?? 'user';
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        // Regenerate session ID for security
        session_regenerate_id(true);
        
        self::$currentUser = $userData;
    }
    
    /**
     * Logout user
     */
    public static function logout(): void
    {
        self::initSession();
        
        // Clear session data
        $_SESSION = [];
        
        // Destroy session cookie
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params['path'],
                $params['domain'],
                $params['secure'],
                $params['httponly']
            );
        }
        
        // Destroy session
        session_destroy();
        
        self::$currentUser = null;
    }
    
    /**
     * Get current user data
     */
    public static function getCurrentUser(): ?array
    {
        if (!self::isAuthenticated()) {
            return null;
        }
        
        if (self::$currentUser === null) {
            self::$currentUser = [
                'id' => $_SESSION['user_id'],
                'email' => $_SESSION['user_email'],
                'name' => $_SESSION['user_name'] ?? '',
                'role' => $_SESSION['user_role'] ?? 'user'
            ];
        }
        
        return self::$currentUser;
    }
    
    /**
     * Get current user ID
     */
    public static function getCurrentUserId(): ?int
    {
        $user = self::getCurrentUser();
        return $user ? (int)$user['id'] : null;
    }
    
    /**
     * Check if current user has admin role
     */
    public static function isAdmin(): bool
    {
        $user = self::getCurrentUser();
        return $user && ($user['role'] === 'admin' || $user['role'] === 'superadmin');
    }
    
    /**
     * Require authentication (redirect if not logged in)
     */
    public static function requireAuth(?string $redirectUrl = null): void
    {
        if (!self::isAuthenticated()) {
            $redirectUrl = $redirectUrl ?? 'index.php';
            header("Location: {$redirectUrl}");
            exit;
        }
    }
    
    /**
     * Require admin access
     */
    public static function requireAdmin(?string $redirectUrl = null): void
    {
        self::requireAuth($redirectUrl);
        
        if (!self::isAdmin()) {
            $redirectUrl = $redirectUrl ?? 'dashboard.php';
            header("Location: {$redirectUrl}");
            exit;
        }
    }
    
    /**
     * Make API request to backend
     */
    public static function apiRequest(string $endpoint, array $data = [], string $method = 'POST'): array
    {
        $url = self::get('API_BASE_URL') . '/' . ltrim($endpoint, '/');
        
        $options = [
            'http' => [
                'method' => $method,
                'header' => [
                    'Content-Type: application/json',
                    'Accept: application/json'
                ],
                'content' => json_encode($data),
                'timeout' => self::get('API_TIMEOUT')
            ]
        ];
        
        // Add authentication if user is logged in
        if (self::isAuthenticated()) {
            $options['http']['header'][] = 'Authorization: Bearer ' . self::generateApiToken();
        }
        
        $context = stream_context_create($options);
        
        try {
            $response = file_get_contents($url, false, $context);
            
            if ($response === false) {
                return [
                    'success' => false,
                    'error' => 'API request failed'
                ];
            }
            
            $data = json_decode($response, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                return [
                    'success' => false,
                    'error' => 'Invalid JSON response'
                ];
            }
            
            return $data;
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate API token for authenticated requests
     */
    private static function generateApiToken(): string
    {
        $user = self::getCurrentUser();
        if (!$user) {
            return '';
        }
        
        // Simple token generation - in production, use JWT or similar
        return base64_encode(json_encode([
            'user_id' => $user['id'],
            'email' => $user['email'],
            'timestamp' => time()
        ]));
    }
    
    /**
     * Sanitize input data
     */
    public static function sanitizeInput(mixed $input): mixed
    {
        if (is_string($input)) {
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        } elseif (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        return $input;
    }
    
    /**
     * Validate email format
     */
    public static function isValidEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Generate CSRF token
     */
    public static function generateCsrfToken(): string
    {
        self::initSession();
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Validate CSRF token
     */
    public static function validateCsrfToken(string $token): bool
    {
        self::initSession();
        
        return isset($_SESSION['csrf_token']) && 
               hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Format currency amount
     */
    public static function formatCurrency(float $amount, string $currency = null): string
    {
        $currency = $currency ?? self::get('DEFAULT_CURRENCY');
        
        switch ($currency) {
            case 'TRX':
            case 'USDT':
                return number_format($amount, 2, '.', ',') . ' ' . $currency;
            default:
                return number_format($amount, 2, '.', ',');
        }
    }
    
    /**
     * Format date
     */
    public static function formatDate(string $date, string $format = 'Y-m-d H:i:s'): string
    {
        return date($format, strtotime($date));
    }
    
    /**
     * Load environment variables
     */
    private static function loadEnvironment(): void
    {
        // Load from $_ENV and $_SERVER
        foreach ($_ENV as $key => $value) {
            self::$config[$key] = $value;
        }
        
        foreach ($_SERVER as $key => $value) {
            if (strpos($key, 'TLS_') === 0) {
                self::$config[substr($key, 4)] = $value;
            }
        }
        
        // Load from .env file if available
        $envFile = __DIR__ . '/../../../backend/.env';
        if (file_exists($envFile)) {
            self::loadEnvFile($envFile);
        }
    }
    
    /**
     * Load .env file
     */
    private static function loadEnvFile(string $filePath): void
    {
        $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
              if (strpos($line, '=') !== false) {
                [$key, $value] = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if value is not empty
                if (!empty($value) && strlen($value) > 1) {
                    if (($value[0] === '"' && $value[-1] === '"') ||
                        ($value[0] === "'" && $value[-1] === "'")) {
                        $value = substr($value, 1, -1);
                    }
                }
                
                self::$config[$key] = $value;
            }
        }
    }
    
    /**
     * Get application information
     */
    public static function getAppInfo(): array
    {
        return [
            'name' => self::get('APP_NAME'),
            'version' => self::get('APP_VERSION'),
            'environment' => self::get('ENVIRONMENT', 'development'),
            'php_version' => PHP_VERSION,
            'session_name' => self::get('SESSION_NAME')
        ];
    }
}
