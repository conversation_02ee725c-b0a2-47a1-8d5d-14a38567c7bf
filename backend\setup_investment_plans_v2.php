<?php
require_once 'vendor/autoload.php';

use Simbi\Tls\Config\Database;

try {
    $pdo = Database::getConnection();
    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }
    
    echo "Database connection: SUCCESS\n";
    
    // Read the schema SQL
    $sql = file_get_contents('investment_plans_schema.sql');
    if (!$sql) {
        throw new Exception("Failed to read investment_plans_schema.sql");
    }
    
    echo "SQL file size: " . strlen($sql) . " bytes\n";
    
    // First, create the table
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS investment_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        plan_code VARCHAR(50) NOT NULL UNIQUE,
        plan_name VARCHAR(100) NOT NULL,
        daily_rate DECIMAL(5, 4) NOT NULL,
        duration INT NOT NULL DEFAULT 30,
        min_amount DECIMAL(20, 8) NOT NULL,
        max_amount DECIMAL(20, 8) NULL,
        description TEXT NULL,
        features JSON NULL,
        is_active BOOLEAN DEFAULT TRUE,
        is_featured BOOLEAN DEFAULT FALSE,
        display_order INT DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_plan_code (plan_code),
        INDEX idx_is_active (is_active),
        INDEX idx_display_order (display_order)
    ) ENGINE=InnoDB COMMENT='Investment plan configurations'";
    
    echo "Creating investment_plans table...\n";
    $result = $pdo->exec($createTableSQL);
    echo "✓ Table creation result: " . ($result !== false ? "SUCCESS" : "ALREADY EXISTS") . "\n";
    
    // Now insert the data
    $insertSQL = "
    INSERT IGNORE INTO investment_plans (plan_code, plan_name, daily_rate, duration, min_amount, max_amount, description, features, is_active, is_featured, display_order) VALUES
    ('basic', 'Basic Plan', 0.0500, 30, 600.00000000, NULL, 'Perfect for beginners with steady daily returns', 
     JSON_ARRAY('Daily returns', '30-day duration', 'Low risk', 'Beginner friendly'), TRUE, TRUE, 1),
    ('premium', 'Premium Plan', 0.0700, 30, 2000.00000000, NULL, 'Enhanced returns for experienced investors', 
     JSON_ARRAY('Higher daily returns', '30-day duration', 'Medium risk', 'Advanced features'), FALSE, FALSE, 2),
    ('vip', 'VIP Plan', 0.1000, 30, 5000.00000000, NULL, 'Maximum returns for high-value investors', 
     JSON_ARRAY('Highest daily returns', '30-day duration', 'Premium service', 'Maximum profits'), FALSE, FALSE, 3)";
    
    echo "Inserting default investment plans...\n";
    $result = $pdo->exec($insertSQL);
    echo "✓ Data insertion result: " . ($result !== false ? $result . " rows affected" : "ERROR") . "\n";
    
    // Verify the table was created and data inserted
    echo "\nVerifying installation...\n";
    $stmt = $pdo->query("SELECT plan_code, plan_name, daily_rate, is_active FROM investment_plans ORDER BY display_order");
    $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nInvestment Plans in Database (" . count($plans) . " plans):\n";
    echo "-----------------------------\n";
    foreach ($plans as $plan) {
        $status = $plan['is_active'] ? 'ACTIVE' : 'DISABLED';
        $rate = ($plan['daily_rate'] * 100) . '%';
        echo "• {$plan['plan_name']} ({$plan['plan_code']}) - {$rate} daily - {$status}\n";
    }
    
    echo "\n✅ Investment plans database setup complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
