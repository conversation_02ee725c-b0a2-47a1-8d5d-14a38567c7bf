<?php
/**
 * Test script to verify the PSR-4 endpoint fixes
 * This tests the corrected get_balance and get_active_investments endpoints
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session to simulate authenticated user
session_start();

// Simulate authentication for testing (replace with actual login logic)
if (!isset($_SESSION['user_id'])) {
    echo "<h1>🔑 Authentication Test</h1>";
    echo "<p>No session found. Please <a href='frontend/login.php'>login first</a> or testing with mock user.</p>";
    
    // For testing, you can uncomment these lines to simulate a logged-in user:
    // $_SESSION['user_id'] = 1;
    // $_SESSION['token'] = 'test-token-for-endpoint-testing';
    // echo "<p>✅ Mock authentication set for testing.</p>";
    
    exit;
}

require_once 'frontend/api_psr4.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>PSR-4 Endpoint Fix Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
    .info { background-color: #d1ecf1; border-color: #b8daff; color: #0c5460; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    .endpoint { font-family: monospace; font-weight: bold; color: #0066cc; }
</style></head><body>";

echo "<h1>🔧 PSR-4 Endpoint Fix Verification</h1>";
echo "<p>Testing the corrected endpoints for <code>get_balance</code> and <code>get_active_investments</code></p>";

try {
    $api = new FrontendApi();
    
    // Test 1: Balance Endpoint Fix
    echo "<div class='test-section'>";
    echo "<h2>Test 1: Balance Endpoint</h2>";
    echo "<p><strong>Fix Applied:</strong> Changed <span class='endpoint'>GET /api/balance</span> → <span class='endpoint'>POST /api/balance</span></p>";
    
    $balanceResult = $api->getBalance();
    
    if (isset($balanceResult['success']) && $balanceResult['success']) {
        echo "<div class='success'>";
        echo "<p>✅ <strong>Balance Endpoint: FIXED & WORKING</strong></p>";
        echo "<p>Balance: " . ($balanceResult['balance'] ?? '0') . " USDT</p>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Balance Endpoint: STILL FAILING</strong></p>";
        echo "<p>Error: " . ($balanceResult['error'] ?? $balanceResult['message'] ?? 'Unknown error') . "</p>";
        echo "</div>";
    }
    
    echo "<details><summary>Raw Response</summary><pre>" . json_encode($balanceResult, JSON_PRETTY_PRINT) . "</pre></details>";
    echo "</div>";
    
    // Test 2: Active Investments Endpoint Fix
    echo "<div class='test-section'>";
    echo "<h2>Test 2: Active Investments Endpoint</h2>";
    echo "<p><strong>Fix Applied:</strong> Changed <span class='endpoint'>/api/active-investments</span> → <span class='endpoint'>/api/investments/active</span></p>";
    
    $investmentsResult = $api->getActiveInvestments();
    
    if (isset($investmentsResult['success']) && $investmentsResult['success']) {
        echo "<div class='success'>";
        echo "<p>✅ <strong>Active Investments Endpoint: FIXED & WORKING</strong></p>";
        $count = count($investmentsResult['investments'] ?? []);
        echo "<p>Found {$count} active investment(s)</p>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<p>❌ <strong>Active Investments Endpoint: STILL FAILING</strong></p>";
        echo "<p>Error: " . ($investmentsResult['error'] ?? $investmentsResult['message'] ?? 'Unknown error') . "</p>";
        echo "</div>";
    }
    
    echo "<details><summary>Raw Response</summary><pre>" . json_encode($investmentsResult, JSON_PRETTY_PRINT) . "</pre></details>";
    echo "</div>";
    
    // Test 3: Additional Compatibility Tests
    echo "<div class='test-section'>";
    echo "<h2>Test 3: Additional Fixed Endpoints</h2>";
    
    // Test payment endpoints (if they're used)
    echo "<div class='info'>";
    echo "<p><strong>Other Fixed Endpoints:</strong></p>";
    echo "<ul>";
    echo "<li><span class='endpoint'>/api/confirm-payment</span> → <span class='endpoint'>/api/payment/confirm</span></li>";
    echo "<li><span class='endpoint'>/api/payment-status</span> → <span class='endpoint'>/api/payment/status</span></li>";
    echo "<li><span class='endpoint'>/api/my-investments</span> → <span class='endpoint'>/api/investments/active</span></li>";
    echo "</ul>";
    echo "<p><em>These endpoints are now correctly mapped to backend routes.</em></p>";
    echo "</div>";
    echo "</div>";
    
    // Test 4: Backend Route Verification
    echo "<div class='test-section'>";
    echo "<h2>Test 4: Backend Route Verification</h2>";
    echo "<p>Checking if backend routes are accessible...</p>";
    
    $backendUrl = 'http://localhost:8000';
    $testRoutes = [
        'POST /api/balance' => 'Balance route',
        'GET /api/investments/active' => 'Active investments route',
        'GET /api/investment-plans' => 'Investment plans route'
    ];
    
    foreach ($testRoutes as $route => $description) {
        list($method, $path) = explode(' ', $route, 2);
        $url = $backendUrl . $path;
        
        echo "<p><strong>{$description}:</strong> ";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
        }
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200 || $httpCode === 401) { // 401 is OK (means route exists but needs auth)
            echo "<span style='color: green;'>✅ Route exists (HTTP {$httpCode})</span>";
        } else if ($httpCode === 404) {
            echo "<span style='color: red;'>❌ Route not found (HTTP 404)</span>";
        } else {
            echo "<span style='color: orange;'>⚠️ Unexpected response (HTTP {$httpCode})</span>";
        }
        echo "</p>";
    }
    echo "</div>";
    
    // Summary
    echo "<div class='test-section info'>";
    echo "<h2>📋 Fix Summary</h2>";
    echo "<p><strong>Endpoints Fixed:</strong></p>";
    echo "<ol>";
    echo "<li><strong>get_balance:</strong> Method mismatch fixed (GET → POST)</li>";
    echo "<li><strong>get_active_investments:</strong> URL mismatch fixed (/api/active-investments → /api/investments/active)</li>";
    echo "<li><strong>Payment endpoints:</strong> URL structure fixed to match backend routes</li>";
    echo "<li><strong>getInvestments():</strong> Corrected to use existing backend route</li>";
    echo "</ol>";
    echo "<p><em>These fixes should resolve the \"Endpoint not found\" errors in the PSR-4 frontend.</em></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ Test Error</h2>";
    echo "<p>Error running tests: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>Test the actual pages that were experiencing errors</li>";
echo "<li>Verify that <code>active_plan.php</code> now loads investment data correctly</li>";
echo "<li>Check that balance displays properly on investment pages</li>";
echo "</ul>";

echo "</body></html>";
?>
