<?php
require_once 'vendor/autoload.php';

use Simbi\Tls\Config\Database;

try {
    $pdo = Database::getConnection();
    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }
    
    echo "Database connection: SUCCESS\n";
    
    // Read and execute the schema SQL
    $sql = file_get_contents('investment_plans_schema.sql');
    if (!$sql) {
        throw new Exception("Failed to read investment_plans_schema.sql");
    }
    
    echo "Executing investment plans schema...\n";
      // Split SQL by semicolons and execute each statement
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty lines and comments
        }
        
        // Skip comment-only statements
        if (preg_match('/^\s*--/', $statement)) {
            continue;
        }
        
        try {
            echo "Executing: " . substr($statement, 0, 80) . "...\n";
            $result = $pdo->exec($statement);
            echo "✓ Success - Affected rows: " . ($result !== false ? $result : 0) . "\n";
        } catch (PDOException $e) {
            // If it's a duplicate entry error for the INSERT, that's okay
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "⚠ Skipped duplicate entry\n";
            } else {
                echo "❌ Error: " . $e->getMessage() . "\n";
                throw $e;
            }
        }
    }
    
    echo "\n=== Schema execution complete ===\n";
    
    // Verify the table was created and data inserted
    $stmt = $pdo->query("SELECT plan_code, plan_name, daily_rate, is_active FROM investment_plans ORDER BY display_order");
    $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nInvestment Plans in Database:\n";
    echo "-----------------------------\n";
    foreach ($plans as $plan) {
        $status = $plan['is_active'] ? 'ACTIVE' : 'DISABLED';
        $rate = ($plan['daily_rate'] * 100) . '%';
        echo "• {$plan['plan_name']} ({$plan['plan_code']}) - {$rate} daily - {$status}\n";
    }
    
    echo "\n✅ Investment plans database setup complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
