{"packages": [{"name": "comely-io/data-types", "version": "1.0.34", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/comely-io/data-types.git", "reference": "833ecf364a99aa5cd161e0ebd1191860921834c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/comely-io/data-types/zipball/833ecf364a99aa5cd161e0ebd1191860921834c6", "reference": "833ecf364a99aa5cd161e0ebd1191860921834c6", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-mbstring": "*", "php": "^7.2"}, "time": "2020-06-07T14:02:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Comely\\DataTypes\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fur<PERSON><PERSON> <PERSON>", "email": "<EMAIL>", "homepage": "https://www.furqansiddiqui.com", "role": "Author"}], "description": "PHP data types", "homepage": "https://github.com/comely-io/data-types", "support": {"issues": "https://github.com/comely-io/data-types/issues", "source": "https://github.com/comely-io/data-types/tree/master"}, "abandoned": true, "install-path": "../comely-io/data-types"}, {"name": "fgrosse/phpasn1", "version": "v2.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/fgrosse/PHPASN1.git", "reference": "42060ed45344789fb9f21f9f1864fc47b9e3507b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fgrosse/PHPASN1/zipball/42060ed45344789fb9f21f9f1864fc47b9e3507b", "reference": "42060ed45344789fb9f21f9f1864fc47b9e3507b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "~2.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "suggest": {"ext-bcmath": "BCmath is the fallback extension for big integer calculations", "ext-curl": "For loading OID information from the web if they have not bee defined statically", "ext-gmp": "GMP is the preferred extension for big integer calculations", "phpseclib/bcmath_compat": "BCmath polyfill for servers where neither GMP nor BCmath is available"}, "time": "2022-12-19T11:08:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"FG\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Friedrich Große", "email": "<EMAIL>", "homepage": "https://github.com/FGrosse", "role": "Author"}, {"name": "All contributors", "homepage": "https://github.com/FGrosse/PHPASN1/contributors"}], "description": "A PHP Framework that allows you to encode and decode arbitrary ASN.1 structures using the ITU-T X.690 Encoding Rules.", "homepage": "https://github.com/FGrosse/PHPASN1", "keywords": ["DER", "asn.1", "asn1", "ber", "binary", "decoding", "encoding", "x.509", "x.690", "x509", "x690"], "support": {"issues": "https://github.com/fgrosse/PHPASN1/issues", "source": "https://github.com/fgrosse/PHPASN1/tree/v2.5.0"}, "abandoned": true, "install-path": "../fgrosse/phpasn1"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2025-03-27T13:37:11+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "time": "2025-03-27T13:27:01+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2025-03-27T12:30:47+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "iexbase/tron-api", "version": "v5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/iexbase/tron-api.git", "reference": "45b01d8a562a2445c307df3b2f233473d970755b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/iexbase/tron-api/zipball/45b01d8a562a2445c307df3b2f233473d970755b", "reference": "45b01d8a562a2445c307df3b2f233473d970755b", "shasum": ""}, "require": {"comely-io/data-types": "^1.0", "ext-bcmath": "*", "ext-json": "*", "guzzlehttp/guzzle": "^7.2", "iexbase/web3.php": "^2.0.1", "kornrunner/secp256k1": "^0.2", "php": ">=8.0", "simplito/elliptic-php": "^1.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "time": "2022-10-18T15:21:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"IEXBase\\TronAPI\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A PHP API for interacting with Tron (Trx)", "homepage": "https://github.com/iexbase/tron-api", "keywords": ["iexbase", "tron-api", "tron-lib", "tron-php", "tron-rest-api"], "support": {"issues": "https://github.com/iexbase/tron-api/issues", "source": "https://github.com/iexbase/tron-api/tree/v5.0"}, "install-path": "../iexbase/tron-api"}, {"name": "iexbase/web3.php", "version": "2.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/iexbase/web3.php.git", "reference": "f25ed954a7586ead86046dd7e02a333a8098511b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/iexbase/web3.php/zipball/f25ed954a7586ead86046dd7e02a333a8098511b", "reference": "f25ed954a7586ead86046dd7e02a333a8098511b", "shasum": ""}, "require": {"ext-mbstring": "*", "guzzlehttp/guzzle": "^7.0", "kornrunner/keccak": "~1.0", "php": "^7.1", "phpseclib/phpseclib": "~2.0.11"}, "require-dev": {"phpunit/phpunit": "~6.0"}, "time": "2020-10-15T18:16:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Web3\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Ethereum web3 interface.", "support": {"source": "https://github.com/iexbase/web3.php/tree/2.0.1"}, "install-path": "../iexbase/web3.php"}, {"name": "kornrunner/keccak", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/kornrunner/php-keccak.git", "reference": "433749d28e117fb97baf9f2631b92b5d9ab3c890"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kornrunner/php-keccak/zipball/433749d28e117fb97baf9f2631b92b5d9ab3c890", "reference": "433749d28e117fb97baf9f2631b92b5d9ab3c890", "shasum": ""}, "require": {"php": ">=7.3", "symfony/polyfill-mbstring": "^1.8"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "time": "2020-12-07T15:40:44+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"kornrunner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/kornrunner/php-keccak"}], "description": "Pure PHP implementation of Keccak", "keywords": ["keccak", "sha-3", "sha3-256"], "support": {"issues": "https://github.com/kornrunner/php-keccak/issues", "source": "https://github.com/kornrunner/php-keccak/tree/1.1.0"}, "install-path": "../kornrunner/keccak"}, {"name": "kornrunner/secp256k1", "version": "0.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/kornrunner/php-secp256k1.git", "reference": "c3990dba47c7a8b0c9fd858fb29c61a5794fbb39"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kornrunner/php-secp256k1/zipball/c3990dba47c7a8b0c9fd858fb29c61a5794fbb39", "reference": "c3990dba47c7a8b0c9fd858fb29c61a5794fbb39", "shasum": ""}, "require": {"mdanter/ecc": "^1", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9"}, "time": "2021-01-19T03:30:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"kornrunner\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "boris.mom<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Pure PHP secp256k1", "keywords": ["curve", "ecc", "elliptic", "secp256k1"], "support": {"issues": "https://github.com/kornrunner/php-secp256k1/issues", "source": "https://github.com/kornrunner/php-secp256k1/tree/0.2.0"}, "install-path": "../kornrunner/secp256k1"}, {"name": "mdanter/ecc", "version": "v1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phpecc/phpecc.git", "reference": "34e2eec096bf3dcda814e8f66dd91ae87a2db7cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpecc/phpecc/zipball/34e2eec096bf3dcda814e8f66dd91ae87a2db7cd", "reference": "34e2eec096bf3dcda814e8f66dd91ae87a2db7cd", "shasum": ""}, "require": {"ext-gmp": "*", "fgrosse/phpasn1": "^2.0", "php": "^7.0||^8.0"}, "require-dev": {"phpunit/phpunit": "^6.0||^8.0||^9.0", "squizlabs/php_codesniffer": "^2.0", "symfony/yaml": "^2.6|^3.0"}, "time": "2021-01-16T19:42:14+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Mdanter\\Ecc\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "http://matejdanter.com/", "role": "Author"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://aztech.io", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "PHP Elliptic Curve Cryptography library", "homepage": "https://github.com/phpecc/phpecc", "keywords": ["<PERSON><PERSON><PERSON>", "ECDSA", "<PERSON><PERSON>", "curve", "ecdh", "elliptic", "nistp192", "nistp224", "nistp256", "nistp384", "nistp521", "phpecc", "secp256k1", "secp256r1"], "support": {"issues": "https://github.com/phpecc/phpecc/issues", "source": "https://github.com/phpecc/phpecc/tree/v1.0.0"}, "abandoned": "paragonie/ecc", "install-path": "../mdanter/ecc"}, {"name": "phpseclib/phpseclib", "version": "2.0.48", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "eaa7be704b8b93a6913b69eb7f645a59d7731b61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/eaa7be704b8b93a6913b69eb7f645a59d7731b61", "reference": "eaa7be704b8b93a6913b69eb7f645a59d7731b61", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations.", "ext-xml": "Install the XML extension to load XML formatted public keys."}, "time": "2024-12-14T21:03:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.48"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "install-path": "../phpseclib/phpseclib"}, {"name": "psr/http-client", "version": "1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-09-23T14:17:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "time": "2024-04-15T12:06:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:54:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "install-path": "../psr/http-message"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "simplito/bigint-wrapper-php", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/simplito/bigint-wrapper-php.git", "reference": "cf21ec76d33f103add487b3eadbd9f5033a25930"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplito/bigint-wrapper-php/zipball/cf21ec76d33f103add487b3eadbd9f5033a25930", "reference": "cf21ec76d33f103add487b3eadbd9f5033a25930", "shasum": ""}, "time": "2018-02-27T12:38:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"BI\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Simplito Team", "email": "s.s<PERSON><PERSON><PERSON><PERSON>@simplito.com", "homepage": "https://simplito.com"}], "description": "Common interface for php_gmp and php_bcmath modules", "support": {"issues": "https://github.com/simplito/bigint-wrapper-php/issues", "source": "https://github.com/simplito/bigint-wrapper-php/tree/1.0.0"}, "install-path": "../simplito/bigint-wrapper-php"}, {"name": "simplito/bn-php", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/simplito/bn-php.git", "reference": "83446756a81720eacc2ffb87ff97958431451fd6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplito/bn-php/zipball/83446756a81720eacc2ffb87ff97958431451fd6", "reference": "83446756a81720eacc2ffb87ff97958431451fd6", "shasum": ""}, "require": {"simplito/bigint-wrapper-php": "~1.0.0"}, "require-dev": {"phpunit/phpunit": "*"}, "time": "2024-01-10T16:16:59+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"BN\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Simplito Team", "email": "s.s<PERSON><PERSON><PERSON><PERSON>@simplito.com", "homepage": "https://simplito.com"}], "description": "Big number implementation compatible with bn.js", "support": {"issues": "https://github.com/simplito/bn-php/issues", "source": "https://github.com/simplito/bn-php/tree/1.1.4"}, "install-path": "../simplito/bn-php"}, {"name": "simplito/elliptic-php", "version": "1.0.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/simplito/elliptic-php.git", "reference": "be321666781be2be2c89c79c43ffcac834bc8868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/simplito/elliptic-php/zipball/be321666781be2be2c89c79c43ffcac834bc8868", "reference": "be321666781be2be2c89c79c43ffcac834bc8868", "shasum": ""}, "require": {"ext-gmp": "*", "simplito/bn-php": "~1.1.0"}, "require-dev": {"phpbench/phpbench": "@dev", "phpunit/phpunit": "*"}, "time": "2024-01-09T14:57:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Elliptic\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Simplito Team", "email": "s.s<PERSON><PERSON><PERSON><PERSON>@simplito.com", "homepage": "https://simplito.com"}], "description": "Fast elliptic curve cryptography", "homepage": "https://github.com/simplito/elliptic-php", "keywords": ["Curve25519", "ECDSA", "Ed25519", "EdDSA", "cryptography", "curve", "curve25519-weier", "ecc", "ecdh", "elliptic", "nistp192", "nistp224", "nistp256", "nistp384", "nistp521", "secp256k1"], "support": {"issues": "https://github.com/simplito/elliptic-php/issues", "source": "https://github.com/simplito/elliptic-php/tree/1.0.12"}, "install-path": "../simplito/elliptic-php"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T14:21:43+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-12-23T08:48:59+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}], "dev": true, "dev-package-names": []}