<?php
/**
 * Comprehensive End-to-End Authentication and Investment Test
 * This script tests the complete user flow: login → authentication → investment creation
 */

echo "=== COMPREHENSIVE AUTHENTICATION & INVESTMENT FLOW TEST ===\n\n";

// Test Configuration
$frontendUrl = 'http://localhost:8080';
$testEmail = '<EMAIL>';
$testPassword = 'password123';

// Cookie jar to maintain session across requests
$cookieJar = tempnam(sys_get_temp_dir(), 'test_cookies');

echo "Test Configuration:\n";
echo "Frontend URL: $frontendUrl\n";
echo "Test Email: $testEmail\n";
echo "Cookie Jar: $cookieJar\n\n";

// Function to make HTTP requests with session maintenance
function makeRequest($url, $data = null, $cookieJar = null, $method = 'GET') {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if ($cookieJar) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieJar);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieJar);
    }
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Step 1: Test login through ajax.php
echo "STEP 1: Testing Login Authentication\n";
echo "=====================================\n";

$loginData = json_encode([
    'action' => 'login',
    'email' => $testEmail,
    'password' => $testPassword
]);

$loginResult = makeRequest("$frontendUrl/frontend/ajax.php", $loginData, $cookieJar, 'POST');

echo "Login Request:\n";
echo "HTTP Status: {$loginResult['httpCode']}\n";
echo "Response: {$loginResult['response']}\n\n";

if ($loginResult['httpCode'] !== 200) {
    echo "❌ Login request failed with HTTP {$loginResult['httpCode']}\n";
    echo "Error: {$loginResult['error']}\n";
    exit(1);
}

$loginResponse = json_decode($loginResult['response'], true);

if (!$loginResponse) {
    echo "❌ Failed to parse login response JSON\n";
    exit(1);
}

if (isset($loginResponse['success']) && $loginResponse['success']) {
    echo "✅ Login successful!\n";
    echo "User ID: " . ($loginResponse['user']['id'] ?? 'Not provided') . "\n";
    echo "Token present: " . (isset($loginResponse['token']) ? 'Yes' : 'No') . "\n";
    echo "Session should be established now.\n\n";
} else {
    echo "❌ Login failed: " . ($loginResponse['error'] ?? 'Unknown error') . "\n";
    exit(1);
}

// Step 2: Test profile access to verify session is working
echo "STEP 2: Testing Session Persistence\n";
echo "===================================\n";

$profileData = json_encode(['action' => 'get_profile']);
$profileResult = makeRequest("$frontendUrl/frontend/ajax.php", $profileData, $cookieJar, 'POST');

echo "Profile Request:\n";
echo "HTTP Status: {$profileResult['httpCode']}\n";
echo "Response: {$profileResult['response']}\n\n";

$profileResponse = json_decode($profileResult['response'], true);

if (isset($profileResponse['success']) && $profileResponse['success']) {
    echo "✅ Session is working - profile data retrieved\n";
    echo "User email: " . ($profileResponse['user']['email'] ?? 'Not provided') . "\n\n";
} else {
    echo "❌ Session test failed: " . ($profileResponse['error'] ?? 'Unknown error') . "\n";
    echo "This could indicate a session management issue.\n\n";
}

// Step 3: Test investment creation (this is the main test)
echo "STEP 3: Testing Investment Creation\n";
echo "==================================\n";

$investmentData = json_encode([
    'action' => 'create_investment',
    'plan' => 'basic',
    'amount' => 600
]);

$investmentResult = makeRequest("$frontendUrl/frontend/ajax.php", $investmentData, $cookieJar, 'POST');

echo "Investment Creation Request:\n";
echo "HTTP Status: {$investmentResult['httpCode']}\n";
echo "Response: {$investmentResult['response']}\n\n";

$investmentResponse = json_decode($investmentResult['response'], true);

if (!$investmentResponse) {
    echo "❌ Failed to parse investment response JSON\n";
    exit(1);
}

if (isset($investmentResponse['success']) && $investmentResponse['success']) {
    echo "✅ INVESTMENT CREATION SUCCESSFUL!\n";
    echo "The authentication fix is working correctly.\n";
    
    if (isset($investmentResponse['investment_id'])) {
        echo "Investment ID: {$investmentResponse['investment_id']}\n";
    }
    
    if (isset($investmentResponse['message'])) {
        echo "Message: {$investmentResponse['message']}\n";
    }
    
    echo "\n🎉 SUCCESS: The 'User not authenticated' error has been resolved!\n\n";
    
} else {
    echo "❌ INVESTMENT CREATION FAILED\n";
    echo "Error: " . ($investmentResponse['error'] ?? 'Unknown error') . "\n";
    
    if ($investmentResponse['error'] === 'Not authenticated') {
        echo "\n🔍 DIAGNOSIS: The 'User not authenticated' error is still occurring.\n";
        echo "This suggests the session → backend authentication bridge may need further fixes.\n\n";
    } else {
        echo "\n🔍 DIAGNOSIS: Different error occurred - authentication may be working but there's another issue.\n\n";
    }
}

// Step 4: Test getting active investments
echo "STEP 4: Testing Get Active Investments\n";
echo "=====================================\n";

$getInvestmentsData = json_encode(['action' => 'get_active_investments']);
$getInvestmentsResult = makeRequest("$frontendUrl/frontend/ajax.php", $getInvestmentsData, $cookieJar, 'POST');

echo "Get Active Investments Request:\n";
echo "HTTP Status: {$getInvestmentsResult['httpCode']}\n";
echo "Response: {$getInvestmentsResult['response']}\n\n";

$getInvestmentsResponse = json_decode($getInvestmentsResult['response'], true);

if ($getInvestmentsResponse && !isset($getInvestmentsResponse['error'])) {
    echo "✅ Get active investments successful\n";
    
    if (is_array($getInvestmentsResponse)) {
        echo "Found " . count($getInvestmentsResponse) . " active investments\n";
    } elseif (isset($getInvestmentsResponse['investments'])) {
        echo "Found " . count($getInvestmentsResponse['investments']) . " active investments\n";
    }
} else {
    echo "❌ Get active investments failed\n";
    echo "Error: " . ($getInvestmentsResponse['error'] ?? 'Unknown error') . "\n";
}

// Cleanup
echo "\nCleaning up...\n";
if (file_exists($cookieJar)) {
    unlink($cookieJar);
}

echo "\n=== TEST COMPLETE ===\n";
echo "Summary:\n";
echo "- Login: " . (isset($loginResponse['success']) && $loginResponse['success'] ? '✅ Success' : '❌ Failed') . "\n";
echo "- Session: " . (isset($profileResponse['success']) && $profileResponse['success'] ? '✅ Working' : '❌ Failed') . "\n";
echo "- Investment: " . (isset($investmentResponse['success']) && $investmentResponse['success'] ? '✅ Success' : '❌ Failed') . "\n";
echo "- Get Investments: " . (isset($getInvestmentsResponse) && !isset($getInvestmentsResponse['error']) ? '✅ Success' : '❌ Failed') . "\n";

if (isset($investmentResponse['success']) && $investmentResponse['success']) {
    echo "\n🎯 FINAL RESULT: AUTHENTICATION FIX SUCCESSFUL!\n";
    echo "The 'User not authenticated' error has been resolved.\n";
    echo "Users can now successfully create investments through the frontend.\n";
} else {
    echo "\n⚠️  FINAL RESULT: FURTHER INVESTIGATION NEEDED\n";
    echo "The authentication flow may need additional adjustments.\n";
}

?>
