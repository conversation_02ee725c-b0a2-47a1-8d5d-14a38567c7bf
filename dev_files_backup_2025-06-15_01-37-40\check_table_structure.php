<?php
require_once 'vendor/autoload.php';
use Simbi\Tls\Config\Database;

try {
    $pdo = Database::getConnection();
    
    // Describe users table structure
    echo "Users table structure:\n";
    echo "=====================\n";
    $stmt = $pdo->query('DESCRIBE users');
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "Column: {$column['Field']}, Type: {$column['Type']}, Null: {$column['Null']}, Key: {$column['Key']}, Default: {$column['Default']}, Extra: {$column['Extra']}\n";
    }
    
    echo "\<NAME_EMAIL> has a password:\n";
    $stmt = $pdo->prepare('SELECT * FROM users WHERE email = ?');
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "User data:\n";
        foreach ($user as $key => $value) {
            echo "$key: $value\n";
        }
    } else {
        echo "User not found\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
