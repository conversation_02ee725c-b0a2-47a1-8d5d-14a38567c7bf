<?php

namespace Simbi\Tls\Config;

class Config
{
    private static array $config = [];

    public static function load(): void
    {
        // Load from .env file if it exists
        $envFile = __DIR__ . '/../../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos(trim($line), '#') === 0) continue;
                list($name, $value) = explode('=', $line, 2);
                self::$config[trim($name)] = trim($value);
            }
        }

        // Set defaults (do not use testnet or secrets in code)
        self::$config = array_merge([
            'DB_HOST' => 'localhost',
            'DB_NAME' => 'tlssc',
            'DB_USER' => 'root',            'DB_PASS' => '',
            'MASTER_ADDRESS' => '',
            'MASTER_PRIVATE_KEY' => '64ac6824-cdee-4a5f-846e-98b297dfe3f5',
            'USDT_CONTRACT' => 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf', // Nile testnet USDT contract - Set in .env for production
            'TRON_NETWORK' => 'nile'   // Set in .env for production
        ], self::$config);
    }

    public static function get(string $key, $default = null)
    {
        return self::$config[$key] ?? $default;
    }

    public static function all(): array
    {
        return self::$config;
    }
}

// NOTE: All secrets and sensitive configuration must be set in the .env file for production. Never commit secrets to source control.
