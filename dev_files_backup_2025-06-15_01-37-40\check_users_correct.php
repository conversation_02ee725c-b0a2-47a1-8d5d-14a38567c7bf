<?php
require_once 'vendor/autoload.php';

try {
    $pdo = \Simbi\Tls\Config\Database::getConnection();
    
    // Check if there are any users at all
    $countStmt = $pdo->query('SELECT COUNT(*) as total FROM users');
    $count = $countStmt->fetch();
    echo "Total users: " . $count['total'] . "\n";
    
    if ($count['total'] > 0) {
        echo "\nRecent users:\n";
        $stmt = $pdo->query('SELECT email, is_admin, created_at FROM users ORDER BY created_at DESC LIMIT 5');
        while($row = $stmt->fetch()) {
            $type = $row['is_admin'] ? 'Admin' : 'User';
            echo $row['email'] . ' (' . $type . ') - ' . $row['created_at'] . "\n";
        }
    } else {
        echo "No users found. You need to create a user account first.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
