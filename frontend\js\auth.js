// Authentication JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize auth page
    initAuthPage();
});

function initAuthPage() {
    // Set up form handlers
    setupFormHandlers();
    
    // Show login form by default
    showLogin();
}

function setupFormHandlers() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Register form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    // Forgot password form
    const forgotPasswordForm = document.getElementById('forgotPasswordForm');
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', handleForgotPassword);
    }
}

function showLogin() {
    hideAllForms();
    document.getElementById('loginForm').classList.add('active');
    setActiveTab(0);
}

function showRegister() {
    hideAllForms();
    document.getElementById('registerForm').classList.add('active');
    setActiveTab(1);
}

function showForgotPassword() {
    hideAllForms();
    document.getElementById('forgotPasswordForm').classList.add('active');
    // Reset tab states
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
}

function hideAllForms() {
    document.querySelectorAll('.auth-form').forEach(form => {
        form.classList.remove('active');
    });
}

function setActiveTab(index) {
    document.querySelectorAll('.tab-btn').forEach((btn, i) => {
        btn.classList.toggle('active', i === index);
    });
}

async function handleLogin(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const email = formData.get('email');
    const password = formData.get('password');
    
    if (!email || !password) {
        showMessage('Please fill in all fields', 'error');
        return;
    }
    
    // Disable submit button and show loading
    setButtonLoading(submitBtn, true);
    
    try {
        const response = await fetch('ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'login',
                email: email,
                password: password
            })
        });
        
        const result = await response.json();
          if (result.success) {
            showMessage('Login successful! Redirecting...', 'success');
            // Redirect based on user role
            setTimeout(() => {
                if (result.user && result.user.is_admin) {
                    window.location.href = 'admin.php';
                } else {
                    window.location.href = 'user/dashboard.php';
                }
            }, 1000);
        } else {
            showMessage(result.error || 'Login failed', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showMessage('Connection error. Please try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

async function handleRegister(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const email = formData.get('email');
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');
    
    if (!email || !password || !confirmPassword) {
        showMessage('Please fill in all fields', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showMessage('Passwords do not match', 'error');
        return;
    }
    
    if (password.length < 6) {
        showMessage('Password must be at least 6 characters long', 'error');
        return;
    }
    
    // Disable submit button and show loading
    setButtonLoading(submitBtn, true);
    
    try {
        const response = await fetch('ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'register',
                email: email,
                password: password
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Registration successful! Redirecting...', 'success');
            // Redirect based on user role            
            setTimeout(() => {
                if (result.user && result.user.is_admin) {
                    window.location.href = 'admin.php';
                } else {
                    window.location.href = 'user/dashboard.php';
                }
            }, 1000);
        } else {
            showMessage(result.error || 'Registration failed', 'error');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showMessage('Connection error. Please try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

async function handleForgotPassword(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const email = formData.get('email');
    
    if (!email) {
        showMessage('Please enter your email address', 'error');
        return;
    }
    
    // Disable submit button and show loading
    setButtonLoading(submitBtn, true);
    
    try {
        const response = await fetch('ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'forgot_password',
                email: email
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Password reset email sent! Check your inbox.', 'success');
            form.reset();
        } else {
            showMessage(result.error || 'Failed to send reset email', 'error');
        }
    } catch (error) {
        console.error('Forgot password error:', error);
        showMessage('Connection error. Please try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

function setButtonLoading(button, loading) {
    if (loading) {
        button.disabled = true;
        button.classList.add('loading');
        button.textContent = 'Please wait...';
    } else {
        button.disabled = false;
        button.classList.remove('loading');
        // Reset button text based on form
        const form = button.closest('form');
        if (form.id === 'loginForm') {
            button.textContent = 'Login';
        } else if (form.id === 'registerForm') {
            button.textContent = 'Register';
        } else if (form.id === 'forgotPasswordForm') {
            button.textContent = 'Send Reset Link';
        }
    }
}

function showMessage(message, type = 'info') {
    const messageEl = document.getElementById('message');
    if (messageEl) {
        messageEl.textContent = message;
        messageEl.className = `message ${type}`;
        messageEl.style.display = 'block';
        
        // Auto-hide success and info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }
    }
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
