<?php
require_once 'vendor/autoload.php';

$pdo = \Simbi\Tls\Config\Database::getConnection();
$stmt = $pdo->query('SELECT plan_code, plan_name, is_active FROM investment_plans ORDER BY display_order');

echo "Investment Plans Status:\n";
echo "========================\n";
while($row = $stmt->fetch()) {
    $status = $row['is_active'] ? 'ENABLED' : 'DISABLED';
    echo $row['plan_name'] . ' (' . $row['plan_code'] . '): ' . $status . "\n";
}
?>
