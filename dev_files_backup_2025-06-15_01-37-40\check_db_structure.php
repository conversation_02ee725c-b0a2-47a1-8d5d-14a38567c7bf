<?php
require_once 'vendor/autoload.php';

try {
    $pdo = \Simbi\Tls\Config\Database::getConnection();
    
    // First, let's see what tables exist
    echo "Tables in database:\n";
    $stmt = $pdo->query('SHOW TABLES');
    while($row = $stmt->fetch()) {
        echo "- " . $row[0] . "\n";
    }
    
    // Check the structure of the users table
    echo "\nUsers table structure:\n";
    $stmt = $pdo->query('DESCRIBE users');
    while($row = $stmt->fetch()) {
        echo $row['Field'] . ' (' . $row['Type'] . ")\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
