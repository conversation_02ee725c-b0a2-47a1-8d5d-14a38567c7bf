<?php

namespace Simbi\Tls\Repositories;

use PDO;
use Exception;

class TransactionRepository
{
    private PDO $pdo;

    public function __construct(PDO $pdo)
    {
        $this->pdo = $pdo;
    }

    public function create(array $data): ?int
    {
        try {
            $sql = "INSERT INTO transactions (
                user_id, wallet_id, transaction_hash, type, amount, fee,
                from_address, to_address, status, block_number, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->pdo->prepare($sql);
            $success = $stmt->execute([
                $data['user_id'],
                $data['wallet_id'],
                $data['transaction_hash'],
                $data['type'],
                $data['amount'],
                $data['fee'] ?? 0,
                $data['from_address'] ?? null,
                $data['to_address'] ?? null,
                $data['status'] ?? 'pending',
                $data['block_number'] ?? null,
                $data['notes'] ?? null
            ]);

            return $success ? $this->pdo->lastInsertId() : null;
        } catch (Exception $e) {
            error_log("Transaction creation error: " . $e->getMessage());
            return null;
        }
    }

    public function findByUserId(int $userId, int $limit = 50, int $offset = 0): array
    {
        try {
            $sql = "SELECT t.*, w.address as wallet_address 
                    FROM transactions t 
                    JOIN wallets w ON t.wallet_id = w.id 
                    WHERE t.user_id = ? 
                    ORDER BY t.created_at DESC 
                    LIMIT ? OFFSET ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$userId, $limit, $offset]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Transaction fetch error: " . $e->getMessage());
            return [];
        }
    }

    public function findByWalletId(int $walletId, int $limit = 50, int $offset = 0): array
    {
        try {
            $sql = "SELECT * FROM transactions 
                    WHERE wallet_id = ? 
                    ORDER BY created_at DESC 
                    LIMIT ? OFFSET ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$walletId, $limit, $offset]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Transaction fetch error: " . $e->getMessage());
            return [];
        }
    }

    public function findById(int $id): ?array
    {
        try {
            $sql = "SELECT t.*, w.address as wallet_address 
                    FROM transactions t 
                    JOIN wallets w ON t.wallet_id = w.id 
                    WHERE t.id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$id]);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ?: null;
        } catch (Exception $e) {
            error_log("Transaction fetch error: " . $e->getMessage());
            return null;
        }
    }

    public function updateStatus(int $id, string $status, array $updates = []): bool
    {
        try {
            $setParts = ['status = ?', 'updated_at = NOW()'];
            $params = [$status];            foreach ($updates as $field => $value) {
                if (in_array($field, ['confirmations', 'block_number'])) {
                    $setParts[] = "$field = ?";
                    $params[] = $value;
                }
            }

            $params[] = $id;
            $sql = "UPDATE transactions SET " . implode(', ', $setParts) . " WHERE id = ?";
            
            $stmt = $this->pdo->prepare($sql);        return $stmt->execute($params);
        } catch (Exception $e) {
            error_log("Transaction update error: " . $e->getMessage());
            return false;
        }
    }

    public function getStatistics(int $userId = null): array
    {
        try {
            $whereClause = $userId ? "WHERE user_id = ?" : "";
            $params = $userId ? [$userId] : [];

            $sql = "SELECT 
                        COUNT(*) as total_transactions,
                        SUM(CASE WHEN type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
                        SUM(CASE WHEN type = 'withdrawal' THEN amount ELSE 0 END) as total_withdrawals,
                        SUM(CASE WHEN type = 'sweep' THEN amount ELSE 0 END) as total_sweeps,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_count,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
                    FROM transactions $whereClause";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
        } catch (Exception $e) {
            error_log("Transaction statistics error: " . $e->getMessage());
            return [];
        }
    }

    public function findByHash(string $transactionHash): ?array
    {
        try {
            $sql = "SELECT t.*, w.address as wallet_address 
                    FROM transactions t 
                    JOIN wallets w ON t.wallet_id = w.id 
                    WHERE t.transaction_hash = ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$transactionHash]);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ?: null;
        } catch (Exception $e) {
            error_log("Transaction fetch by hash error: " . $e->getMessage());
            return null;
        }
    }
}
