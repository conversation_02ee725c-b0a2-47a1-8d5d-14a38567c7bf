// Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initDashboard();
});

let currentPage = 0;
let currentLimit = 10;
let walletData = null;

function initDashboard() {
    loadInitialData();
    setupNavigation();
    setupForms();
    initFooterMenu();
}

function setupNavigation() {
    // Tab navigation for both regular nav and footer nav
    const navButtons = document.querySelectorAll('.nav-btn, .footer-btn[data-tab]');
    
    navButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const tabId = this.dataset.tab;
            if (tabId) {
                showTab(tabId);
                
                // Update active state for both nav types
                document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.footer-btn[data-tab]').forEach(b => b.classList.remove('active'));
                
                // Set active state for clicked button type
                if (this.classList.contains('nav-btn')) {
                    this.classList.add('active');
                    // Also update corresponding footer button
                    const footerBtn = document.querySelector(`.footer-btn[data-tab="${tabId}"]`);
                    if (footerBtn) footerBtn.classList.add('active');
                } else if (this.classList.contains('footer-btn')) {
                    this.classList.add('active');
                    // Also update corresponding nav button
                    const navBtn = document.querySelector(`.nav-btn[data-tab="${tabId}"]`);
                    if (navBtn) navBtn.classList.add('active');
                }
            }        });
    });
}

function setupForms() {
    // Wallet actions
    const createWalletBtn = document.getElementById('createWalletBtn');
    const refreshBalanceBtn = document.getElementById('refreshBalanceBtn');
    const copyAddressBtn = document.getElementById('copyAddressBtn');
    const copyDepositBtn = document.getElementById('copyDepositBtn');
    const sweepFundsBtn = document.getElementById('sweepFundsBtn');
    
    if (createWalletBtn) {
        createWalletBtn.addEventListener('click', createWallet);
    }
    
    if (refreshBalanceBtn) {
        refreshBalanceBtn.addEventListener('click', refreshBalance);
    }
    
    if (copyAddressBtn) {
        copyAddressBtn.addEventListener('click', () => copyToClipboard(walletData?.address));
    }
    
    if (copyDepositBtn) {
        copyDepositBtn.addEventListener('click', () => copyToClipboard(walletData?.address));
    }
    
    if (sweepFundsBtn) {
        sweepFundsBtn.addEventListener('click', sweepFunds);
    }
      // Forms
    const withdrawForm = document.getElementById('withdrawForm');
    const depositForm = document.getElementById('depositForm');
    
    if (withdrawForm) {
        withdrawForm.addEventListener('submit', handleWithdraw);
    }
    
    if (depositForm) {
        depositForm.addEventListener('submit', handleDeposit);
    }
    
    // Transaction filters
    const typeFilter = document.getElementById('transactionType');
    const statusFilter = document.getElementById('transactionStatus');
    
    if (typeFilter) {
        typeFilter.addEventListener('change', loadTransactions);
    }
    
    if (statusFilter) {
        statusFilter.addEventListener('change', loadTransactions);
    }
    
    // Pagination
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');
    
    if (prevPage) {
        prevPage.addEventListener('click', () => {
            if (currentPage > 0) {
                currentPage--;
                loadTransactions();
            }
        });
    }
    
    if (nextPage) {
        nextPage.addEventListener('click', () => {
            currentPage++;
            loadTransactions();
        });
    }
}

function showTab(tabId) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected tab
    const selectedTab = document.getElementById(tabId);
    if (selectedTab) {
        selectedTab.classList.add('active');
        
        // Load tab-specific data
        switch (tabId) {
            case 'dashboard':
                loadDashboardData();
                break;
            case 'wallet':
                loadWalletData();
                break;
            case 'transactions':
                loadTransactions();
                break;
            case 'deposit':
                loadDepositData();
                break;
        }
    }
}

async function loadInitialData() {
    try {
        await Promise.all([
            loadWalletData(),
            loadDashboardData()
        ]);
    } catch (error) {
        console.error('Error loading initial data:', error);
        showMessage('Error loading data. Please refresh the page.', 'error');
    }
}

async function loadDashboardData() {
    try {
        // Load balance
        const balanceResponse = await apiCall('get_balance');
        if (balanceResponse.success) {
            document.getElementById('totalBalance').textContent = balanceResponse.balance_formatted || '0.000000';
            walletData = balanceResponse;
        }
        
        // Load transaction statistics
        const statsResponse = await apiCall('get_transaction_statistics');
        if (statsResponse.success) {
            const stats = statsResponse.statistics;
            document.getElementById('totalTransactions').textContent = stats.total_transactions || '0';
            document.getElementById('totalDeposits').textContent = stats.total_deposits || '0';
            document.getElementById('totalVolume').textContent = (stats.total_volume || '0') + ' TRX';
        }
        
        // Load recent transactions
        const transactionsResponse = await apiCall('get_transactions', { limit: 5, offset: 0 });
        if (transactionsResponse.success) {
            displayRecentTransactions(transactionsResponse.transactions);
        }
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

async function loadWalletData() {
    try {
        const response = await apiCall('get_balance');
        if (response.success) {
            walletData = response;
            updateWalletDisplay();
        } else if (response.error && response.error.includes('Wallet not found')) {
            // No wallet exists yet
            walletData = null;
            updateWalletDisplay();
        }
    } catch (error) {
        console.error('Error loading wallet data:', error);
    }
}

function updateWalletDisplay() {
    const addressEl = document.getElementById('walletAddress');
    const balanceEl = document.getElementById('walletBalance');
    const createBtn = document.getElementById('createWalletBtn');
    const refreshBtn = document.getElementById('refreshBalanceBtn');
    const copyBtn = document.getElementById('copyAddressBtn');
    const sweepBtn = document.getElementById('sweepFundsBtn');
    const depositAddressEl = document.getElementById('depositAddress');
    const copyDepositBtn = document.getElementById('copyDepositBtn');
    
    if (walletData && walletData.address) {
        // Wallet exists
        if (addressEl) addressEl.value = walletData.address;
        if (balanceEl) balanceEl.value = (walletData.balance_formatted || '0.000000') + ' TRX';
        
        if (createBtn) createBtn.style.display = 'none';
        if (refreshBtn) refreshBtn.style.display = 'inline-block';
        if (copyBtn) copyBtn.style.display = 'inline-block';
        if (sweepBtn) sweepBtn.style.display = 'inline-block';
        
        // Update deposit section
        if (depositAddressEl) depositAddressEl.textContent = walletData.address;
        if (copyDepositBtn) copyDepositBtn.style.display = 'inline-block';
        
    } else {
        // No wallet
        if (addressEl) addressEl.value = 'No wallet created yet';
        if (balanceEl) balanceEl.value = '0.000000 TRX';
        
        if (createBtn) createBtn.style.display = 'inline-block';
        if (refreshBtn) refreshBtn.style.display = 'none';
        if (copyBtn) copyBtn.style.display = 'none';
        if (sweepBtn) sweepBtn.style.display = 'none';
        
        // Update deposit section
        if (depositAddressEl) depositAddressEl.textContent = 'Create a wallet first';
        if (copyDepositBtn) copyDepositBtn.style.display = 'none';
    }
}

async function createWallet() {
    const btn = document.getElementById('createWalletBtn');
    setButtonLoading(btn, true);
    
    try {
        const response = await apiCall('create_wallet');
        if (response.success) {
            showMessage('Wallet created successfully!', 'success');
            await loadWalletData();
        } else {
            showMessage(response.error || 'Failed to create wallet', 'error');
        }
    } catch (error) {
        console.error('Error creating wallet:', error);
        showMessage('Error creating wallet. Please try again.', 'error');
    } finally {
        setButtonLoading(btn, false);
    }
}

async function refreshBalance() {
    const btn = document.getElementById('refreshBalanceBtn');
    setButtonLoading(btn, true, 'Refreshing...');
    
    try {
        const response = await apiCall('get_balance');
        if (response.success) {
            walletData = response;
            updateWalletDisplay();
            // Also update dashboard balance
            document.getElementById('totalBalance').textContent = response.balance_formatted || '0.000000';
            showMessage('Balance refreshed successfully!', 'success');
        } else {
            showMessage(response.error || 'Failed to refresh balance', 'error');
        }
    } catch (error) {
        console.error('Error refreshing balance:', error);
        showMessage('Error refreshing balance. Please try again.', 'error');
    } finally {
        setButtonLoading(btn, false, 'Refresh Balance');
    }
}

async function handleWithdraw(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const to = formData.get('to');
    const amount = parseFloat(formData.get('amount'));
    
    if (!to || !amount || amount <= 0) {
        showMessage('Please enter valid recipient address and amount', 'error');
        return;
    }
    
    setButtonLoading(submitBtn, true);
    
    try {
        const response = await apiCall('withdraw', { to, amount });
        if (response.success) {
            showMessage('Withdrawal successful!', 'success');
            form.reset();
            await loadWalletData();
            await loadDashboardData();
        } else {
            showMessage(response.error || 'Withdrawal failed', 'error');
        }
    } catch (error) {
        console.error('Error processing withdrawal:', error);
        showMessage('Error processing withdrawal. Please try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

async function sweepFunds() {
    if (!confirm('Are you sure you want to sweep all funds to the main account?')) {
        return;
    }
    
    const btn = document.getElementById('sweepFundsBtn');
    setButtonLoading(btn, true, 'Sweeping...');
    
    try {
        const response = await apiCall('sweep_funds');
        if (response.success) {
            showMessage('Funds swept successfully!', 'success');
            await loadWalletData();
            await loadDashboardData();
        } else {
            showMessage(response.error || 'Sweep failed', 'error');
        }
    } catch (error) {
        console.error('Error sweeping funds:', error);
        showMessage('Error sweeping funds. Please try again.', 'error');
    } finally {
        setButtonLoading(btn, false, 'Sweep All Funds');
    }
}

async function handleDeposit(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const transactionHash = formData.get('transaction_hash');
    const amount = parseFloat(formData.get('amount'));
    const fromAddress = formData.get('from_address');
    
    if (!transactionHash || !amount || amount <= 0 || !fromAddress || !walletData?.address) {
        showMessage('Please fill in all fields and ensure you have a wallet', 'error');
        return;
    }
    
    setButtonLoading(submitBtn, true);
    
    try {
        const response = await apiCall('record_deposit', {
            wallet_address: walletData.address,
            transaction_hash: transactionHash,
            amount: amount,
            from_address: fromAddress
        });
        
        if (response.success) {
            showMessage('Deposit recorded successfully!', 'success');
            form.reset();
            await loadWalletData();
            await loadDashboardData();
        } else {
            showMessage(response.error || 'Failed to record deposit', 'error');
        }
    } catch (error) {
        console.error('Error recording deposit:', error);
        showMessage('Error recording deposit. Please try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

async function loadTransactions() {
    const typeFilter = document.getElementById('transactionType');
    const statusFilter = document.getElementById('transactionStatus');
    
    const type = typeFilter ? typeFilter.value : null;
    const status = statusFilter ? statusFilter.value : null;
    
    try {
        const response = await apiCall('get_transactions', {
            limit: currentLimit,
            offset: currentPage * currentLimit,
            type: type || undefined,
            status: status || undefined
        });
        
        if (response.success) {
            displayTransactions(response.transactions);
            updatePagination(response.pagination);
        } else {
            showMessage(response.error || 'Failed to load transactions', 'error');
        }
    } catch (error) {
        console.error('Error loading transactions:', error);
        showMessage('Error loading transactions. Please try again.', 'error');
    }
}

function displayTransactions(transactions) {
    const container = document.getElementById('transactionsList');
    
    if (!transactions || transactions.length === 0) {
        container.innerHTML = '<p class="text-center text-muted">No transactions found</p>';
        return;
    }
    
    const transactionsHtml = transactions.map(tx => `
        <div class="transaction-row">
            <div class="transaction-details">
                <div class="transaction-type">${capitalizeFirst(tx.type)} - ${tx.amount} TRX</div>
                <div class="transaction-hash">${tx.transaction_hash}</div>
                <div class="transaction-date">${formatDate(tx.created_at)}</div>
            </div>
            <div class="transaction-status ${tx.status}">${capitalizeFirst(tx.status)}</div>
        </div>
    `).join('');
    
    container.innerHTML = `<div class="transaction-list">${transactionsHtml}</div>`;
}

function displayRecentTransactions(transactions) {
    const container = document.getElementById('recentTransactionsList');
    
    if (!transactions || transactions.length === 0) {
        container.innerHTML = '<p class="text-center text-muted">No recent transactions</p>';
        return;
    }
    
    const transactionsHtml = transactions.map(tx => `
        <div class="transaction-item">
            <div class="transaction-info">
                <div class="transaction-type">${capitalizeFirst(tx.type)}</div>
                <div class="transaction-date">${formatDate(tx.created_at)}</div>
            </div>
            <div class="transaction-amount ${tx.type === 'deposit' ? 'positive' : 'negative'}">
                ${tx.type === 'deposit' ? '+' : '-'}${tx.amount} TRX
            </div>
        </div>
    `).join('');
    
    container.innerHTML = transactionsHtml;
}

function updatePagination(pagination) {
    const pageInfo = document.getElementById('pageInfo');
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');
    
    if (pageInfo) {
        pageInfo.textContent = `Page ${currentPage + 1}`;
    }
    
    if (prevBtn) {
        prevBtn.disabled = currentPage === 0;
    }
    
    if (nextBtn) {
        nextBtn.disabled = !pagination.has_more;
    }
}

function loadDepositData() {
    if (walletData && walletData.address) {
        document.getElementById('depositAddress').textContent = walletData.address;
        document.getElementById('copyDepositBtn').style.display = 'inline-block';
        
        // Generate QR code (you might want to add a QR code library)
        generateQRCode(walletData.address);
    }
}

function generateQRCode(address) {
    // Initialize QR code generator if available
    const qrContainer = document.getElementById('depositQR');
    if (!qrContainer) return;
    
    if (typeof QRCodeGenerator !== 'undefined') {
        const qrGenerator = new QRCodeGenerator();
        qrGenerator.createQRCodeElement(address, 'depositQR');
    } else {
        // Fallback to online QR generator
        const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(address)}`;
        qrContainer.innerHTML = `
            <div class="qr-code-container">
                <img src="${qrUrl}" alt="Deposit Address QR Code" class="qr-code-image" />
                <p class="qr-code-address">${address}</p>
                <button class="btn btn-secondary btn-sm" onclick="copyToClipboard('${address}')">
                    📋 Copy Address
                </button>
            </div>
        `;
    }
    qrContainer.style.display = 'block';
}

function initFooterMenu() {
    // Add smooth entrance animation for footer menu
    const footerMenu = document.querySelector('.footer-menu');
    if (footerMenu) {
        // Always show footer menu
        footerMenu.classList.add('show');
        
        // Optional: Handle window resize for future mobile-specific behavior
        window.addEventListener('resize', function() {
            // Footer is always visible now, but this can be customized later
            footerMenu.classList.add('show');
        });
    }
}

// Utility functions
async function apiCall(action, data = {}) {
    const response = await fetch('ajax.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action,
            ...data
        })
    });
    
    if (!response.ok) {
        throw new Error('Network error');
    }
    
    return await response.json();
}

function setButtonLoading(button, loading, text = null) {
    if (loading) {
        button.disabled = true;
        button.classList.add('loading');
        if (text) {
            button.textContent = text;
        }
    } else {
        button.disabled = false;
        button.classList.remove('loading');
        if (text) {
            button.textContent = text;
        }
    }
}

function showMessage(message, type = 'info') {
    const messageEl = document.getElementById('message');
    if (messageEl) {
        messageEl.textContent = message;
        messageEl.className = `message ${type}`;
        messageEl.style.display = 'block';
        
        // Auto-hide success and info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }
    }
}

function copyToClipboard(text) {
    if (!text) {
        showMessage('Nothing to copy', 'warning');
        return;
    }
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showMessage('Copied to clipboard!', 'success');
        }).catch(() => {
            showMessage('Failed to copy to clipboard', 'error');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showMessage('Copied to clipboard!', 'success');
        } catch (err) {
            showMessage('Failed to copy to clipboard', 'error');
        }
        document.body.removeChild(textArea);
    }
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleString();
}

function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
