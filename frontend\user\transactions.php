<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'TRON Wallet - Transaction History';
$currentPage = 'transactions';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>

            <!-- Transactions Content -->
            <div class="transactions-page">
                <div class="page-header">
                    <h2>Transaction History</h2>
                    <p>View and manage your transaction history</p>
                </div>

                <!-- Transaction Filters -->
                <div class="card">
                    <h3>Filter Transactions</h3>
                    <div class="transaction-filters">
                        <div class="filter-group">
                            <label for="typeFilter">Type</label>
                            <select id="typeFilter">
                                <option value="">All Types</option>
                                <option value="deposit">Deposits</option>
                                <option value="withdrawal">Withdrawals</option>
                                <option value="transfer">Transfers</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="statusFilter">Status</label>
                            <select id="statusFilter">
                                <option value="">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="failed">Failed</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="limitFilter">Per Page</label>
                            <select id="limitFilter">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        
                        <div class="filter-actions">
                            <button id="applyFiltersBtn" class="btn btn-primary">Apply Filters</button>
                            <button id="resetFiltersBtn" class="btn btn-outline">Reset</button>
                        </div>
                    </div>
                </div>

                <!-- Transaction List -->
                <div class="card">
                    <h3>Transactions</h3>
                    <div id="transactionsList">Loading...</div>
                    
                    <!-- Pagination -->
                    <div class="pagination-container">
                        <div class="pagination-info">
                            <span id="paginationInfo">Showing 0 of 0 transactions</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="prevPageBtn" class="btn btn-outline btn-sm" disabled>Previous</button>
                            <span id="pageInfo">Page 1 of 1</span>
                            <button id="nextPageBtn" class="btn btn-outline btn-sm" disabled>Next</button>
                        </div>
                    </div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>    <script src="js/qr-generator.js"></script>
    <script src="js/transactions.js"></script>
</body>
</html>
