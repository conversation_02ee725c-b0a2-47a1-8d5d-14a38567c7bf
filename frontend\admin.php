<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in and is admin
if (!SessionService::isAuthenticated() || !SessionService::isAdmin()) {
    header('Location: index.php');
    exit;
}

$user = SessionService::getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - TLS Wallet</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-container">        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <div class="header-brand">
                    <h1>TLS Wallet</h1>
                    <span class="brand-subtitle">Admin Dashboard</span>
                </div>
                <div class="header-actions">
                    <a href="user/dashboard.php" class="btn btn-secondary">User View</a>
                    <a href="index.php?logout=1" class="btn btn-danger">Logout</a>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="admin-nav">
            <button class="nav-btn active" data-tab="overview">Overview</button>
            <button class="nav-btn" data-tab="users">Users</button>
            <button class="nav-btn" data-tab="transactions">Transactions</button>
            <button class="nav-btn" data-tab="system">System</button>
            <button class="nav-btn" data-tab="logs">Logs</button>
        </nav>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <div class="admin-grid">
                    <!-- System Stats -->
                    <div class="card stats-overview">
                        <h3>System Overview</h3>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value" id="totalUsers">0</div>
                                <div class="stat-label">Total Users</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="activeUsers">0</div>
                                <div class="stat-label">Active Users</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="totalWallets">0</div>
                                <div class="stat-label">Total Wallets</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="systemTransactions">0</div>
                                <div class="stat-label">Transactions</div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card">
                        <h3>Recent Activity (24h)</h3>
                        <div class="activity-stats">
                            <div class="activity-item">
                                <span class="activity-label">New Users:</span>
                                <span class="activity-value" id="newUsers24h">0</span>
                            </div>
                            <div class="activity-item">
                                <span class="activity-label">New Wallets:</span>
                                <span class="activity-value" id="newWallets24h">0</span>
                            </div>
                            <div class="activity-item">
                                <span class="activity-label">Transactions:</span>
                                <span class="activity-value" id="transactions24h">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Transaction Statistics -->
                    <div class="card">
                        <h3>Transaction Statistics</h3>
                        <div class="transaction-stats" id="transactionStats">
                            <div class="stat-row">
                                <span>Total Volume:</span>
                                <span id="totalVolume">0 TRX</span>
                            </div>
                            <div class="stat-row">
                                <span>Average Transaction:</span>
                                <span id="avgTransaction">0 TRX</span>
                            </div>
                            <div class="stat-row">
                                <span>Success Rate:</span>
                                <span id="successRate">0%</span>
                            </div>
                        </div>
                    </div>

                    <!-- System Health -->
                    <div class="card">
                        <h3>System Health</h3>
                        <div class="health-indicators" id="systemHealth">
                            <div class="health-item">
                                <span class="health-label">Database:</span>
                                <span class="health-status" id="dbStatus">Loading...</span>
                            </div>
                            <div class="health-item">
                                <span class="health-label">TRON Network:</span>
                                <span class="health-status" id="tronStatus">Loading...</span>
                            </div>
                            <div class="health-item">
                                <span class="health-label">API Status:</span>
                                <span class="health-status" id="apiStatus">Loading...</span>
                            </div>
                            <div class="health-item">
                                <span class="health-label">Uptime:</span>
                                <span class="health-status" id="uptime">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Tab -->
            <div id="users" class="tab-content">
                <div class="card">
                    <div class="users-header">
                        <h3>User Management</h3>
                        <div class="user-filters">
                            <select id="userStatusFilter">
                                <option value="">All Users</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                            <button id="refreshUsers" class="btn btn-secondary">Refresh</button>
                        </div>
                    </div>
                    <div class="users-table">
                        <table id="usersTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Admin</th>
                                    <th>Wallets</th>
                                    <th>Transactions</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <tr><td colspan="8">Loading...</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="pagination">
                        <button id="usersPrevPage" class="btn btn-outline">Previous</button>
                        <span id="usersPageInfo">Page 1</span>
                        <button id="usersNextPage" class="btn btn-outline">Next</button>
                    </div>
                </div>
            </div>

            <!-- Transactions Tab -->
            <div id="transactions" class="tab-content">
                <div class="card">
                    <h3>All Transactions</h3>
                    <div class="admin-transaction-stats">
                        <div class="stat-item">
                            <span>Total:</span>
                            <span id="adminTotalTransactions">0</span>
                        </div>
                        <div class="stat-item">
                            <span>Successful:</span>
                            <span id="adminSuccessfulTransactions">0</span>
                        </div>
                        <div class="stat-item">
                            <span>Failed:</span>
                            <span id="adminFailedTransactions">0</span>
                        </div>
                        <div class="stat-item">
                            <span>Deposits:</span>
                            <span id="adminDeposits">0</span>
                        </div>
                        <div class="stat-item">
                            <span>Withdrawals:</span>
                            <span id="adminWithdrawals">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Tab -->
            <div id="system" class="tab-content">
                <div class="card">
                    <h3>System Information</h3>
                    <div class="system-info" id="systemInfo">
                        <div class="info-row">
                            <span class="info-label">Memory Usage:</span>
                            <span class="info-value" id="memoryUsage">Loading...</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Response Time:</span>
                            <span class="info-value" id="responseTime">Loading...</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">System Uptime:</span>
                            <span class="info-value" id="systemUptime">Loading...</span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3>System Actions</h3>
                    <div class="system-actions">
                        <button class="btn btn-primary" onclick="refreshSystemHealth()">Refresh Health Check</button>
                        <button class="btn btn-warning" onclick="clearCache()">Clear Cache</button>
                    </div>
                </div>
            </div>

            <!-- Logs Tab -->
            <div id="logs" class="tab-content">
                <div class="card">
                    <h3>Activity Logs</h3>
                    <div class="logs-container">
                        <div class="logs-table">
                            <table id="logsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Admin</th>
                                        <th>Action</th>
                                        <th>Target User</th>
                                        <th>Details</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody id="logsTableBody">
                                    <tr><td colspan="6">Loading...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination">
                            <button id="logsPrevPage" class="btn btn-outline">Previous</button>
                            <span id="logsPageInfo">Page 1</span>
                            <button id="logsNextPage" class="btn btn-outline">Next</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- User Action Modal -->
        <div id="userActionModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3 id="userActionTitle">User Actions</h3>
                <div id="userActionContent">
                    <div class="user-info">
                        <p><strong>User ID:</strong> <span id="modalUserId"></span></p>
                        <p><strong>Email:</strong> <span id="modalUserEmail"></span></p>
                        <p><strong>Status:</strong> <span id="modalUserStatus"></span></p>
                    </div>
                    <div class="user-actions">
                        <button id="toggleUserStatus" class="btn btn-warning">Toggle Status</button>
                        <button id="promoteUser" class="btn btn-primary">Promote to Admin</button>
                    </div>
                </div>
            </div>
        </div>        <div id="message" class="message"></div>
    </div>

    <!-- Footer Menu -->
    <footer class="footer-menu">
        <div class="footer-nav">
            <a href="user/dashboard.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                </svg>
                <span>Dashboard</span>
            </a>
            <a href="user/wallet.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 18v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M16 8h4a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-4" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="16" cy="12" r="1" fill="currentColor"/>
                </svg>
                <span>Wallet</span>
            </a>
            <a href="user/transactions.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="M9 12h6" stroke="currentColor" stroke-width="2"/>
                    <path d="M9 16h6" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Transactions</span>
            </a>
            <a href="admin.php" class="footer-btn active">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                </svg>
                <span>Admin</span>
            </a>
            <a href="user/profile.php" class="footer-btn">
                <svg class="footer-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
                <span>Profile</span>
            </a>
        </div>
    </footer>    <script src="user/js/admin.js"></script>
</body>
</html>
