<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Parse Error Fix Demonstration</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .btn { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 14px;
            background: #28a745;
            color: white;
        }
        .btn:hover { background: #218838; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .result {
            background: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .before { background: #f8d7da; }
        .after { background: #d4edda; }
        .console {
            background: #343a40;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Investment JSON Parse Error - FIX DEMONSTRATION</h1>
        <p>This page demonstrates that the JSON parse error has been fixed.</p>

        <div class="test-section">
            <h3>🔧 The Fix Applied</h3>
            <p><strong>Problem:</strong> Investment creation was returning HTML error pages instead of JSON, causing <code>SyntaxError: Unexpected token '&lt;'</code></p>
            <p><strong>Solution:</strong> Fixed parameter mapping and endpoint URLs throughout the API chain</p>
        </div>

        <div class="test-section">
            <h3>📋 Before vs After</h3>
            
            <h4>Before (Broken):</h4>
            <div class="result before">❌ Frontend → AJAX → Wrong Parameters → HTML Error → JSON Parse Error</div>
            
            <h4>After (Fixed):</h4>
            <div class="result after">✅ Frontend → AJAX → Correct Parameters → JSON Response → Success</div>
        </div>

        <div class="test-section">
            <h3>🧪 Live Test</h3>
            <p>Test the investment creation API with the exact same call that was failing before:</p>
            <button class="btn" onclick="testInvestmentCreation()">Test Investment API (Same Call That Failed Before)</button>
            <div id="testResult"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test Results Log</h3>
            <div id="console" class="console"></div>
        </div>
    </div>

    <script>
        // Console logging
        const consoleDiv = document.getElementById('console');
        
        function log(message, type = 'INFO') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type}: ${message}\n`;
            consoleDiv.textContent += logEntry;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            console.log(logEntry);
        }

        function showResult(message, isSuccess = false) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = `<div class="result ${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }

        async function testInvestmentCreation() {
            log('🧪 Starting Investment Creation Test...');
            log('This is the EXACT same API call that was failing before with JSON parse error');
            
            // This is the exact same API call that was causing the JSON parse error
            const testData = {
                action: 'create_investment',
                amount: 700,
                plan: 'basic'
            };
            
            log('📤 Sending request data: ' + JSON.stringify(testData));
            
            try {
                log('🌐 Making AJAX call to frontend/ajax.php...');
                
                const response = await fetch('http://localhost:8080/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                log(`📡 Response received - Status: ${response.status}`);
                log(`📋 Response headers: ${response.headers.get('content-type')}`);
                
                // Get the response text first to see what we're getting
                const responseText = await response.text();
                log(`📄 Raw response (first 200 chars): ${responseText.substring(0, 200)}`);
                
                // Try to parse as JSON
                let result;
                try {
                    result = JSON.parse(responseText);
                    log('✅ SUCCESS: Response parsed as valid JSON!');
                    log('📋 Parsed response: ' + JSON.stringify(result, null, 2));
                    
                    if (result.error) {
                        if (result.error.includes('authenticated')) {
                            showResult('✅ PERFECT! API returned valid JSON with authentication error.\n\n' +
                                     'Before: HTML error page → JSON parse error\n' +
                                     'After: Valid JSON response → Proper error handling\n\n' +
                                     'The JSON parse error is FIXED!', true);
                            log('🎉 JSON parse error is FIXED! Authentication error is expected without session.');
                        } else {
                            showResult('✅ API returned valid JSON with error: ' + result.error, true);
                            log('✅ Valid JSON response with error: ' + result.error);
                        }
                    } else {
                        showResult('✅ API call successful! Investment would be created.', true);
                        log('🎉 Investment creation successful!');
                    }
                    
                } catch (parseError) {
                    log('❌ JSON Parse Error: ' + parseError.message);
                    log('❌ This means the fix did not work completely');
                    
                    // Check if it's an HTML response
                    if (responseText.includes('<') || responseText.includes('<!DOCTYPE')) {
                        log('❌ Response appears to be HTML, not JSON');
                        showResult('❌ Still receiving HTML response instead of JSON.\n\nResponse: ' + responseText.substring(0, 300), false);
                    } else {
                        log('❌ Response is not HTML but still not valid JSON');
                        showResult('❌ Response is not valid JSON but also not HTML.\n\nResponse: ' + responseText, false);
                    }
                }
                
            } catch (networkError) {
                log('❌ Network Error: ' + networkError.message);
                showResult('❌ Network error: ' + networkError.message + '\n\nMake sure both servers are running:\n- Backend: php -S localhost:8000 src/index.php\n- Frontend: php -S localhost:8080', false);
            }
            
            log('🏁 Test completed');
        }

        // Initialize
        log('🚀 JSON Parse Error Fix Demonstration initialized');
        log('Backend should be running on: http://localhost:8000');
        log('Frontend should be running on: http://localhost:8080');
        log('Ready to test the investment creation fix!');
    </script>
</body>
</html>
