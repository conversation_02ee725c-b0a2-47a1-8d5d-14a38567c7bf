<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Investment Statistics Authentication Fix</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .btn { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 14px;
            background: #007bff;
            color: white;
        }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .loading { background: #e2e3e5; color: #383d41; }
        input, select { 
            padding: 8px; 
            margin: 5px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
        }
        .test-status {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
            margin-right: 10px;
            font-weight: bold;
            color: white;
        }
        .test-pass { background: #28a745; }
        .test-fail { background: #dc3545; }
        .test-pending { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Investment Statistics Authentication Fix Test</h1>
        <p>Testing the fix for "User not authenticated" error on get_investment_statistics API call.</p>

        <div class="test-section">
            <h3>🎯 Fix Applied</h3>
            <p><strong>Issue:</strong> <code>getInvestmentStatistics()</code> method wasn't receiving the authenticated user parameter</p>
            <p><strong>Fix:</strong> Updated route to pass <code>$user</code> parameter to the method</p>
            <div class="result info">
<strong>Files Modified:</strong>
1. backend/src/Controllers/InvestmentController.php
   - Updated getInvestmentStatistics() to accept $user parameter
   - Added dual authentication support (JWT + session)

2. backend/src/index.php
   - Updated route to pass authenticated user to method
            </div>
        </div>

        <div class="test-section">
            <h3>Step 1: Login and Authentication</h3>
            <div>
                <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
                <input type="password" id="loginPassword" placeholder="Password" value="password123">
                <button class="btn" onclick="performLogin()">Login</button>
            </div>
            <div id="loginResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 2: Test Investment Statistics (The Fixed Endpoint)</h3>
            <button class="btn" onclick="testInvestmentStatistics()" id="statsBtn" disabled>Test Investment Statistics</button>
            <div id="statisticsResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 3: Test Other Investment Endpoints (For Comparison)</h3>
            <button class="btn" onclick="testActiveInvestments()" id="activeBtn" disabled>Test Active Investments</button>
            <button class="btn" onclick="testInvestmentHistory()" id="historyBtn" disabled>Test Investment History</button>
            <div id="comparisonResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 4: Dashboard Integration Test</h3>
            <button class="btn" onclick="testDashboardIntegration()" id="dashboardBtn" disabled>Test Dashboard Load</button>
            <div id="dashboardResult"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test Results Summary</h3>
            <div id="summaryResult">
                <p>Run tests to see results...</p>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            login: null,
            statistics: null,
            activeInvestments: null,
            investmentHistory: null,
            dashboardIntegration: null
        };

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function updateTestStatus(testName, passed) {
            testResults[testName] = passed;
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('summaryResult');
            const results = Object.entries(testResults).map(([test, status]) => {
                let statusIcon, statusClass;
                if (status === null) {
                    statusIcon = '⏳';
                    statusClass = 'test-pending';
                } else if (status === true) {
                    statusIcon = '✅';
                    statusClass = 'test-pass';
                } else {
                    statusIcon = '❌';
                    statusClass = 'test-fail';
                }
                
                return `<div><span class="test-status ${statusClass}">${statusIcon}</span> ${test.replace(/([A-Z])/g, ' $1').trim()}</div>`;
            }).join('');

            const passedTests = Object.values(testResults).filter(result => result === true).length;
            const totalTests = Object.keys(testResults).length;
            const completedTests = Object.values(testResults).filter(result => result !== null).length;

            summary.innerHTML = `
                <div class="result info">
                    <strong>Test Progress: ${completedTests}/${totalTests} completed</strong><br>
                    <strong>Passed: ${passedTests}/${completedTests || 1}</strong><br><br>
                    ${results}
                </div>
            `;
        }

        async function performLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                showResult('loginResult', 'Attempting login...', 'loading');
                
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    updateTestStatus('login', true);
                    showResult('loginResult', 
                        `✅ Login successful!
User ID: ${result.user?.id}
Email: ${result.user?.email}
Token: ${result.token ? 'Present' : 'Missing'}`, 
                        'success'
                    );
                    
                    // Enable other test buttons
                    document.getElementById('statsBtn').disabled = false;
                    document.getElementById('activeBtn').disabled = false;
                    document.getElementById('historyBtn').disabled = false;
                    document.getElementById('dashboardBtn').disabled = false;
                } else {
                    updateTestStatus('login', false);
                    showResult('loginResult', `❌ Login failed: ${result.error}`, 'error');
                }
            } catch (error) {
                updateTestStatus('login', false);
                showResult('loginResult', `❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testInvestmentStatistics() {
            try {
                showResult('statisticsResult', '🔄 Testing investment statistics (THE FIX)...', 'loading');
                
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_investment_statistics'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    updateTestStatus('statistics', true);
                    showResult('statisticsResult', 
                        `✅ Investment Statistics SUCCESS! (Fix worked!)
                        
Statistics:
${JSON.stringify(result.statistics, null, 2)}

🎉 The "User not authenticated" error has been resolved!`, 
                        'success'
                    );
                } else if (result.error && result.error.includes('authenticated')) {
                    updateTestStatus('statistics', false);
                    showResult('statisticsResult', 
                        `❌ Investment Statistics FAILED: ${result.error}
                        
⚠️  The fix may not be working correctly.
Please check the backend logs for more details.`, 
                        'error'
                    );
                } else {
                    updateTestStatus('statistics', false);
                    showResult('statisticsResult', `❌ Investment Statistics error: ${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                updateTestStatus('statistics', false);
                showResult('statisticsResult', `❌ Investment Statistics error: ${error.message}`, 'error');
            }
        }

        async function testActiveInvestments() {
            try {
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_active_investments'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    updateTestStatus('activeInvestments', true);
                    showResult('comparisonResult', 
                        `✅ Active Investments working (${result.investments?.length || 0} investments found)`, 
                        'success'
                    );
                } else {
                    updateTestStatus('activeInvestments', false);
                    showResult('comparisonResult', `❌ Active Investments failed: ${result.error}`, 'error');
                }
            } catch (error) {
                updateTestStatus('activeInvestments', false);
                showResult('comparisonResult', `❌ Active Investments error: ${error.message}`, 'error');
            }
        }

        async function testInvestmentHistory() {
            try {
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_investment_history'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    updateTestStatus('investmentHistory', true);
                    const existingResult = document.getElementById('comparisonResult').innerHTML;
                    showResult('comparisonResult', 
                        existingResult + `\n✅ Investment History working (${result.history?.length || 0} records found)`, 
                        'success'
                    );
                } else {
                    updateTestStatus('investmentHistory', false);
                    const existingResult = document.getElementById('comparisonResult').innerHTML;
                    showResult('comparisonResult', 
                        existingResult + `\n❌ Investment History failed: ${result.error}`, 
                        'error'
                    );
                }
            } catch (error) {
                updateTestStatus('investmentHistory', false);
                const existingResult = document.getElementById('comparisonResult').innerHTML;
                showResult('comparisonResult', 
                    existingResult + `\n❌ Investment History error: ${error.message}`, 
                    'error'
                );
            }
        }

        async function testDashboardIntegration() {
            try {
                showResult('dashboardResult', '🔄 Testing dashboard integration...', 'loading');
                
                // Simulate the dashboard loading investment statistics
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_investment_statistics'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    updateTestStatus('dashboardIntegration', true);
                    showResult('dashboardResult', 
                        `✅ Dashboard Integration SUCCESS!
                        
The dashboard can now successfully load investment statistics without authentication errors.

Statistics that will be displayed:
- Total Invested: ${result.statistics.total_invested} USDT
- Total Earned: ${result.statistics.total_earned} USDT  
- Active Investments: ${result.statistics.active_investments}
- Completed Investments: ${result.statistics.completed_investments}

🎉 Users will no longer see "User not authenticated" errors on the dashboard!`, 
                        'success'
                    );
                } else {
                    updateTestStatus('dashboardIntegration', false);
                    showResult('dashboardResult', 
                        `❌ Dashboard Integration FAILED: ${result.error}
                        
The dashboard will still show authentication errors.`, 
                        'error'
                    );
                }
            } catch (error) {
                updateTestStatus('dashboardIntegration', false);
                showResult('dashboardResult', `❌ Dashboard Integration error: ${error.message}`, 'error');
            }
        }

        // Initialize
        updateSummary();
        console.log('Investment Statistics Authentication Fix Test loaded');
        console.log('Click "Login" to start testing the fix');
    </script>
</body>
</html>
