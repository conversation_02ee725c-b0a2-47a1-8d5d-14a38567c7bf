<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Balance Deduction Diagnosis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #b8daff; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .balance-display { font-size: 18px; font-weight: bold; text-align: center; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; margin: 20px 0; }
        input[type="number"] { padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💰 Balance Deduction Diagnosis</h1>
        <p>This tool helps diagnose why "Failed to deduct balance" error occurs during investment creation.</p>
        
        <div class="balance-display" id="balanceDisplay">
            Available Balance: Loading...
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runDiagnosis()" id="diagnosisBtn">Run Full Diagnosis</button>
            <button onclick="testInvestment()" id="investBtn" disabled>Test Investment</button>
        </div>
        
        <div style="text-align: center; margin: 10px 0;">
            <input type="number" id="testAmount" placeholder="Test amount" value="25" min="1" max="1000">
            <label>USDT to test investment</label>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        let currentBalance = 0;
        
        function addStatus(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function runDiagnosis() {
            const balanceDisplay = document.getElementById('balanceDisplay');
            const results = document.getElementById('results');
            
            results.innerHTML = '';
            balanceDisplay.textContent = 'Available Balance: Testing...';
            
            try {
                addStatus('🔍 Starting balance deduction diagnosis...', 'info');
                
                // Step 1: Login
                addStatus('Step 1: Authenticating...', 'info');
                const loginResponse = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const loginResult = await loginResponse.json();
                
                if (!loginResult.success) {
                    addStatus('❌ Authentication failed: ' + (loginResult.error || 'Unknown error'), 'error');
                    balanceDisplay.textContent = 'Available Balance: Auth Failed';
                    return;
                }
                
                addStatus('✅ Authentication successful!', 'success');
                
                // Step 2: Get current balance
                addStatus('Step 2: Checking current balance...', 'info');
                const balanceResponse = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'get_balance' })
                });
                
                const balanceResult = await balanceResponse.json();
                
                if (balanceResult.success) {
                    currentBalance = parseFloat(balanceResult.balance) || 0;
                    balanceDisplay.textContent = `Available Balance: ${currentBalance.toFixed(2)} USDT`;
                    
                    addStatus(`✅ Current available balance: ${currentBalance} USDT`, 'success');
                    
                    // Step 3: Analyze the balance situation
                    addStatus('Step 3: Analyzing balance situation...', 'info');
                    
                    if (currentBalance === 30) {
                        addStatus('🎯 Balance is correct: 30 USDT (630 wallet - 600 investments)', 'success');
                        addStatus('ℹ️ This means user can only invest up to 30 USDT with current balance', 'info');
                    } else if (currentBalance === 630) {
                        addStatus('⚠️ Balance shows raw wallet amount (630). Fix may not be active.', 'warning');
                    } else if (currentBalance === 0) {
                        addStatus('❌ Balance is 0. This will cause all investments to fail.', 'error');
                    } else {
                        addStatus(`ℹ️ Available balance: ${currentBalance} USDT`, 'info');
                    }
                    
                    // Step 4: Explain deduction logic
                    addStatus('Step 4: Balance deduction logic explained...', 'info');
                    addStatus(`<strong>WalletService::deductBalance() logic:</strong><br>
                              1. Check: current_balance (${currentBalance}) >= investment_amount<br>
                              2. If YES: Create deduction transaction<br>
                              3. If NO: Return false → "Failed to deduct balance"`, 'info');
                    
                    // Step 5: Show recommended investment amounts
                    if (currentBalance > 0) {
                        addStatus(`✅ You can safely invest up to ${currentBalance.toFixed(2)} USDT`, 'success');
                        addStatus(`💡 Try amounts like: ${Math.min(10, currentBalance).toFixed(2)}, ${Math.min(20, currentBalance).toFixed(2)}, or ${currentBalance.toFixed(2)} USDT`, 'info');
                    } else {
                        addStatus('❌ Cannot invest with 0 balance. Need to add funds first.', 'error');
                    }
                    
                    // Enable test button
                    document.getElementById('investBtn').disabled = false;
                    
                } else {
                    const errorMsg = balanceResult.error || balanceResult.message || 'Unknown error';
                    addStatus(`❌ Balance check failed: ${errorMsg}`, 'error');
                    balanceDisplay.textContent = 'Available Balance: Error';
                }
                
            } catch (error) {
                addStatus(`❌ Diagnosis error: ${error.message}`, 'error');
                balanceDisplay.textContent = 'Available Balance: Error';
            }
        }

        async function testInvestment() {
            const testAmount = parseFloat(document.getElementById('testAmount').value) || 25;
            
            try {
                addStatus(`🧪 Testing investment of ${testAmount} USDT...`, 'info');
                
                if (testAmount > currentBalance) {
                    addStatus(`⚠️ Warning: Test amount (${testAmount}) > Available balance (${currentBalance})`, 'warning');
                    addStatus('❌ Expected result: "Failed to deduct balance"', 'warning');
                } else {
                    addStatus(`✅ Test amount (${testAmount}) <= Available balance (${currentBalance})`, 'success');
                    addStatus('✅ Expected result: Investment should succeed', 'success');
                }
                
                const response = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'create_investment',
                        amount: testAmount,
                        plan: 'basic'
                    })
                });
                
                const result = await response.json();
                
                addStatus('Investment API Response:', 'info');
                addStatus(`<pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                
                if (result.success) {
                    addStatus(`✅ Investment successful! Investment ID: ${result.investment_id}`, 'success');
                    addStatus('💡 The balance deduction worked correctly!', 'success');
                    
                    // Refresh balance
                    setTimeout(() => {
                        runDiagnosis();
                    }, 1000);
                } else {
                    const errorMsg = result.error || result.message || 'Unknown error';
                    if (errorMsg.includes('Failed to deduct balance')) {
                        addStatus(`❌ Confirmed: "Failed to deduct balance" error`, 'error');
                        addStatus(`💡 This happens because ${testAmount} USDT > ${currentBalance} USDT available`, 'info');
                    } else {
                        addStatus(`❌ Investment failed: ${errorMsg}`, 'error');
                    }
                }
                
            } catch (error) {
                addStatus(`❌ Test error: ${error.message}`, 'error');
            }
        }

        // Auto-run diagnosis when page loads
        window.addEventListener('load', () => {
            setTimeout(runDiagnosis, 1000);
        });
    </script>
</body>
</html>
