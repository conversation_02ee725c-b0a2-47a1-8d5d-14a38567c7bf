<?php

namespace Simbi\Tls\Services;

use Exception;

class TronGridService
{
    private string $apiKey;
    private string $baseUrl;
    private string $usdtContract;    public function __construct()
    {
        \Simbi\Tls\Config\Config::load();
        
        $this->apiKey = \Simbi\Tls\Config\Config::get('TRON_GRID_API_KEY', '');
          // Get network from environment and set appropriate base URL
        $network = \Simbi\Tls\Config\Config::get('TRON_NETWORK', 'nile');
        $this->baseUrl = match($network) {
            'mainnet' => \Simbi\Tls\Config\Config::get('TRON_MAINNET_URL', 'https://api.trongrid.io'),
            'shasta' => \Simbi\Tls\Config\Config::get('TRON_SHASTA_URL', 'https://api.shasta.trongrid.io'),
            'nile' => \Simbi\Tls\Config\Config::get('TRON_NILE_URL', 'https://nile.trongrid.io'),
            default => \Simbi\Tls\Config\Config::get('TRON_BASE_URL', 'https://nile.trongrid.io')
        };
        
        // Get network-specific contract from environment variables
        $networkContract = match($network) {
            'mainnet' => \Simbi\Tls\Config\Config::get('USDT_CONTRACT_MAINNET', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'),
            'nile' => \Simbi\Tls\Config\Config::get('USDT_CONTRACT_NILE', 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'),
            'shasta' => \Simbi\Tls\Config\Config::get('USDT_CONTRACT_SHASTA', 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs'),
            default => \Simbi\Tls\Config\Config::get('USDT_CONTRACT_NILE', 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf')
        };
        
        // Allow override with legacy USDT_CONTRACT environment variable
        $this->usdtContract = \Simbi\Tls\Config\Config::get('USDT_CONTRACT', $networkContract);
    }

    /**
     * Verify a USDT transaction on TRON network
     */
    public function verifyTransaction(string $walletAddress, float $amount, string $txHash = null): array
    {
        try {
            // Get recent transactions for the wallet address
            $transactions = $this->getAccountTransactions($walletAddress);
            
            if (empty($transactions)) {
                return [
                    'success' => false,
                    'message' => 'No transactions found for this wallet',
                    'verified' => false
                ];
            }

            // Search for matching transaction
            $matchingTx = $this->findMatchingTransaction($transactions, $amount, $txHash);
            
            if ($matchingTx) {
                return [
                    'success' => true,
                    'message' => 'Transaction verified successfully',
                    'verified' => true,
                    'transaction' => $matchingTx,
                    'confirmation_count' => $matchingTx['confirmations'] ?? 0
                ];
            }

            return [
                'success' => false,
                'message' => 'No matching transaction found',
                'verified' => false,
                'searched_amount' => $amount,
                'searched_hash' => $txHash
            ];

        } catch (Exception $e) {
            error_log("TronGrid verification error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error verifying transaction: ' . $e->getMessage(),
                'verified' => false
            ];
        }
    }

    /**
     * Get account transactions from TronGrid
     */
    private function getAccountTransactions(string $address, int $limit = 50): array
    {
        try {
            // Get TRC20 transfers (USDT transactions)
            $url = $this->baseUrl . "/v1/accounts/{$address}/transactions/trc20";
            $params = [
                'limit' => $limit,
                'contract_address' => $this->usdtContract,
                'order_by' => 'block_timestamp,desc'
            ];

            $response = $this->makeApiCall($url, $params);
            
            if (!$response || !isset($response['data'])) {
                return [];
            }

            return $response['data'];

        } catch (Exception $e) {
            error_log("TronGrid API error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Make API call to TronGrid
     */
    private function makeApiCall(string $url, array $params = []): ?array
    {
        $fullUrl = $url;
        if (!empty($params)) {
            $fullUrl .= '?' . http_build_query($params);
        }

        $headers = [
            'Accept: application/json',
            'Content-Type: application/json'
        ];

        // Add API key if available
        if (!empty($this->apiKey)) {
            $headers[] = 'TRON-PRO-API-KEY: ' . $this->apiKey;
        }

        $ch = \curl_init();
        \curl_setopt_array($ch, [
            CURLOPT_URL => $fullUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_USERAGENT => 'TLS-Wallet-API/1.0'
        ]);

        $response = \curl_exec($ch);
        $httpCode = \curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = \curl_error($ch);
        \curl_close($ch);

        if ($error) {
            throw new Exception("cURL error: " . $error);
        }

        if ($httpCode !== 200) {
            throw new Exception("TronGrid API returned HTTP {$httpCode}: " . $response);
        }

        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response from TronGrid");
        }

        return $data;
    }

    /**
     * Find matching transaction in the list
     */
    private function findMatchingTransaction(array $transactions, float $expectedAmount, string $expectedHash = null): ?array
    {
        $expectedAmountRaw = $this->convertToRawAmount($expectedAmount);
        
        foreach ($transactions as $tx) {
            // Skip if transaction is too old (more than 24 hours)
            if (isset($tx['block_timestamp']) && $tx['block_timestamp'] < (time() - 86400) * 1000) {
                continue;
            }

            // If hash is provided, match exactly
            if ($expectedHash && isset($tx['transaction_id'])) {
                if ($tx['transaction_id'] === $expectedHash && $this->matchesAmount($tx, $expectedAmountRaw)) {
                    return $this->formatTransaction($tx);
                }
                continue;
            }

            // Match by amount and other criteria
            if ($this->matchesAmount($tx, $expectedAmountRaw) && $this->isValidDeposit($tx)) {
                return $this->formatTransaction($tx);
            }
        }

        return null;
    }

    /**
     * Check if transaction matches expected amount
     */
    private function matchesAmount(array $tx, string $expectedAmountRaw): bool
    {
        if (!isset($tx['value'])) {
            return false;
        }

        $txAmount = $tx['value'];
        
        // Allow for small variations due to precision
        $tolerance = '100000'; // 0.1 USDT tolerance
        
        return abs((float)$txAmount - (float)$expectedAmountRaw) <= (float)$tolerance;
    }

    /**
     * Check if transaction is a valid deposit
     */
    private function isValidDeposit(array $tx): bool
    {
        // Check if transaction is confirmed
        if (!isset($tx['confirmed']) || !$tx['confirmed']) {
            return false;
        }

        // Check if it's a TRC20 transfer
        if (!isset($tx['token_info']['symbol']) || $tx['token_info']['symbol'] !== 'USDT') {
            return false;
        }

        // Additional validation can be added here
        return true;
    }

    /**
     * Convert USDT amount to raw format (6 decimals)
     */
    private function convertToRawAmount(float $amount): string
    {
        return (string)($amount * 1000000);
    }

    /**
     * Format transaction for response
     */
    private function formatTransaction(array $tx): array
    {
        $amount = isset($tx['value']) ? (float)$tx['value'] / 1000000 : 0;
        
        return [
            'transaction_id' => $tx['transaction_id'] ?? '',
            'from_address' => $tx['from'] ?? '',
            'to_address' => $tx['to'] ?? '',
            'amount' => $amount,
            'amount_raw' => $tx['value'] ?? '0',
            'block_number' => $tx['block'] ?? 0,
            'block_timestamp' => $tx['block_timestamp'] ?? 0,
            'confirmed' => $tx['confirmed'] ?? false,
            'confirmations' => $this->calculateConfirmations($tx),
            'token_symbol' => $tx['token_info']['symbol'] ?? 'USDT',
            'contract_address' => $tx['token_info']['address'] ?? $this->usdtContract
        ];
    }

    /**
     * Calculate confirmation count
     */
    private function calculateConfirmations(array $tx): int
    {
        if (!isset($tx['block_timestamp']) || !$tx['confirmed']) {
            return 0;
        }

        // Estimate confirmations based on time (TRON blocks are ~3 seconds)
        $blockTime = 3; // seconds
        $timeDiff = time() - ($tx['block_timestamp'] / 1000);
        
        return max(0, (int)($timeDiff / $blockTime));
    }

    /**
     * Get transaction details by hash
     */
    public function getTransactionByHash(string $txHash): ?array
    {
        try {
            $url = $this->baseUrl . "/v1/transactions/{$txHash}";
            $response = $this->makeApiCall($url);
            
            if (!$response || !isset($response['data'])) {
                return null;
            }

            return $response['data'][0] ?? null;

        } catch (Exception $e) {
            error_log("TronGrid transaction lookup error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if TronGrid API is accessible
     */
    public function healthCheck(): array
    {
        try {
            $url = $this->baseUrl . "/healthz";
            $response = $this->makeApiCall($url);
            
            return [
                'success' => true,
                'status' => 'healthy',
                'api_accessible' => true
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'status' => 'error',
                'api_accessible' => false,
                'error' => $e->getMessage()
            ];
        }
    }    /**
     * Get USDT balance for a wallet address from TronGrid
     */
    public function getUSDTBalance(string $address): array
    {
        try {
            // Use the TRC20 transactions endpoint to calculate balance from transaction history
            $url = $this->baseUrl . "/v1/accounts/{$address}/transactions/trc20";
            $params = [
                'limit' => 200, // Get more transactions to ensure accurate balance
                'contract_address' => $this->usdtContract,
                'order_by' => 'block_timestamp,desc'
            ];

            $response = $this->makeApiCall($url, $params);
            
            if (!$response || !isset($response['data'])) {
                return [
                    'success' => true,
                    'balance' => '0.000000',
                    'balance_raw' => '0',
                    'contract' => $this->usdtContract,
                    'address' => $address,
                    'source' => 'trongrid',
                    'note' => 'No transactions found'
                ];
            }

            // Calculate balance from transaction history
            $balance = 0;
            $transactionCount = 0;
            
            foreach ($response['data'] as $tx) {
                // Only process USDT transactions for the correct contract
                if ($tx['token_info']['symbol'] === 'USDT' && 
                    $tx['token_info']['address'] === $this->usdtContract) {
                    
                    $amount = (float)$tx['value'] / 1000000; // USDT has 6 decimals
                    
                    if ($tx['to'] === $address) {
                        $balance += $amount; // Incoming
                    } elseif ($tx['from'] === $address) {
                        $balance -= $amount; // Outgoing
                    }
                    
                    $transactionCount++;
                }
            }
            
            // Format balance
            $formattedBalance = number_format($balance, 6, '.', '');
            $rawBalance = (string)($balance * 1000000);
            
            return [
                'success' => true,
                'balance' => $formattedBalance,
                'balance_raw' => $rawBalance,
                'contract' => $this->usdtContract,
                'address' => $address,
                'source' => 'trongrid',
                'transaction_count' => $transactionCount,
                'calculated_from_history' => true
            ];
            
        } catch (Exception $e) {
            error_log("TronGrid balance check error: " . $e->getMessage());
            
            // Fallback: return zero balance but mark as successful with a note
            return [
                'success' => true,
                'balance' => '0.000000',
                'balance_raw' => '0',
                'contract' => $this->usdtContract,
                'address' => $address,
                'source' => 'fallback',
                'note' => 'TronGrid unavailable, using fallback'
            ];
        }
    }
}
