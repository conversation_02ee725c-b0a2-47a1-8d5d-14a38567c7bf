<?php
/**
 * Check what's in the transactions table for the test user
 */

require_once 'backend/vendor/autoload.php';

try {
    $pdo = \Simbi\Tls\Config\Database::getConnection();
    
    echo "=== TRANSACTIONS TABLE ANALYSIS ===\n\n";
    
    // Check all transactions for test user
    $stmt = $pdo->prepare('SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC');
    $stmt->execute([2]); // Test user ID
    $transactions = $stmt->fetchAll();
    
    echo "Total transactions for user 2: " . count($transactions) . "\n\n";
    
    if (count($transactions) > 0) {
        foreach ($transactions as $tx) {
            echo "Transaction ID: {$tx['id']}\n";
            echo "Type: {$tx['type']}\n";
            echo "Amount: {$tx['amount']}\n";
            echo "Status: {$tx['status']}\n";
            echo "Created: {$tx['created_at']}\n";
            echo "---\n";
        }
    } else {
        echo "❌ NO TRANSACTIONS FOUND!\n";
        echo "This explains why WalletService::getBalance() returns 0\n";
    }
    
    // Test the actual WalletService balance calculation
    echo "\n=== TESTING WALLETSERVICE BALANCE CALCULATION ===\n";
    
    $stmt = $pdo->prepare("
        SELECT 
            COALESCE(SUM(CASE 
                WHEN type = 'deposit' THEN amount 
                WHEN type = 'withdrawal' THEN -amount 
                WHEN type = 'investment' THEN -amount 
                ELSE 0 
            END), 0) as calculated_balance 
        FROM transactions 
        WHERE user_id = ? AND status = 'confirmed'
    ");
    $stmt->execute([2]);
    $result = $stmt->fetch();
    
    $calculatedBalance = $result ? floatval($result['calculated_balance']) : 0;
    echo "WalletService calculated balance: $calculatedBalance\n";
    
    // Compare with wallets table
    $stmt = $pdo->prepare('SELECT balance FROM wallets WHERE user_id = ?');
    $stmt->execute([2]);
    $walletRow = $stmt->fetch();
    $walletBalance = $walletRow ? floatval($walletRow['balance']) : 0;
    
    echo "Wallets table balance: $walletBalance\n";
    
    echo "\n=== ROOT CAUSE IDENTIFIED ===\n";
    echo "The issue is that:\n";
    echo "1. ✅ Wallets table has balance: $walletBalance\n";
    echo "2. ❌ WalletService calculates from transactions: $calculatedBalance\n";
    echo "3. ❌ No transactions exist, so calculated balance = 0\n";
    echo "4. ❌ Investment check: 0 < 630 = insufficient balance\n";
    
    echo "\n=== SOLUTION NEEDED ===\n";
    echo "Either:\n";
    echo "A. Fix WalletService to use wallets.balance directly\n";
    echo "B. Create initial deposit transaction for test user\n";
    echo "C. Update balance calculation logic\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

?>
