<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Investment Deduction Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; border-left: 4px solid #ddd; }
        .status.success { background: #d4edda; border-color: #28a745; color: #155724; }
        .status.error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .status.warning { background: #fff3cd; border-color: #ffc107; color: #856404; }
        .status.info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        .balance-display { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center; font-size: 18px; font-weight: bold; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input[type="number"] { padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; margin: 5px; }
        .test-summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Investment Deduction Fix Verification</h1>
        <p>This test verifies that the "Failed to deduct balance" error has been resolved.</p>
        
        <div class="balance-display" id="balanceDisplay">
            Available Balance: Loading...
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runFullTest()" id="testBtn">Run Complete Test</button>
            <button onclick="testSpecificAmount()" id="specificBtn" disabled>Test Specific Amount</button>
        </div>
        
        <div style="text-align: center; margin: 10px 0;">
            <input type="number" id="testAmount" placeholder="Amount to test" value="25" min="1" max="100">
            <label>USDT</label>
        </div>
        
        <div id="testResults"></div>
        
        <div class="test-summary" id="testSummary" style="display: none;">
            <h3>Test Summary</h3>
            <div id="summaryContent"></div>
        </div>
    </div>

    <script>
        let currentBalance = 0;
        let testsPassed = 0;
        let totalTests = 0;

        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            document.getElementById('testResults').appendChild(div);
            div.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        async function apiCall(action, data = {}) {
            const response = await fetch('frontend/ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action, ...data })
            });
            return await response.json();
        }

        async function authenticate() {
            addStatus('🔐 Step 1: Authenticating...', 'info');
            
            const loginResult = await apiCall('login', {
                email: '<EMAIL>',
                password: 'password123'
            });
            
            if (loginResult.success) {
                addStatus('✅ Authentication successful!', 'success');
                return true;
            } else {
                addStatus(`❌ Authentication failed: ${loginResult.error || 'Unknown error'}`, 'error');
                return false;
            }
        }

        async function getBalance() {
            addStatus('💰 Step 2: Getting current balance...', 'info');
            
            const balanceResult = await apiCall('get_balance');
            
            if (balanceResult.success) {
                currentBalance = parseFloat(balanceResult.balance) || 0;
                document.getElementById('balanceDisplay').textContent = `Available Balance: ${currentBalance.toFixed(2)} USDT`;
                
                addStatus(`✅ Current balance: ${currentBalance} USDT`, 'success');
                
                if (currentBalance === 30) {
                    addStatus('🎯 Balance is correct: 30 USDT (630 wallet - 600 investments)', 'success');
                } else if (currentBalance > 0) {
                    addStatus(`ℹ️ Available balance: ${currentBalance} USDT`, 'info');
                } else {
                    addStatus('⚠️ Balance is 0 - may need to add funds or check setup', 'warning');
                }
                
                return true;
            } else {
                addStatus(`❌ Balance check failed: ${balanceResult.error || 'Unknown error'}`, 'error');
                return false;
            }
        }

        async function testInvestment(amount) {
            totalTests++;
            addStatus(`🧪 Step 3: Testing investment of ${amount} USDT...`, 'info');
            
            // Predict expected result
            if (amount > currentBalance) {
                addStatus(`⚠️ Expected: Should fail with "Insufficient balance" (${amount} > ${currentBalance})`, 'warning');
            } else {
                addStatus(`✅ Expected: Should succeed (${amount} <= ${currentBalance})`, 'success');
            }
            
            const investResult = await apiCall('create_investment', {
                amount: amount,
                plan: 'basic'
            });
            
            addStatus('📋 Investment API Response:', 'info');
            addStatus(`<pre>${JSON.stringify(investResult, null, 2)}</pre>`, 'info');
            
            if (investResult.success) {
                addStatus(`✅ Investment successful! ID: ${investResult.investment_id}`, 'success');
                
                if (amount <= currentBalance) {
                    addStatus('🎉 PERFECT! Investment succeeded as expected!', 'success');
                    testsPassed++;
                } else {
                    addStatus('⚠️ Investment succeeded but should have failed (amount > balance)', 'warning');
                }
                
                // Update balance after successful investment
                currentBalance -= amount;
                document.getElementById('balanceDisplay').textContent = `Available Balance: ${currentBalance.toFixed(2)} USDT`;
                
            } else {
                const errorMsg = investResult.error || investResult.message || 'Unknown error';
                
                if (errorMsg.includes('Insufficient balance')) {
                    if (amount > currentBalance) {
                        addStatus('✅ CORRECT! Investment failed with "Insufficient balance" as expected', 'success');
                        testsPassed++;
                    } else {
                        addStatus('❌ Investment failed but should have succeeded', 'error');
                    }
                } else if (errorMsg.includes('Failed to deduct balance')) {
                    addStatus('❌ CRITICAL: Still getting "Failed to deduct balance" error!', 'error');
                    addStatus('💡 This means our fix didn\'t work completely', 'error');
                } else {
                    addStatus(`❌ Investment failed with unexpected error: ${errorMsg}`, 'error');
                }
            }
        }

        async function runFullTest() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('testSummary').style.display = 'none';
            testsPassed = 0;
            totalTests = 0;
            
            try {
                const authSuccess = await authenticate();
                if (!authSuccess) return;
                
                const balanceSuccess = await getBalance();
                if (!balanceSuccess) return;
                
                // Enable specific test button
                document.getElementById('specificBtn').disabled = false;
                
                // Test various amounts
                const testAmounts = [
                    Math.min(10, currentBalance), // Small valid amount
                    Math.min(currentBalance * 0.8, currentBalance), // 80% of balance
                    currentBalance, // Exact balance
                    currentBalance + 10 // Over balance
                ];
                
                for (const amount of testAmounts) {
                    if (amount > 0) {
                        await testInvestment(amount);
                        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait between tests
                    }
                }
                
                // Show summary
                showTestSummary();
                
            } catch (error) {
                addStatus(`❌ Test error: ${error.message}`, 'error');
            }
        }

        async function testSpecificAmount() {
            const amount = parseFloat(document.getElementById('testAmount').value);
            if (!amount || amount <= 0) {
                addStatus('❌ Please enter a valid amount', 'error');
                return;
            }
            
            await testInvestment(amount);
        }

        function showTestSummary() {
            const summary = document.getElementById('testSummary');
            const content = document.getElementById('summaryContent');
            
            let summaryHTML = `
                <p><strong>Tests Passed:</strong> ${testsPassed} / ${totalTests}</p>
                <p><strong>Success Rate:</strong> ${totalTests > 0 ? Math.round((testsPassed / totalTests) * 100) : 0}%</p>
            `;
            
            if (testsPassed === totalTests && totalTests > 0) {
                summaryHTML += `
                    <div class="status success">
                        🎉 <strong>ALL TESTS PASSED!</strong><br>
                        The "Failed to deduct balance" error has been successfully fixed!
                    </div>
                `;
            } else {
                summaryHTML += `
                    <div class="status warning">
                        ⚠️ <strong>Some tests failed</strong><br>
                        The fix may need additional adjustments.
                    </div>
                `;
            }
            
            content.innerHTML = summaryHTML;
            summary.style.display = 'block';
        }

        // Auto-run test when page loads
        window.addEventListener('load', () => {
            setTimeout(runFullTest, 1000);
        });
    </script>
</body>
</html>
