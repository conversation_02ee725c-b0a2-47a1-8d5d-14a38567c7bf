<?php

namespace Simbi\Tls\Middleware;

use Simbi\Tls\Services\AuthService;

class AdminMiddleware
{
    private AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function authenticate(): array
    {
        // First do regular authentication
        $authMiddleware = new AuthMiddleware($this->authService);
        $user = $authMiddleware->authenticate();
        
        if (isset($user['error'])) {
            return $user;
        }

        // Check if user is admin
        if (!isset($user['is_admin']) || !$user['is_admin']) {
            http_response_code(403);
            return ['error' => 'Admin access required', 'code' => 403];
        }

        // Check if user is active
        if (!isset($user['is_active']) || !$user['is_active']) {
            http_response_code(403);
            return ['error' => 'Account is disabled', 'code' => 403];
        }

        return $user;
    }
}
