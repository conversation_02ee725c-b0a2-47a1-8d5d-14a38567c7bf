<?php
require_once 'vendor/autoload.php';

try {
    $pdo = \Simbi\Tls\Config\Database::getConnection();
    $stmt = $pdo->query('SELECT username, email, created_at FROM users ORDER BY created_at DESC LIMIT 5');
    
    echo "Recent users:\n";
    while($row = $stmt->fetch()) {
        echo $row['username'] . ' (' . $row['email'] . ') - ' . $row['created_at'] . "\n";
    }
    
    // Also check if there are any users at all
    $countStmt = $pdo->query('SELECT COUNT(*) as total FROM users');
    $count = $countStmt->fetch();
    echo "\nTotal users: " . $count['total'] . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
