<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Balance Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Quick Balance Fix Test</h1>
    
    <button onclick="runTest()">Test Balance API Fix</button>
    <div id="results"></div>

    <script>
        async function runTest() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="result">Testing...</div>';
            
            try {
                // Step 1: Login
                const loginResponse = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const loginResult = await loginResponse.json();
                
                if (!loginResult.success) {
                    results.innerHTML = '<div class="result error">❌ Login failed: ' + (loginResult.error || 'Unknown error') + '</div>';
                    return;
                }
                
                results.innerHTML += '<div class="result success">✅ Login successful</div>';
                
                // Step 2: Test balance
                const balanceResponse = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'get_balance' })
                });
                
                const balanceResult = await balanceResponse.json();
                
                results.innerHTML += '<div class="result">Raw balance response:<pre>' + JSON.stringify(balanceResult, null, 2) + '</pre></div>';
                
                if (balanceResult.success) {
                    const balance = parseFloat(balanceResult.balance) || 0;
                    results.innerHTML += '<div class="result success">✅ Balance API successful!</div>';
                    results.innerHTML += '<div class="result success">Balance: ' + balance + ' USDT</div>';
                    
                    if (balance === 30) {
                        results.innerHTML += '<div class="result success">🎯 Perfect! Balance is correct (30 USDT)</div>';
                    } else {
                        results.innerHTML += '<div class="result error">⚠️ Balance unexpected. Expected: 30, Got: ' + balance + '</div>';
                    }
                } else {
                    results.innerHTML += '<div class="result error">❌ Balance API failed: ' + (balanceResult.error || balanceResult.message || 'Unknown error') + '</div>';
                }
                
            } catch (error) {
                results.innerHTML += '<div class="result error">❌ Test error: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
