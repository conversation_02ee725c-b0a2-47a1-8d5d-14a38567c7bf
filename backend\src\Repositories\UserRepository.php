<?php

namespace Simbi\Tls\Repositories;

use PDO;
use Exception;

class UserRepository
{
    private PDO $pdo;

    public function __construct(PDO $pdo)
    {
        $this->pdo = $pdo;
    }

    public function create(string $email, string $passwordHash): ?int
    {
        try {
            $stmt = $this->pdo->prepare("INSERT INTO users (email, password_hash) VALUES (?, ?)");
            $success = $stmt->execute([$email, $passwordHash]);
            
            return $success ? (int)$this->pdo->lastInsertId() : null;
        } catch (Exception $e) {
            error_log("Failed to create user: " . $e->getMessage());
            return null;
        }
    }

    public function findByEmail(string $email): ?array
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ?: null;
        } catch (Exception $e) {
            error_log("Failed to find user by email: " . $e->getMessage());
            return null;
        }
    }

    public function findById(int $userId): ?array
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ?: null;
        } catch (Exception $e) {
            error_log("Failed to find user by ID: " . $e->getMessage());
            return null;
        }
    }

    public function emailExists(string $email): bool
    {
        try {
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $stmt->execute([$email]);
            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            error_log("Failed to check if email exists: " . $e->getMessage());
            return false;
        }
    }

    public function createPasswordResetToken(int $userId, string $token, string $expires): bool
    {
        try {
            // First, update the user table with reset token
            $stmt = $this->pdo->prepare("
                UPDATE users 
                SET password_reset_token = ?, password_reset_expires = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $userSuccess = $stmt->execute([$token, $expires, $userId]);

            // Also insert into password_resets table for tracking
            $stmt = $this->pdo->prepare("
                INSERT INTO password_resets (user_id, token, expires_at, ip_address) 
                VALUES (?, ?, ?, ?)
            ");
            $logSuccess = $stmt->execute([
                $userId, 
                $token, 
                $expires, 
                $_SERVER['REMOTE_ADDR'] ?? null
            ]);

            return $userSuccess && $logSuccess;
        } catch (Exception $e) {
            error_log("Failed to create password reset token: " . $e->getMessage());
            return false;
        }
    }

    public function findByPasswordResetToken(string $token): ?array
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM users 
                WHERE password_reset_token = ? 
                AND password_reset_expires > NOW()
            ");
            $stmt->execute([$token]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ?: null;
        } catch (Exception $e) {
            error_log("Failed to find user by reset token: " . $e->getMessage());
            return null;
        }
    }

    public function updatePassword(int $userId, string $passwordHash): bool
    {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE users 
                SET password_hash = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            return $stmt->execute([$passwordHash, $userId]);
        } catch (Exception $e) {
            error_log("Failed to update password: " . $e->getMessage());
            return false;
        }
    }

    public function clearPasswordResetToken(int $userId): bool
    {
        try {
            // Clear reset token from users table
            $stmt = $this->pdo->prepare("
                UPDATE users 
                SET password_reset_token = NULL, password_reset_expires = NULL, updated_at = NOW() 
                WHERE id = ?
            ");
            $userSuccess = $stmt->execute([$userId]);

            // Mark reset token as used in password_resets table
            $stmt = $this->pdo->prepare("
                UPDATE password_resets 
                SET used_at = NOW() 
                WHERE user_id = ? AND used_at IS NULL
            ");
            $logSuccess = $stmt->execute([$userId]);

            return $userSuccess && $logSuccess;
        } catch (Exception $e) {
            error_log("Failed to clear password reset token: " . $e->getMessage());
            return false;
        }
    }
}
