<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Data API Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🚀 Dynamic Data API Testing</h1>
    <p>This page tests that all static data has been replaced with dynamic API-driven data.</p>

    <div class="test-section">
        <h3>📊 Test 1: System Configuration API</h3>
        <p>Test that system configuration including deposit limits is loaded from API</p>
        <button class="btn" onclick="testSystemConfiguration()">Test Configuration API</button>
        <div id="configResult"></div>
    </div>

    <div class="test-section">
        <h3>📈 Test 2: Investment Plans API</h3>
        <p>Test that investment plans are loaded from database instead of hardcoded</p>
        <button class="btn" onclick="testInvestmentPlans()">Test Investment Plans API</button>
        <div id="plansResult"></div>
    </div>

    <div class="test-section">
        <h3>💰 Test 3: Backend Configuration Service</h3>
        <p>Test backend system configuration endpoint</p>
        <button class="btn" onclick="testBackendConfiguration()">Test Backend Config</button>
        <div id="backendConfigResult"></div>
    </div>

    <div class="test-section">
        <h3>🔄 Test 4: Dynamic Deposit Validation</h3>
        <p>Test that deposit forms use dynamic minimum amounts</p>
        <button class="btn" onclick="testDepositValidation()">Test Deposit Validation</button>
        <div id="depositValidationResult"></div>
    </div>

    <div class="test-section">
        <h3>📋 Test 5: Investment Plan Details</h3>
        <p>Test that investment plan details come from database</p>
        <button class="btn" onclick="testPlanDetails()">Test Plan Details</button>
        <div id="planDetailsResult"></div>
    </div>

    <div class="test-section">
        <h3>✅ Test Summary</h3>
        <div id="testSummary">
            <p>Run all tests above to see the summary here.</p>
        </div>
    </div>

    <script>
        let testResults = {
            systemConfig: false,
            investmentPlans: false,
            backendConfig: false,
            depositValidation: false,
            planDetails: false
        };

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function testSystemConfiguration() {
            showResult('configResult', '🔄 Testing system configuration API...', 'info');
            
            try {
                const response = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_system_configuration'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    testResults.systemConfig = true;
                    showResult('configResult', 
                        `✅ System Configuration API: SUCCESS\n\n` +
                        `Deposit Config:\n` +
                        `- Minimum: ${result.data.deposit.minimum_amount} USDT\n` +
                        `- Maximum: ${result.data.deposit.maximum_amount} USDT\n` +
                        `- Currency: ${result.data.deposit.currency}\n` +
                        `- Network: ${result.data.deposit.network}\n\n` +
                        `Investment Config:\n` +
                        `- Basic minimum: ${result.data.investment.minimum_amounts.basic} USDT\n` +
                        `- Premium minimum: ${result.data.investment.minimum_amounts.premium} USDT\n` +
                        `- VIP minimum: ${result.data.investment.minimum_amounts.vip} USDT`,
                        'success'
                    );
                } else {
                    showResult('configResult', `❌ System Configuration API: FAILED\n${result.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('configResult', `❌ System Configuration API: ERROR\n${error.message}`, 'error');
            }

            updateTestSummary();
        }

        async function testInvestmentPlans() {
            showResult('plansResult', '🔄 Testing investment plans API...', 'info');
            
            try {
                const response = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_investment_plans'
                    })
                });

                const result = await response.json();
                
                if (result.success && result.plans) {
                    testResults.investmentPlans = true;
                    let plansInfo = `✅ Investment Plans API: SUCCESS\n\nFound ${result.plans.length} plans:\n\n`;
                    
                    result.plans.forEach(plan => {
                        const status = plan.is_active ? '✅ ACTIVE' : '❌ DISABLED';
                        plansInfo += `📋 ${plan.plan_name} (${plan.plan_code}):\n`;
                        plansInfo += `   - Daily Rate: ${(plan.daily_rate * 100).toFixed(2)}%\n`;
                        plansInfo += `   - Duration: ${plan.duration} days\n`;
                        plansInfo += `   - Min Amount: ${plan.min_amount} USDT\n`;
                        plansInfo += `   - Status: ${status}\n\n`;
                    });
                    
                    showResult('plansResult', plansInfo, 'success');
                } else {
                    showResult('plansResult', `❌ Investment Plans API: FAILED\n${result.message || 'No plans returned'}`, 'error');
                }
            } catch (error) {
                showResult('plansResult', `❌ Investment Plans API: ERROR\n${error.message}`, 'error');
            }

            updateTestSummary();
        }

        async function testBackendConfiguration() {
            showResult('backendConfigResult', '🔄 Testing backend configuration endpoint...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/api/system-configuration', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success) {
                        testResults.backendConfig = true;
                        showResult('backendConfigResult', 
                            `✅ Backend Configuration API: SUCCESS\n\n` +
                            `Response: ${JSON.stringify(result, null, 2)}`,
                            'success'
                        );
                    } else {
                        showResult('backendConfigResult', `❌ Backend Configuration API: FAILED\n${result.message}`, 'error');
                    }
                } else {
                    showResult('backendConfigResult', `❌ Backend Configuration API: HTTP ${response.status}\n${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult('backendConfigResult', `❌ Backend Configuration API: ERROR\n${error.message}\n\nNote: Make sure backend server is running on localhost:8000`, 'error');
            }

            updateTestSummary();
        }

        async function testDepositValidation() {
            showResult('depositValidationResult', '🔄 Testing dynamic deposit validation...', 'info');
            
            // First get the configuration
            try {
                const configResponse = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_system_configuration'
                    })
                });

                const configResult = await configResponse.json();
                
                if (configResult.success) {
                    const minAmount = configResult.data.deposit.minimum_amount;
                    const maxAmount = configResult.data.deposit.maximum_amount;
                    
                    testResults.depositValidation = true;
                    showResult('depositValidationResult', 
                        `✅ Dynamic Deposit Validation: SUCCESS\n\n` +
                        `Configuration loaded successfully:\n` +
                        `- Dynamic minimum: ${minAmount} USDT\n` +
                        `- Dynamic maximum: ${maxAmount} USDT\n\n` +
                        `✅ Deposit forms should now use these dynamic values instead of hardcoded 1 USDT`,
                        'success'
                    );
                } else {
                    showResult('depositValidationResult', `❌ Dynamic Deposit Validation: FAILED\nCould not load configuration`, 'error');
                }
            } catch (error) {
                showResult('depositValidationResult', `❌ Dynamic Deposit Validation: ERROR\n${error.message}`, 'error');
            }

            updateTestSummary();
        }

        async function testPlanDetails() {
            showResult('planDetailsResult', '🔄 Testing investment plan details from database...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/api/investment-plans', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success && result.data) {
                        testResults.planDetails = true;
                        let details = `✅ Backend Investment Plans: SUCCESS\n\nDatabase-driven plans:\n\n`;
                        
                        result.data.forEach(plan => {
                            details += `📋 ${plan.plan_name}:\n`;
                            details += `   - Code: ${plan.plan_code}\n`;
                            details += `   - Rate: ${(plan.daily_rate * 100).toFixed(2)}% daily\n`;
                            details += `   - Duration: ${plan.duration} days\n`;
                            details += `   - Min Amount: ${plan.min_amount} USDT\n`;
                            details += `   - Active: ${plan.is_active ? 'YES' : 'NO'}\n`;
                            details += `   - Features: ${plan.features ? plan.features.length : 0} items\n\n`;
                        });
                        
                        showResult('planDetailsResult', details, 'success');
                    } else {
                        showResult('planDetailsResult', `❌ Backend Investment Plans: FAILED\n${result.message}`, 'error');
                    }
                } else {
                    showResult('planDetailsResult', `❌ Backend Investment Plans: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('planDetailsResult', `❌ Backend Investment Plans: ERROR\n${error.message}\n\nNote: Make sure backend server is running on localhost:8000`, 'error');
            }

            updateTestSummary();
        }

        function updateTestSummary() {
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            const totalTests = Object.keys(testResults).length;
            
            let summary = `📊 Test Results: ${passedTests}/${totalTests} passed\n\n`;
            
            summary += `System Configuration API: ${testResults.systemConfig ? '✅ PASS' : '❌ FAIL'}\n`;
            summary += `Investment Plans API: ${testResults.investmentPlans ? '✅ PASS' : '❌ FAIL'}\n`;
            summary += `Backend Configuration: ${testResults.backendConfig ? '✅ PASS' : '❌ FAIL'}\n`;
            summary += `Dynamic Deposit Validation: ${testResults.depositValidation ? '✅ PASS' : '❌ FAIL'}\n`;
            summary += `Plan Details from DB: ${testResults.planDetails ? '✅ PASS' : '❌ FAIL'}\n\n`;
            
            if (passedTests === totalTests) {
                summary += `🎉 ALL TESTS PASSED!\n\nStatic data has been successfully replaced with dynamic API-driven data:
- Investment plans are now loaded from database
- Deposit limits are now configurable through API
- All hardcoded values have been replaced
- System is ready for dynamic configuration management`;
            } else {
                summary += `⚠️ Some tests failed. Please check the individual test results above.`;
            }
            
            const summaryClass = passedTests === totalTests ? 'success' : 'error';
            showResult('testSummary', summary, summaryClass);
        }

        // Auto-run tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testSystemConfiguration();
                setTimeout(() => testInvestmentPlans(), 1000);
                setTimeout(() => testBackendConfiguration(), 2000);
                setTimeout(() => testDepositValidation(), 3000);
                setTimeout(() => testPlanDetails(), 4000);
            }, 500);
        });
    </script>
</body>
</html>
