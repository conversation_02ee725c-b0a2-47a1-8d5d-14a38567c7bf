@echo off
REM Payment Confirmation Cron Job Batch Script
REM This script runs the payment confirmation cron job
REM You can schedule this with Windows Task Scheduler

echo [%date% %time%] Starting Payment Confirmation Cron Job

REM Change to the cronJob directory
cd /d "c:\Users\<USER>\Documents\workspace\tls\cronJob"

REM Run the cron job with 1 hour scan period
php cron_runner.php --hours=1 --verbose

REM Check exit code
if %errorlevel% equ 0 (
    echo [%date% %time%] Cron job completed successfully
) else (
    echo [%date% %time%] Cron job failed with error code %errorlevel%
)

REM Log to Windows Event Log (optional)
REM eventcreate /T INFORMATION /ID 1000 /L APPLICATION /SO "TLS Payment Cron" /D "Payment confirmation cron job completed"

echo [%date% %time%] Batch script finished
