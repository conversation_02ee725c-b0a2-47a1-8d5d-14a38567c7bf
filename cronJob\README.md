# Payment Confirmation API Setup and Usage

## Overview
This standalone payment confirmation API allows you to:
- Accept wallet ID and amount to confirm USDT payments from TronGrid
- Update deposit status automatically
- Monitor TRON network transactions
- Run automated cron jobs for payment processing

## File Structure
```
cronJob/
├── index.php                    # Main API endpoint
├── config.php                   # Database and configuration
├── TronGridService.php          # TronGrid API integration
├── PaymentConfirmationService.php # Payment processing logic
├── cron_runner.php              # Cron job runner
├── test_payment_api.php         # API testing script
├── run_cron.bat                 # Windows batch script for cron
└── logs/                        # Log files directory
```

## Setup Instructions

### 1. Environment Configuration
Ensure your backend `.env` file contains:
```env
# Database Configuration
DB_HOST=localhost
DB_NAME=tlssc
DB_USER=root
DB_PASS=your_password

# TronGrid API Configuration
TRON_GRID_API_KEY=your_trongrid_api_key
TRON_NETWORK=mainnet
USDT_CONTRACT=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
```

### 2. Database Requirements
The API expects the following database tables:
- `wallets` - User wallet information
- `users` - User accounts and balances
- `transactions` - Transaction records
- `balance_history` - Balance change history
- `deposit_logs` - Deposit activity logs

### 3. Web Server Setup
Place the `cronJob` folder in your web server directory and ensure PHP has access to:
- cURL extension
- PDO MySQL extension
- JSON extension

## API Endpoints

### Health Check
```
GET /cronJob/
```
Returns API status and TronGrid connectivity.

### Payment Confirmation
```
POST /cronJob/
Content-Type: application/json

{
  "wallet_id": 123,
  "amount": 50.25,
  "transaction_hash": "optional_tx_hash"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment confirmed successfully",
  "transaction_id": 456,
  "blockchain_tx": "abc123...",
  "amount": 50.25,
  "wallet_address": "TXxxx...",
  "confirmations": 20,
  "verified_at": "2025-06-10 14:30:00"
}
```

### Transaction Status
```
GET /cronJob/status?hash=transaction_hash
```

**Response:**
```json
{
  "success": true,
  "found": true,
  "status": "confirmed",
  "amount": 50.25,
  "user_id": 123,
  "created_at": "2025-06-10 14:30:00"
}
```

## Cron Job Setup

### Manual Testing
```bash
# Test the cron runner
php cron_runner.php --dry-run --verbose

# Run with 2-hour scan period
php cron_runner.php --hours=2 --verbose
```

### Windows Task Scheduler
1. Open Task Scheduler
2. Create Basic Task
3. Set trigger (e.g., every 5 minutes)
4. Set action to run `run_cron.bat`
5. Set start in directory to the cronJob folder

### Linux Cron Setup
Add to crontab (`crontab -e`):
```cron
# Run every 5 minutes
*/5 * * * * cd /path/to/cronJob && php cron_runner.php --hours=1
```

## Testing

### Run API Tests
```bash
php test_payment_api.php
```

### Manual API Testing
```bash
# Health check
curl -X GET http://localhost/tls/cronJob/

# Payment confirmation
curl -X POST http://localhost/tls/cronJob/ \
  -H "Content-Type: application/json" \
  -d '{"wallet_id": 1, "amount": 10.5}'

# Status check
curl -X GET "http://localhost/tls/cronJob/status?hash=your_tx_hash"
```

## Security Considerations

### API Security
- Implement rate limiting
- Add API key authentication
- Use HTTPS in production
- Validate all inputs
- Log all API calls

### Database Security
- Use prepared statements (already implemented)
- Limit database user permissions
- Enable database logging
- Regular backups

### TronGrid API
- Keep API key secure
- Monitor API usage limits
- Implement retry logic for failures
- Cache responses when appropriate

## Monitoring and Logging

### Log Files
- `logs/cron_YYYY-MM-DD.log` - Daily cron job logs
- Web server error logs
- Database logs

### Monitoring Points
- API response times
- TronGrid API availability
- Database connection health
- Transaction confirmation rates
- Error rates and patterns

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials in `.env`
   - Verify database server is running
   - Check network connectivity

2. **TronGrid API Error**
   - Verify API key is valid
   - Check network connectivity
   - Monitor API rate limits

3. **Transaction Not Found**
   - Verify wallet address is correct
   - Check transaction hash format
   - Ensure sufficient confirmations

4. **Cron Job Not Running**
   - Check file permissions
   - Verify PHP CLI is available
   - Check Windows Task Scheduler logs

### Debug Mode
Add to `config.php` for debugging:
```php
define('DEBUG_MODE', true);
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Performance Optimization

### Database Optimization
- Add indexes on frequently queried columns
- Regular database maintenance
- Connection pooling

### API Optimization
- Implement response caching
- Optimize database queries
- Add request queuing for high volume

### TronGrid Optimization
- Cache transaction data
- Batch API requests
- Implement exponential backoff

## Production Deployment

### Checklist
- [ ] Environment variables configured
- [ ] Database tables created
- [ ] TronGrid API key added
- [ ] Web server configured
- [ ] SSL certificate installed
- [ ] Cron jobs scheduled
- [ ] Monitoring setup
- [ ] Backup procedures in place
- [ ] Security hardening complete

### Scaling Considerations
- Load balancing for multiple API instances
- Database replication
- Queue system for high-volume processing
- Microservice architecture for large deployments

## Support and Maintenance

### Regular Tasks
- Monitor log files
- Check API health
- Update TronGrid configurations
- Database maintenance
- Security updates

### Backup Strategy
- Daily database backups
- Configuration file backups
- Log file retention policy
- Disaster recovery procedures
