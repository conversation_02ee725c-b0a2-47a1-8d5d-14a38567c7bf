<?php
// Debug session state for investment creation
require_once 'frontend/config.php';

// Initialize session
FrontendConfig::initSession();

echo "Session Debug for Investment Creation\n";
echo "===================================\n\n";

echo "Session ID: " . session_id() . "\n";
echo "Session Name: " . session_name() . "\n\n";

echo "Session Data:\n";
echo "-------------\n";
foreach ($_SESSION as $key => $value) {
    if (is_array($value) || is_object($value)) {
        echo "$key: " . print_r($value, true) . "\n";
    } else {
        echo "$key: $value\n";
    }
}

echo "\nAuthentication Check:\n";
echo "--------------------\n";
echo "isAuthenticated(): " . (FrontendConfig::isAuthenticated() ? 'YES' : 'NO') . "\n";
echo "isAdmin(): " . (FrontendConfig::isAdmin() ? 'YES' : 'NO') . "\n";

$currentUser = FrontendConfig::getCurrentUser();
if ($currentUser) {
    echo "\nCurrent User:\n";
    echo "------------\n";
    print_r($currentUser);
} else {
    echo "\nCurrent User: NULL (not authenticated)\n";
}

echo "\nRequired Session Variables for AJAX:\n";
echo "-----------------------------------\n";
echo "user_id: " . ($_SESSION['user_id'] ?? 'NOT SET') . "\n";
echo "token: " . ($_SESSION['token'] ?? 'NOT SET') . "\n";
echo "email: " . ($_SESSION['email'] ?? 'NOT SET') . "\n";

echo "\nTest AJAX Authentication Check:\n";
echo "------------------------------\n";
if (!isset($_SESSION['token'])) {
    echo "❌ AJAX would fail: 'Not authenticated' (no token)\n";
} elseif (!isset($_SESSION['user_id'])) {
    echo "❌ AJAX would fail: 'User not authenticated' (no user_id)\n";
} else {
    echo "✅ AJAX should pass authentication checks\n";
}

echo "\nTo test real session state, access this via browser after logging in:\n";
echo "http://localhost:8080/debug_session.php\n";
?>
