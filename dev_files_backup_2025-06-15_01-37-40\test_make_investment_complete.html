<!DOCTYPE html>
<html>
<head>
    <title>Make Investment Page Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Make Investment Page Testing</h1>
    
    <div class="test-section">
        <h2>1. API Endpoint Test</h2>
        <button onclick="testAPI()">Test get_investment_plans API</button>
        <div id="apiResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. CSS Verification</h2>
        <p class="success">✅ CSS file location: frontend/css/dashboard.css</p>
        <p class="success">✅ Make investment styles added to dashboard.css</p>
        <p class="info">Testing responsive design classes...</p>
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px;">
            <h3>Sample Investment Plan Card</h3>
            <p>This should have gradient background if CSS is working</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. Page Structure Test</h2>
        <p class="success">✅ make_investment.php file exists</p>
        <p class="success">✅ JavaScript file included: js/make_investment.js</p>
        <p class="success">✅ Header and footer includes present</p>
    </div>
    
    <div class="test-section">
        <h2>4. Live Page Test</h2>
        <p><a href="frontend/user/make_investment.php" target="_blank">Open Make Investment Page in New Tab</a></p>
        <p>Expected behavior:</p>
        <ul>
            <li>Page loads with proper styling</li>
            <li>Investment plans load dynamically</li>
            <li>Only Basic Plan should be active/selectable</li>
            <li>Premium and VIP plans should be disabled</li>
            <li>User balance displays</li>
            <li>Plan selection shows investment form</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>5. Interactive Test</h2>
        <iframe src="frontend/user/make_investment.php"></iframe>
    </div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p class="info">Testing API...</p>';
            
            try {
                const response = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_investment_plans'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    let html = '<p class="success">✅ API working correctly!</p>';
                    html += '<h4>Investment Plans:</h4><ul>';
                    
                    data.plans.forEach(plan => {
                        const status = plan.is_active ? '✅ Active' : '❌ Disabled';
                        html += `<li><strong>${plan.plan_name}</strong> - ${status} - ${(plan.daily_rate * 100).toFixed(2)}% daily</li>`;
                    });
                    
                    html += '</ul>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<p class="error">❌ API Error: ' + (data.message || data.error || 'Unknown error') + '</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<p class="error">❌ Network Error: ' + error.message + '</p>';
            }
        }
        
        // Auto-test API on page load
        window.addEventListener('load', testAPI);
    </script>
</body>
</html>
