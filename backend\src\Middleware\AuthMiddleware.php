<?php

namespace Simbi\Tls\Middleware;

use Simbi\Tls\Services\AuthService;

class AuthMiddleware
{
    private AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function authenticate(): ?array
    {
        // Get Authorization header
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;

        if (!$authHeader) {
            return ['error' => 'Authorization header missing', 'code' => 401];
        }

        // Extract token from "Bearer <token>"
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return ['error' => 'Invalid authorization header format', 'code' => 401];
        }

        $token = $matches[1];

        // Validate token and get user
        $user = $this->authService->getUserFromToken($token);

        if (!$user) {
            return ['error' => 'Invalid or expired token', 'code' => 401];
        }

        return $user;
    }
}
