<?php

declare(strict_types=1);

/**
 * TLS Wallet Cron Job Runner
 * 
 * This script handles all scheduled tasks for the TLS Wallet system:
 * - Payment confirmation via blockchain verification
 * - Investment return processing
 * - System maintenance tasks
 * 
 * Usage:
 * php cron_runner.php [task]
 * 
 * Tasks:
 * - all (default): Run all scheduled tasks
 * - payments: Run payment confirmation only
 * - investments: Run investment processing only
 * - maintenance: Run maintenance tasks only
 * - status: Get system status
 */

// Include composer autoloader
require_once __DIR__ . '/../backend/vendor/autoload.php';

use Simbi\Tls\CronJob\CronRunner;
use Simbi\Tls\CronJob\Config\CronConfig;

try {
    // Get task from command line argument
    $task = $argv[1] ?? 'all';
    
    // Initialize cron runner
    $cronRunner = new CronRunner();
    
    // Execute based on task type
    switch ($task) {
        case 'payments':
            $result = $cronRunner->runPaymentConfirmation();
            break;
            
        case 'investments':
            $result = $cronRunner->runInvestmentProcessing();
            break;
            
        case 'maintenance':
            $result = $cronRunner->runMaintenanceTasks();
            break;
            
        case 'status':
            $result = $cronRunner->getStatus();
            break;
            
        case 'all':
        default:
            $result = $cronRunner->runAll();
            break;
    }
    
    // Output result
    echo json_encode($result, JSON_PRETTY_PRINT) . PHP_EOL;
    
    // Exit with appropriate code
    exit($result['success'] ? 0 : 1);
    
} catch (Exception $e) {
    $error = [
        'success' => false,
        'error' => 'Cron runner initialization failed: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($error, JSON_PRETTY_PRINT) . PHP_EOL;
    
    // Log error if possible
    if (class_exists('Simbi\Tls\CronJob\Config\CronConfig')) {
        CronConfig::log($e->getMessage(), 'error', 'main');
    }
    
    exit(1);
}
