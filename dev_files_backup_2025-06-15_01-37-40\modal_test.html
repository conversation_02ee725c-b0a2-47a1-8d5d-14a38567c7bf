<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Success Modal Test</title>
    <link rel="stylesheet" href="frontend/user/css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .balance-display {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1, h2 {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Success Modal Test</h1>
        <p>This page tests the success modal implementation without requiring backend authentication.</p>
        
        <div class="test-section">
            <h2>Current Balance</h2>
            <div class="balance-display" id="balance-display">
                Balance: <span id="current-balance">5000.00</span> USDT
            </div>
        </div>

        <div class="test-section">
            <h2>Test Scenarios</h2>
            <button onclick="testSuccessModal('1000', 'Basic Plan', 'INV123456')">
                Test Basic Plan Investment (1000 USDT)
            </button>
            <button onclick="testSuccessModal('2500', 'Premium Plan', 'INV789012')">
                Test Premium Plan Investment (2500 USDT)
            </button>
            <button onclick="testSuccessModal('500', 'Starter Plan', 'INV345678')">
                Test Starter Plan Investment (500 USDT)
            </button>
        </div>

        <div class="test-section">
            <h2>Test Instructions</h2>
            <ol>
                <li>Click any test button above</li>
                <li>Verify the success modal appears</li>
                <li>Check that investment details are displayed correctly</li>
                <li>Verify the new balance is calculated and shown</li>
                <li>Test modal close functionality (X button, buttons, or escape key)</li>
                <li>Confirm the main balance display updates after modal closes</li>
            </ol>
        </div>
    </div>

    <!-- Success Modal (copied from make_investment.php) -->
    <div id="successModal" class="modal" style="display: none;">
        <div class="modal-content success-modal">
            <div class="modal-header success-header">
                <h3>🎉 Investment Successful!</h3>
                <button type="button" class="modal-close" id="closeSuccessModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="success-icon">
                    <div class="checkmark-circle">
                        <div class="checkmark"></div>
                    </div>
                </div>
                <p class="success-message">Your investment has been successfully created and processed!</p>
                
                <div class="investment-details">
                    <div class="detail-row">
                        <span class="detail-label">Investment Amount:</span>
                        <span class="detail-value" id="modalAmount">-</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Investment Plan:</span>
                        <span class="detail-value" id="modalPlan">-</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Investment ID:</span>
                        <span class="detail-value" id="modalInvestmentId">-</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">New Balance:</span>
                        <span class="detail-value" id="modalNewBalance">-</span>
                    </div>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-primary" id="makeAnotherInvestment">
                        Make Another Investment
                    </button>
                    <button type="button" class="btn btn-success" id="viewDashboard">
                        View Dashboard
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables to simulate the real application
        let userBalance = 5000.00;

        function updateBalanceDisplay() {
            document.getElementById('current-balance').textContent = userBalance.toFixed(2);
        }

        function showSuccessModal(amount, plan, investmentId) {
            console.log('showSuccessModal called with:', { amount, plan, investmentId });
            
            // Calculate new balance
            const investmentAmount = parseFloat(amount);
            const newBalance = userBalance - investmentAmount;
            
            // Update global balance
            userBalance = newBalance;
            
            // Populate modal elements
            document.getElementById('modalAmount').textContent = `${investmentAmount.toFixed(2)} USDT`;
            document.getElementById('modalPlan').textContent = plan;
            document.getElementById('modalInvestmentId').textContent = investmentId;
            document.getElementById('modalNewBalance').textContent = `${newBalance.toFixed(2)} USDT`;
            
            // Show modal
            const modal = document.getElementById('successModal');
            modal.style.display = 'block';
            
            // Add animation class
            setTimeout(() => {
                modal.querySelector('.modal-content').style.opacity = '1';
                modal.querySelector('.modal-content').style.transform = 'translateY(0)';
            }, 10);
        }

        function hideSuccessModal() {
            console.log('hideSuccessModal called');
            const modal = document.getElementById('successModal');
            const modalContent = modal.querySelector('.modal-content');
            
            // Animate out
            modalContent.style.opacity = '0';
            modalContent.style.transform = 'translateY(-20px)';
            
            setTimeout(() => {
                modal.style.display = 'none';
                // Update main balance display after modal closes
                updateBalanceDisplay();
            }, 300);
        }

        function testSuccessModal(amount, plan, investmentId) {
            console.log('Testing success modal with:', { amount, plan, investmentId });
            showSuccessModal(amount, plan, investmentId);
        }

        function initSuccessModalHandlers() {
            // Close button
            document.getElementById('closeSuccessModal').addEventListener('click', hideSuccessModal);
            
            // Action buttons
            document.getElementById('makeAnotherInvestment').addEventListener('click', () => {
                hideSuccessModal();
                alert('Redirecting to investment page...');
            });
            
            document.getElementById('viewDashboard').addEventListener('click', () => {
                hideSuccessModal();
                alert('Redirecting to dashboard...');
            });
            
            // Close on outside click
            document.getElementById('successModal').addEventListener('click', (e) => {
                if (e.target.id === 'successModal') {
                    hideSuccessModal();
                }
            });
            
            // Close on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && document.getElementById('successModal').style.display === 'block') {
                    hideSuccessModal();
                }
            });
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Page loaded, initializing modal handlers');
            initSuccessModalHandlers();
            updateBalanceDisplay();
        });
    </script>
</body>
</html>
