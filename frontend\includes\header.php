<?php
// Reusable header component for user dashboard pages
// Requires: $pageTitle to be set before including this file
// Optional: $currentPage to set active navigation

if (!isset($pageTitle)) {
    $pageTitle = 'TRON Wallet';
}

$currentPage = $currentPage ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>    <link rel="stylesheet" href="<?php echo isset($cssPath) ? $cssPath : 'css'; ?>/style.css">
    <link rel="stylesheet" href="<?php echo isset($cssPath) ? $cssPath : 'css'; ?>/dashboard.css">
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo htmlspecialchars($css); ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1>TRON Wallet</h1>                <div class="header-actions">
                    <a href="<?php echo isset($basePath) ? $basePath : '.'; ?>/dashboard.php" class="btn btn-outline">Dashboard</a>
                    <a href="<?php echo isset($basePath) ? $basePath : '.'; ?>/profile.php" class="btn btn-outline">Profile</a>
                    <a href="../index.php?logout=1" class="btn btn-danger">Logout</a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
