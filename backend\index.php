<?php
// Router script for PHP development server
// This handles URL rewriting for API routes

$uri = $_SERVER['REQUEST_URI'];
$path = parse_url($uri, PHP_URL_PATH);

// Handle API routes - route them to src/index.php
if (strpos($path, '/api/') === 0) {
    require_once 'src/index.php';
    return true;
}

// Handle root path - route to src/index.php as well
if ($path === '/') {
    require_once 'src/index.php';
    return true;
}

// For other files, let PHP dev server handle them normally
return false;
?>
