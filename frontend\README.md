# TLS Crypto Wallet Frontend

A complete mobile-first frontend implementation for the TLS Crypto Wallet system, built with HTML, CSS, PHP, and JavaScript.

## Overview

This frontend provides a complete user interface for the TLS Crypto Wallet system with the following features:

- **User Authentication**: Login, registration, and password management
- **Wallet Management**: Create wallets, check balances, and manage addresses
- **Transaction System**: View transaction history, deposit tracking, and withdrawals
- **Admin Dashboard**: User management, system monitoring, and activity logs
- **Mobile-First Design**: Responsive design optimized for all devices
- **Security**: Session-based authentication with PHP backend integration

## Features

### 🔐 Authentication System
- User registration and login
- Forgot password functionality
- Session management with automatic redirects
- Role-based access control (User/Admin)

### 💰 Wallet Features
- TRON wallet creation and management
- Real-time balance checking
- Deposit address generation with QR codes
- Transaction history with filtering
- Withdrawal functionality
- Fund sweeping capabilities

### 📊 User Dashboard
- Balance overview and statistics
- Recent transaction display
- Interactive transaction filtering
- Wallet management interface
- User profile management

### 🛠️ Admin Dashboard
- User management (promote/demote, activate/deactivate)
- Transaction monitoring and statistics
- System health monitoring
- Activity logs and audit trails
- Database management tools

### 📱 Mobile Responsive
- Mobile-first responsive design
- Optimized for all screen sizes
- Touch-friendly interface
- Adaptive navigation

## File Structure

```
frontend/
├── index.php              # Authentication page (login/register)
├── dashboard.php          # User dashboard
├── admin.php             # Admin dashboard
├── api.php               # PHP API wrapper class
├── ajax.php              # AJAX request handler
├── test.html             # Frontend test page
├── css/
│   ├── style.css         # Base styles and utilities
│   ├── auth.css          # Authentication page styles
│   ├── dashboard.css     # User dashboard styles
│   └── admin.css         # Admin dashboard styles
└── js/
    ├── auth.js           # Authentication functionality
    ├── dashboard.js      # Dashboard functionality
    ├── admin.js          # Admin dashboard functionality
    └── qr-generator.js   # QR code generation
```

## Installation

1. **Prerequisites**
   - PHP 7.4 or higher
   - Web server (Apache/Nginx) or PHP built-in server
   - TLS Backend API running and accessible

2. **Setup**
   ```bash
   # Clone or copy the frontend files to your web directory
   cd /path/to/your/webserver/root
   
   # Copy all frontend files
   cp -r frontend/* .
   
   # Set appropriate permissions
   chmod 644 *.php *.html
   chmod 755 css/ js/
   ```

3. **Configuration**
   - Update the API base URL in `api.php` to point to your backend
   - Configure session settings if needed
   - Ensure the backend API is running and accessible

## Usage

### Development Server
For development, you can use PHP's built-in server:

```bash
cd frontend/
php -S localhost:8080
```

Then access:
- Main application: http://localhost:8080/index.php
- Test page: http://localhost:8080/test.html

### Production Deployment
1. Copy all files to your web server document root
2. Configure your web server to handle PHP
3. Update API endpoint in `api.php`
4. Set up proper SSL certificates for HTTPS

## API Integration

The frontend integrates with the backend API through a PHP wrapper system:

### API Wrapper (`api.php`)
- Handles all backend API communication
- Manages authentication tokens
- Provides error handling and response formatting
- Hides API credentials from browser console

### AJAX Handler (`ajax.php`)
- Processes all frontend AJAX requests
- Validates user sessions
- Routes requests to appropriate API endpoints
- Returns JSON responses to frontend

### Supported API Endpoints
- **Authentication**: register, login, logout, forgot_password, change_password
- **Wallet Management**: create_wallet, get_balance, sweep_funds
- **Transactions**: get_transactions, withdraw, record_deposit, get_transaction_statistics
- **Admin Functions**: user management, system statistics, activity logs

## Security Features

### Session Security
- PHP session-based authentication
- Automatic session timeout
- Role-based access control
- CSRF protection through session validation

### Data Protection
- API credentials hidden from browser
- Input validation and sanitization
- Secure password handling
- Protected admin areas

### Access Control
- User authentication required for all dashboard features
- Admin privileges required for admin functions
- Automatic redirects based on authentication status

## Mobile Responsiveness

### Breakpoints
- **Mobile**: ≤ 576px
- **Tablet**: 577px - 768px  
- **Desktop**: 769px - 992px
- **Large Desktop**: ≥ 1200px

### Mobile Features
- Touch-friendly navigation
- Optimized form layouts
- Responsive data tables
- Mobile-friendly modals
- Swipe-friendly interfaces

## Browser Support

### Supported Browsers
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

### Required Features
- ES6+ JavaScript support
- CSS Grid and Flexbox
- Fetch API
- CSS Custom Properties
- Local Storage

## Development

### Code Structure
- **Modular CSS**: Separate stylesheets for different sections
- **Component-based JS**: Classes and modules for functionality
- **PHP OOP**: Object-oriented API wrapper and handlers
- **Responsive Design**: Mobile-first approach

### Adding New Features
1. Add API endpoint to `api.php` wrapper
2. Add AJAX handler case in `ajax.php`
3. Implement frontend JavaScript functionality
4. Add corresponding CSS styles
5. Update HTML templates as needed

## Testing

### Test Page
Access `test.html` to verify:
- File structure completeness
- Feature implementation status
- API integration readiness
- Mobile responsiveness
- Security features

### Manual Testing
1. **Authentication Flow**
   - Register new user
   - Login with credentials
   - Test forgot password
   - Change password

2. **Wallet Functions**
   - Create new wallet
   - Check balance
   - Generate deposit address
   - Record deposit
   - Process withdrawal

3. **Admin Functions**
   - User management
   - Transaction monitoring
   - System statistics
   - Activity logs

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check backend API is running
   - Verify API URL in `api.php`
   - Check network connectivity

2. **Session Issues**
   - Clear browser cookies
   - Check PHP session configuration
   - Verify session timeout settings

3. **Permission Denied**
   - Check file permissions
   - Verify user authentication
   - Check admin privileges

### Debug Mode
Enable error reporting in PHP for development:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Production Checklist

- [ ] Update API endpoints for production
- [ ] Enable HTTPS/SSL
- [ ] Set secure session cookies
- [ ] Disable error reporting
- [ ] Set up proper logging
- [ ] Configure security headers
- [ ] Test all functionality
- [ ] Verify mobile responsiveness
- [ ] Check performance optimization

## Support

For issues or questions:
1. Check the test page for system status
2. Review browser console for JavaScript errors
3. Check PHP error logs for server issues
4. Verify backend API connectivity

## License

This project is part of the TLS Crypto Wallet system. All rights reserved.
