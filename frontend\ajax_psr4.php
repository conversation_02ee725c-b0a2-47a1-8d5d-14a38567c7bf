<?php

declare(strict_types=1);

/**
 * PSR-4 Compliant AJAX Entry Point
 * 
 * This file replaces the old ajax.php and uses the new PSR-4 structure
 * with proper autoloading, namespaces, and OOP design
 */

// Include composer autoloader
require_once __DIR__ . '/../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Services\AjaxHandler;
use Simbi\Tls\Frontend\Config\FrontendConfig;

try {
    // Initialize frontend configuration
    FrontendConfig::init();
    
    // Create and handle AJAX request
    $ajaxHandler = new AjaxHandler();
    $ajaxHandler->handle();
    
} catch (Exception $e) {
    // Log error
    error_log('AJAX Handler Error: ' . $e->getMessage());
    
    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'Internal server error',
        'message' => FrontendConfig::get('DEBUG_MODE', false) ? $e->getMessage() : 'Please try again later'
    ]);
}
