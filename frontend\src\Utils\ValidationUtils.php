<?php

declare(strict_types=1);

namespace Simbi\Tls\Frontend\Utils;

/**
 * PSR-4 compliant validation and sanitization utility
 * Provides common validation and data sanitization methods
 */
class ValidationUtils
{
    /**
     * Sanitize input data recursively
     */
    public static function sanitizeInput(mixed $input): mixed
    {
        if (is_string($input)) {
            return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        } elseif (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        return $input;
    }

    /**
     * Validate email format
     */
    public static function isValidEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate password strength
     */
    public static function isValidPassword(string $password, int $minLength = 8): array
    {
        $errors = [];
        
        if (strlen($password) < $minLength) {
            $errors[] = "Password must be at least {$minLength} characters long";
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password must contain at least one uppercase letter";
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "Password must contain at least one lowercase letter";
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "Password must contain at least one number";
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "Password must contain at least one special character";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate TRON wallet address
     */
    public static function isValidTronAddress(string $address): bool
    {
        // TRON addresses start with 'T' and are 34 characters long
        return preg_match('/^T[A-Za-z0-9]{33}$/', $address) === 1;
    }

    /**
     * Validate transaction hash
     */
    public static function isValidTransactionHash(string $hash): bool
    {
        // Transaction hashes are 64-character hexadecimal strings
        return preg_match('/^[a-fA-F0-9]{64}$/', $hash) === 1;
    }

    /**
     * Validate numeric amount
     */
    public static function isValidAmount(mixed $amount, float $min = 0, ?float $max = null): array
    {
        $errors = [];
        
        if (!is_numeric($amount)) {
            $errors[] = "Amount must be a valid number";
            return ['valid' => false, 'errors' => $errors];
        }
        
        $amount = (float)$amount;
        
        if ($amount < $min) {
            $errors[] = "Amount must be at least {$min}";
        }
        
        if ($max !== null && $amount > $max) {
            $errors[] = "Amount cannot exceed {$max}";
        }
        
        if ($amount < 0) {
            $errors[] = "Amount cannot be negative";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'value' => $amount
        ];
    }

    /**
     * Validate required fields
     */
    public static function validateRequired(array $data, array $requiredFields): array
    {
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty(trim((string)$data[$field]))) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . " is required";
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate investment plan
     */
    public static function isValidInvestmentPlan(string $plan): bool
    {
        $validPlans = ['basic', 'premium', 'ultimate'];
        return in_array(strtolower($plan), $validPlans, true);
    }

    /**
     * Sanitize filename
     */
    public static function sanitizeFilename(string $filename): string
    {
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // Remove multiple dots
        $filename = preg_replace('/\.+/', '.', $filename);
        
        // Trim dots and spaces
        return trim($filename, '. ');
    }

    /**
     * Validate file upload
     */
    public static function validateFileUpload(array $file, array $allowedTypes = [], int $maxSize = 5242880): array
    {
        $errors = [];
        
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $errors[] = "No file was uploaded";
            return ['valid' => false, 'errors' => $errors];
        }
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = "File upload error: " . $file['error'];
        }
        
        if ($file['size'] > $maxSize) {
            $maxSizeMB = round($maxSize / 1024 / 1024, 2);
            $errors[] = "File size cannot exceed {$maxSizeMB}MB";
        }
        
        if (!empty($allowedTypes)) {
            $fileExt = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($fileExt, $allowedTypes, true)) {
                $errors[] = "File type not allowed. Allowed types: " . implode(', ', $allowedTypes);
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Escape output for HTML
     */
    public static function escape(string $string): string
    {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Validate URL
     */
    public static function isValidUrl(string $url): bool
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Validate date format
     */
    public static function isValidDate(string $date, string $format = 'Y-m-d'): bool
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    /**
     * Clean phone number
     */
    public static function cleanPhoneNumber(string $phone): string
    {
        return preg_replace('/[^0-9+]/', '', $phone);
    }

    /**
     * Validate phone number (basic)
     */
    public static function isValidPhoneNumber(string $phone): bool
    {
        $cleaned = self::cleanPhoneNumber($phone);
        return preg_match('/^[\+]?[0-9]{10,15}$/', $cleaned) === 1;
    }

    /**
     * Generate random string
     */
    public static function generateRandomString(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Validate JSON string
     */
    public static function isValidJson(string $json): bool
    {
        json_decode($json);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Clean and validate input data
     */
    public static function cleanAndValidate(array $data, array $rules): array
    {
        $cleaned = [];
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            
            // Check if required
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . " is required";
                continue;
            }
            
            // Skip validation if value is empty and not required
            if (empty($value) && (!isset($rule['required']) || !$rule['required'])) {
                $cleaned[$field] = null;
                continue;
            }
            
            // Apply type validation
            switch ($rule['type'] ?? 'string') {
                case 'email':
                    if (!self::isValidEmail($value)) {
                        $errors[$field] = "Invalid email format";
                    } else {
                        $cleaned[$field] = filter_var($value, FILTER_SANITIZE_EMAIL);
                    }
                    break;
                    
                case 'string':
                    $cleaned[$field] = self::sanitizeInput($value);
                    if (isset($rule['min_length']) && strlen($cleaned[$field]) < $rule['min_length']) {
                        $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . " must be at least {$rule['min_length']} characters";
                    }
                    if (isset($rule['max_length']) && strlen($cleaned[$field]) > $rule['max_length']) {
                        $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . " cannot exceed {$rule['max_length']} characters";
                    }
                    break;
                    
                case 'number':
                case 'float':
                    if (!is_numeric($value)) {
                        $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . " must be a valid number";
                    } else {
                        $cleaned[$field] = (float)$value;
                        if (isset($rule['min']) && $cleaned[$field] < $rule['min']) {
                            $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . " must be at least {$rule['min']}";
                        }
                        if (isset($rule['max']) && $cleaned[$field] > $rule['max']) {
                            $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . " cannot exceed {$rule['max']}";
                        }
                    }
                    break;
                    
                case 'integer':
                    if (!is_numeric($value) || (int)$value != $value) {
                        $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . " must be a valid integer";
                    } else {
                        $cleaned[$field] = (int)$value;
                    }
                    break;
                    
                case 'tron_address':
                    if (!self::isValidTronAddress($value)) {
                        $errors[$field] = "Invalid TRON address format";
                    } else {
                        $cleaned[$field] = $value;
                    }
                    break;
            }
        }
        
        return [
            'valid' => empty($errors),
            'data' => $cleaned,
            'errors' => $errors
        ];
    }
}
