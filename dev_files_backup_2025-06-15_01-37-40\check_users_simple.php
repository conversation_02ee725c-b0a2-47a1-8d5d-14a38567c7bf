<?php
require_once 'vendor/autoload.php';
use Simbi\Tls\Config\Database;

try {
    $pdo = Database::getConnection();
    $stmt = $pdo->query('SELECT id, email, created_at FROM users ORDER BY id LIMIT 10');
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Users in database:\n";
    echo "==================\n";
    if (empty($users)) {
        echo "No users found in database\n\n";
        echo "To test investment creation, you need to:\n";
        echo "1. Register a new user via the frontend\n";
        echo "2. Or create a test user manually\n";
    } else {
        foreach ($users as $user) {
            echo "ID: {$user['id']}, Email: {$user['email']}, Created: {$user['created_at']}\n";
        }
        echo "\nUse one of these emails to test login and investment creation\n";
    }
    
    // Also check if there are any sessions or tokens
    echo "\nChecking session data...\n";
    $stmt = $pdo->query('SHOW TABLES LIKE "sessions"');
    $sessionTable = $stmt->fetch();
    
    if ($sessionTable) {
        $stmt = $pdo->query('SELECT COUNT(*) as count FROM sessions');
        $sessionCount = $stmt->fetch();
        echo "Sessions table exists with {$sessionCount['count']} active sessions\n";
    } else {
        echo "No sessions table found (session management might be file-based)\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
