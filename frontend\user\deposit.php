<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'TRON Wallet - Deposit Funds';
$currentPage = 'deposit';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>            <!-- Deposit Content -->
            <div class="deposit-page">
                <div class="page-header">
                    <h2>Deposit Funds</h2>
                    <p>Enter the amount you want to deposit and get payment instructions</p>
                </div>                <!-- Deposit Amount Form -->
                <div class="card">
                    <h3>💳 Deposit Amount</h3>
                    <div class="deposit-process-indicator">
                        <div class="process-step active">
                            <div class="step-circle">1</div>
                            <span>Enter Amount</span>
                        </div>
                        <div class="process-connector"></div>
                        <div class="process-step">
                            <div class="step-circle">2</div>
                            <span>Get Instructions</span>
                        </div>
                        <div class="process-connector"></div>
                        <div class="process-step">
                            <div class="step-circle">3</div>
                            <span>Send Payment</span>                        </div>
                    </div>                    <form id="depositForm" class="deposit-form">                        <div class="form-group">
                            <label for="depositAmount">
                                <span class="label-text">Amount to Deposit (USDT)</span>
                                <span class="label-badge">Required</span>
                            </label>
                            <div class="input-wrapper">                                <input type="number" id="depositAmount" name="amount" step="0.01" min="1" required 
                                       placeholder="Enter amount (minimum 1 USDT)"
                                       autocomplete="off">
                                <div class="input-icon">USDT</div>
                            </div>
                            <small class="form-help">
                                <span class="help-icon">ℹ️</span>
                                <span class="dynamic-limits">Minimum deposit: 1 USDT • Maximum: 1,000,000 USDT</span>
                            </small>
                        </div>
                        
                        <div class="form-group">
                            <label for="depositNote">
                                <span class="label-text">Note (Optional)</span>
                                <span class="label-badge optional">Optional</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="text" id="depositNote" name="note" 
                                       placeholder="Add a note to identify this deposit"
                                       maxlength="100">
                                <div class="input-icon">💬</div>
                            </div>
                            <small class="form-help">
                                <span class="help-icon">💡</span>
                                Add a personal note to help you remember this deposit
                            </small>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary" id="submitDepositBtn">
                                <span class="btn-text">Get Payment Details</span>
                                <span class="btn-icon">→</span>
                            </button>
                            <button type="reset" class="btn btn-outline">
                                <span class="btn-text">Reset Form</span>
                                <span class="btn-icon">↻</span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Payment Details (shown after form submission) -->
                <div class="card" id="paymentDetails" style="display: none;">
                    <h3>Payment Details</h3>
                    <div class="payment-info">
                        <div class="deposit-summary">
                            <h4>Deposit Summary</h4>                            <div class="summary-item">
                                <span class="label">Amount to Deposit:</span>
                                <span class="value" id="depositAmountDisplay">0</span>
                                <span class="currency">USDT</span>
                            </div>
                            <div class="summary-item" id="depositNoteDisplay" style="display: none;">
                                <span class="label">Note:</span>
                                <span class="value" id="depositNoteValue"></span>
                            </div>
                        </div>
                          <div class="payment-instructions">
                            <h4>Payment Instructions</h4>
                            
                            <!-- Important: Do Not Leave Page Warning -->
                            <div class="instruction-step" style="border: 2px solid #dc3545; background: #fff5f5;">
                                <div class="step-number" style="background: #dc3545; color: white;">⚠️</div>
                                <div class="step-content">
                                    <h5 style="color: #dc3545; font-weight: bold;">🚨 IMPORTANT: Do Not Leave This Page</h5>
                                    <div style="background: #fff; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545; margin: 10px 0;">
                                        <p style="margin: 0; font-weight: 500; color: #721c24;">
                                            <strong>Please stay on this page until your payment is confirmed!</strong>
                                        </p>
                                        <ul style="margin: 10px 0 0 0; color: #721c24;">
                                            <li>Keep this browser tab open while making your payment</li>
                                            <li>Do not refresh, close, or navigate away from this page</li>
                                            <li>Your deposit will be automatically detected and confirmed</li>
                                            <li>You will see a confirmation message once payment is received</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="instruction-step">
                                <div class="step-number">1</div><div class="step-content">
                                    <h5>Send USDT to Your Wallet Address</h5>
                                    <div class="wallet-address-section">
                                        <label for="paymentAddress">Your Deposit Address:</label>
                                        <div class="address-display-container">
                                            <div id="paymentAddress" class="deposit-address-text">Loading wallet address...</div>
                                            <button id="copyPaymentBtn" class="btn btn-outline btn-sm" type="button">Copy Address</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="instruction-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h5>Scan QR Code (Optional)</h5>
                                    <div class="qr-code-section">
                                        <div id="paymentQR" class="qr-code-container">
                                            <!-- QR code will be generated here -->
                                        </div>
                                        <p class="qr-help">Scan this QR code with your TRON wallet app (USDT/TRC20)</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="instruction-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h5>Wait for Confirmation</h5>
                                    <p>Your deposit will be automatically credited after <strong>19 network confirmations</strong>. This typically takes 1-3 minutes.</p>
                                </div>
                            </div>
                        </div>
                          <div class="deposit-warnings">
                            <h4>⚠️ Important Notes:</h4>
                            <ul>
                                <li><strong>Only send USDT (TRC-20)</strong> to this address</li>
                                <li><strong>Send exactly</strong> <span id="exactAmountWarning">the amount you specified</span> or more</li>
                                <li>Deposits are <strong>automatically detected</strong> and credited to your account</li>
                                <li>Network confirmations required: <strong>19 blocks</strong></li>
                                <li><strong>Do not send</strong> TRX or other tokens to this address</li>
                                <li>Keep your transaction hash (TXID) for support if needed</li>
                            </ul>
                        </div>
                        
                        <div class="deposit-actions">
                            <button id="newDepositBtn" class="btn btn-outline">Make Another Deposit</button>
                            <button id="viewTransactionsBtn" class="btn btn-secondary" onclick="window.location.href='../transactions.php'">View Transactions</button>
                        </div>
                    </div>
                </div>

                <!-- Deposit Instructions (always visible) -->
                <div class="card">
                    <h3>How Deposits Work</h3>
                    <div class="deposit-guide">                        <div class="guide-item">
                            <div class="guide-icon">💰</div>
                            <div class="guide-content">
                                <h5>Automatic Detection</h5>
                                <p>Deposits are automatically detected when you send USDT (TRC-20) to your wallet address</p>
                            </div>
                        </div>
                        <div class="guide-item">
                            <div class="guide-icon">⏱️</div>
                            <div class="guide-content">
                                <h5>Quick Processing</h5>
                                <p>Most deposits are credited within 1-3 minutes after network confirmation</p>
                            </div>
                        </div>
                        <div class="guide-item">
                            <div class="guide-icon">🔒</div>
                            <div class="guide-content">
                                <h5>Secure & Safe</h5>
                                <p>Your wallet address is unique and secure, only you can deposit to it</p>
                            </div>
                        </div>
                        <div class="guide-item">
                            <div class="guide-icon">📱</div>
                            <div class="guide-content">
                                <h5>QR Code Support</h5>
                                <p>Use the QR code to easily send deposits from mobile wallets</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Deposits -->
                <div class="card">
                    <h3>Recent Deposits</h3>
                    <div id="recentDepositsList">Loading...</div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>    <script src="js/qr-generator.js"></script>
    <script src="js/deposit.js"></script>
</body>
</html>
