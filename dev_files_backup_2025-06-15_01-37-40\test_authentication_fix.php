<?php
// Test authentication fix for investment creation
require_once 'frontend/config.php';
require_once 'frontend/api.php';

echo "Authentication Fix Test for Investment Creation\n";
echo "===============================================\n\n";

// Initialize session
FrontendConfig::initSession();

echo "Step 1: Testing Login and Session Setup\n";
echo "---------------------------------------\n";

// Test login first
$api = new APIWrapper();
$loginResult = $api->login('<EMAIL>', 'password123');

if (isset($loginResult['success']) && $loginResult['success']) {
    echo "✅ Login successful!\n";
    echo "   User ID: " . $loginResult['user']['id'] . "\n";
    echo "   Email: " . $loginResult['user']['email'] . "\n";
    echo "   Token: " . (isset($loginResult['token']) ? 'Present' : 'Missing') . "\n\n";
    
    // Store session data (like ajax.php does)
    $_SESSION['user_id'] = $loginResult['user']['id'];
    $_SESSION['email'] = $loginResult['user']['email'];
    $_SESSION['token'] = $loginResult['token'];
    $_SESSION['is_admin'] = $loginResult['user']['is_admin'] ?? false;
    
    echo "Session variables set:\n";
    echo "   user_id: " . ($_SESSION['user_id'] ?? 'NOT SET') . "\n";
    echo "   token: " . ($_SESSION['token'] ?? 'NOT SET') . "\n";
    echo "   email: " . ($_SESSION['email'] ?? 'NOT SET') . "\n\n";
} else {
    echo "❌ Login failed: " . ($loginResult['error'] ?? 'Unknown error') . "\n";
    exit(1);
}

echo "Step 2: Testing Balance API (Should Work)\n";
echo "----------------------------------------\n";

$balanceResult = $api->getBalance();
if (isset($balanceResult['success']) && $balanceResult['success']) {
    echo "✅ Balance API successful!\n";
    echo "   Balance: " . $balanceResult['balance'] . " USDT\n\n";
} else {
    echo "❌ Balance API failed: " . ($balanceResult['error'] ?? 'Unknown error') . "\n\n";
}

echo "Step 3: Testing Investment Creation (The Main Fix)\n";
echo "------------------------------------------------\n";

$investmentResult = $api->createInvestment(700, 'basic');
if (isset($investmentResult['success']) && $investmentResult['success']) {
    echo "✅ Investment creation successful!\n";
    echo "   Investment ID: " . ($investmentResult['investment_id'] ?? 'N/A') . "\n";
    echo "   Message: " . $investmentResult['message'] . "\n\n";
} else {
    echo "❌ Investment creation failed: " . ($investmentResult['error'] ?? $investmentResult['message'] ?? 'Unknown error') . "\n";
    
    if (isset($investmentResult['error']) && strpos($investmentResult['error'], 'authenticated') !== false) {
        echo "   ⚠️  This is the exact error users were experiencing!\n";
        echo "   The authentication fix may not be working correctly.\n\n";
    }
}

echo "Step 4: Testing Other Investment Endpoints\n";
echo "-----------------------------------------\n";

// Test get active investments
$activeResult = $api->getActiveInvestments();
if (isset($activeResult['success']) && $activeResult['success']) {
    echo "✅ Get active investments successful!\n";
    echo "   Active investments: " . count($activeResult['investments'] ?? []) . "\n";
} else {
    echo "❌ Get active investments failed: " . ($activeResult['error'] ?? 'Unknown error') . "\n";
}

// Test get investment history
$historyResult = $api->getInvestmentHistory();
if (isset($historyResult['success']) && $historyResult['success']) {
    echo "✅ Get investment history successful!\n";
    echo "   History records: " . count($historyResult['history'] ?? []) . "\n";
} else {
    echo "❌ Get investment history failed: " . ($historyResult['error'] ?? 'Unknown error') . "\n";
}

echo "\nTest Summary\n";
echo "============\n";

$tests = [
    'Login' => isset($loginResult['success']) && $loginResult['success'],
    'Balance API' => isset($balanceResult['success']) && $balanceResult['success'],
    'Investment Creation' => isset($investmentResult['success']) && $investmentResult['success'],
    'Active Investments' => isset($activeResult['success']) && $activeResult['success'],
    'Investment History' => isset($historyResult['success']) && $historyResult['success']
];

foreach ($tests as $test => $passed) {
    echo ($passed ? '✅' : '❌') . " $test\n";
}

$allPassed = !in_array(false, $tests, true);
echo "\nOverall Result: " . ($allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED') . "\n";

if ($allPassed) {
    echo "\n🎉 The authentication issue has been resolved!\n";
    echo "Users should now be able to create investments successfully.\n";
} else {
    echo "\n⚠️  Some issues remain. Please check the failing tests above.\n";
}

echo "\nNext Steps:\n";
echo "1. Test the fix using the debug tool: http://localhost:8080/debug_auth_investment.html\n";
echo "2. Test the actual investment flow: http://localhost:8080/user/make_investment.php\n";
echo "3. Verify the frontend authentication is working properly\n";
?>
