<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication & Investment Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .btn { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 14px;
            background: #007bff;
            color: white;
        }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .result {
            background: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        input { 
            padding: 8px; 
            margin: 5px; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Authentication & Investment Creation Test</h1>
        <p>This page tests the complete authentication and investment creation flow.</p>

        <div class="test-section">
            <h3>Step 1: Test Current Session</h3>
            <p>Check if you're currently authenticated</p>
            <button class="btn" onclick="checkSession()">Check Session Status</button>
            <div id="sessionResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 2: Login (if not authenticated)</h3>
            <p>Login to get proper session and token</p>
            <div>
                <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
                <input type="password" id="loginPassword" placeholder="Password" value="password123">
                <button class="btn" onclick="testLogin()">Login</button>
            </div>
            <div id="loginResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 3: Test Investment Creation</h3>
            <p>After authentication, test investment creation</p>
            <div>
                <input type="number" id="investAmount" placeholder="Amount" value="700" min="600">
                <select id="investPlan">
                    <option value="basic">Basic Plan</option>
                    <option value="premium">Premium Plan</option>
                    <option value="vip">VIP Plan</option>
                </select>
                <button class="btn" onclick="testInvestmentCreation()">Create Investment</button>
            </div>
            <div id="investmentResult"></div>
        </div>

        <div class="test-section">
            <h3>Debug Information</h3>
            <button class="btn" onclick="showDebugInfo()">Show Debug Info</button>
            <div id="debugInfo"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'result') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function checkSession() {
            try {
                const response = await fetch('http://localhost:8080/debug_session.php');
                const text = await response.text();
                showResult('sessionResult', text, 'success');
            } catch (error) {
                showResult('sessionResult', `Error checking session: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            if (!email || !password) {
                showResult('loginResult', 'Please enter email and password', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('loginResult', '✅ Login successful!\n' + JSON.stringify(result, null, 2), 'success');
                } else {
                    showResult('loginResult', '❌ Login failed:\n' + JSON.stringify(result, null, 2), 'error');
                }
            } catch (error) {
                showResult('loginResult', `❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testInvestmentCreation() {
            const amount = document.getElementById('investAmount').value;
            const plan = document.getElementById('investPlan').value;

            if (!amount || amount < 600) {
                showResult('investmentResult', 'Please enter a valid amount (minimum 600)', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'create_investment',
                        amount: parseFloat(amount),
                        plan: plan
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('investmentResult', '✅ Investment created successfully!\n' + JSON.stringify(result, null, 2), 'success');
                } else if (result.error) {
                    if (result.error === 'Not authenticated' || result.error === 'User not authenticated') {
                        showResult('investmentResult', '❌ Authentication Error:\n' + result.error + '\n\nPlease login first using Step 2', 'error');
                    } else {
                        showResult('investmentResult', '❌ Investment creation failed:\n' + JSON.stringify(result, null, 2), 'error');
                    }
                } else {
                    showResult('investmentResult', '⚠️ Unexpected response:\n' + JSON.stringify(result, null, 2), 'warning');
                }
            } catch (error) {
                showResult('investmentResult', `❌ Investment creation error: ${error.message}`, 'error');
            }
        }

        async function showDebugInfo() {
            try {
                // Test session check
                const sessionResp = await fetch('http://localhost:8080/debug_session.php');
                const sessionInfo = await sessionResp.text();

                // Test get_balance (another authenticated endpoint)
                const balanceResp = await fetch('http://localhost:8080/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_balance'
                    })
                });
                const balanceResult = await balanceResp.json();

                const debugOutput = `Session Information:
${sessionInfo}

Balance API Test:
${JSON.stringify(balanceResult, null, 2)}

Browser Info:
- User Agent: ${navigator.userAgent}
- Cookies Enabled: ${navigator.cookieEnabled}
- Current URL: ${window.location.href}`;

                showResult('debugInfo', debugOutput, 'success');
            } catch (error) {
                showResult('debugInfo', `Error getting debug info: ${error.message}`, 'error');
            }
        }

        // Initialize
        console.log('Authentication & Investment Test initialized');
        console.log('Frontend Server: http://localhost:8080');
        console.log('Backend API: http://localhost:8000');
    </script>
</body>
</html>
