<?php

declare(strict_types=1);

namespace Simbi\Tls\CronJob;

use Simbi\Tls\CronJob\Config\CronConfig;
use Simbi\Tls\CronJob\Services\PaymentConfirmationService;
use Simbi\Tls\CronJob\Services\TronGridService;

/**
 * Main Cron Runner Class
 * Orchestrates all scheduled tasks and cron jobs
 */
class CronRunner
{
    private PaymentConfirmationService $paymentService;
    private TronGridService $tronService;
    
    public function __construct()
    {
        // Load configuration
        CronConfig::load();
        
        // Set PHP configuration
        $this->setupPhpEnvironment();
        
        // Initialize services
        $this->paymentService = new PaymentConfirmationService();
        $this->tronService = new TronGridService();
    }
    
    /**
     * Run all scheduled tasks
     */
    public function runAll(): array
    {
        $startTime = microtime(true);
        $results = [];
        
        CronConfig::log('Starting cron job execution', 'info', 'main');
        
        try {
            // Run payment confirmation task
            $results['payment_confirmation'] = $this->runPaymentConfirmation();
            
            // Run investment processing task
            $results['investment_processing'] = $this->runInvestmentProcessing();
            
            // Run maintenance tasks
            $results['maintenance'] = $this->runMaintenanceTasks();
            
            $executionTime = round(microtime(true) - $startTime, 2);
            
            CronConfig::log("Cron job execution completed in {$executionTime}s", 'info', 'main');
            
            return [
                'success' => true,
                'execution_time' => $executionTime,
                'results' => $results,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
        } catch (\Exception $e) {
            $executionTime = round(microtime(true) - $startTime, 2);
            $errorMessage = 'Cron job execution failed: ' . $e->getMessage();
            
            CronConfig::log($errorMessage, 'error', 'main');
            
            return [
                'success' => false,
                'error' => $errorMessage,
                'execution_time' => $executionTime,
                'results' => $results,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }
    
    /**
     * Run payment confirmation task
     */
    public function runPaymentConfirmation(): array
    {
        try {
            CronConfig::log('Starting payment confirmation task', 'info', 'payments');
            
            $result = $this->paymentService->processPendingPayments();
            
            if ($result['success']) {
                $message = "Payment confirmation completed. Processed: {$result['processed']}/{$result['total_pending']}";
                CronConfig::log($message, 'info', 'payments');
                
                if (!empty($result['errors'])) {
                    foreach ($result['errors'] as $error) {
                        CronConfig::log("Payment error: {$error}", 'warning', 'payments');
                    }
                }
            } else {
                CronConfig::log("Payment confirmation failed: {$result['message']}", 'error', 'payments');
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $errorMessage = 'Payment confirmation task failed: ' . $e->getMessage();
            CronConfig::log($errorMessage, 'error', 'payments');
            
            return [
                'success' => false,
                'message' => $errorMessage
            ];
        }
    }
    
    /**
     * Run investment processing task
     */
    public function runInvestmentProcessing(): array
    {
        try {
            CronConfig::log('Starting investment processing task', 'info', 'investments');
            
            // Check if investment processing is enabled
            if (!CronConfig::get('INVESTMENT_PROCESSING_ENABLED', true)) {
                return [
                    'success' => true,
                    'message' => 'Investment processing is disabled',
                    'processed' => 0
                ];
            }
            
            // Check if it's time to process investments (daily at specified hour)
            $processingHour = (int)CronConfig::get('INVESTMENT_DAILY_PROCESSING_HOUR', 9);
            $currentHour = (int)date('H');
            
            if ($currentHour !== $processingHour) {
                return [
                    'success' => true,
                    'message' => "Investment processing scheduled for {$processingHour}:00, current time: {$currentHour}:00",
                    'processed' => 0
                ];
            }
            
            // Process daily investment returns
            $result = $this->processInvestmentReturns();
            
            CronConfig::log("Investment processing completed. Processed: {$result['processed']} investments", 'info', 'investments');
            
            return $result;
            
        } catch (\Exception $e) {
            $errorMessage = 'Investment processing task failed: ' . $e->getMessage();
            CronConfig::log($errorMessage, 'error', 'investments');
            
            return [
                'success' => false,
                'message' => $errorMessage,
                'processed' => 0
            ];
        }
    }
    
    /**
     * Process daily investment returns
     */
    private function processInvestmentReturns(): array
    {
        $db = CronConfig::getDatabaseConnection();
        $processed = 0;
        $errors = [];
        
        try {
            // Get all active investments that need daily processing
            $stmt = $db->prepare("
                SELECT i.*, ip.daily_rate, ip.duration, u.id as user_id
                FROM investments i
                JOIN investment_plans ip ON i.plan_id = ip.id
                JOIN users u ON i.user_id = u.id
                WHERE i.status = 'active'
                AND DATEDIFF(NOW(), i.created_at) < ip.duration
                AND (i.last_return_date IS NULL OR DATE(i.last_return_date) < CURDATE())
            ");
            
            $stmt->execute();
            $investments = $stmt->fetchAll();
            
            foreach ($investments as $investment) {
                try {
                    $dailyReturn = $investment['amount'] * $investment['daily_rate'];
                    
                    // Add return to user wallet
                    $updateStmt = $db->prepare("
                        UPDATE wallets 
                        SET balance = balance + ? 
                        WHERE user_id = ?
                    ");
                    $updateStmt->execute([$dailyReturn, $investment['user_id']]);
                    
                    // Update investment last return date
                    $investmentStmt = $db->prepare("
                        UPDATE investments 
                        SET last_return_date = CURDATE(),
                            total_earned = total_earned + ?
                        WHERE id = ?
                    ");
                    $investmentStmt->execute([$dailyReturn, $investment['id']]);
                    
                    // Log transaction
                    $transactionStmt = $db->prepare("
                        INSERT INTO transactions (
                            user_id, amount, type, description, status, created_at
                        ) VALUES (?, ?, 'investment_return', ?, 'completed', NOW())
                    ");
                    $transactionStmt->execute([
                        $investment['user_id'],
                        $dailyReturn,
                        "Daily return from investment #{$investment['id']}"
                    ]);
                    
                    $processed++;
                    
                } catch (\Exception $e) {
                    $errors[] = "Investment {$investment['id']}: " . $e->getMessage();
                }
            }
            
            return [
                'success' => true,
                'processed' => $processed,
                'total_investments' => count($investments),
                'errors' => $errors
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'processed' => $processed,
                'errors' => $errors
            ];
        }
    }
    
    /**
     * Run maintenance tasks
     */
    public function runMaintenanceTasks(): array
    {
        $tasks = [];
        
        try {
            // Clean old log files
            $tasks['log_cleanup'] = $this->cleanOldLogFiles();
            
            // Update investment statuses
            $tasks['investment_status_update'] = $this->updateInvestmentStatuses();
            
            // Database optimization
            $tasks['database_optimization'] = $this->optimizeDatabase();
            
            return [
                'success' => true,
                'tasks' => $tasks
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'tasks' => $tasks
            ];
        }
    }
    
    /**
     * Clean old log files
     */
    private function cleanOldLogFiles(): array
    {
        $logPath = CronConfig::get('LOG_PATH');
        $maxFiles = (int)CronConfig::get('LOG_MAX_FILES', 30);
        $deleted = 0;
        
        if (!is_dir($logPath)) {
            return ['success' => true, 'deleted' => 0, 'message' => 'Log directory does not exist'];
        }
        
        $files = glob($logPath . '*.log');
        
        // Sort files by modification time (oldest first)
        usort($files, function($a, $b) {
            return filemtime($a) - filemtime($b);
        });
        
        // Delete old files if we have more than the maximum
        if (count($files) > $maxFiles) {
            $filesToDelete = array_slice($files, 0, count($files) - $maxFiles);
            
            foreach ($filesToDelete as $file) {
                if (unlink($file)) {
                    $deleted++;
                }
            }
        }
        
        return [
            'success' => true,
            'deleted' => $deleted,
            'total_files' => count($files)
        ];
    }
    
    /**
     * Update investment statuses
     */
    private function updateInvestmentStatuses(): array
    {
        $db = CronConfig::getDatabaseConnection();
        
        try {
            // Mark completed investments
            $stmt = $db->prepare("
                UPDATE investments i
                JOIN investment_plans ip ON i.plan_id = ip.id
                SET i.status = 'completed', i.completed_at = NOW()
                WHERE i.status = 'active'
                AND DATEDIFF(NOW(), i.created_at) >= ip.duration
            ");
            
            $stmt->execute();
            $completed = $stmt->rowCount();
            
            return [
                'success' => true,
                'completed_investments' => $completed
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Optimize database
     */
    private function optimizeDatabase(): array
    {
        $db = CronConfig::getDatabaseConnection();
        
        try {
            // Optimize tables
            $tables = ['users', 'wallets', 'investments', 'transactions', 'investment_plans'];
            $optimized = 0;
            
            foreach ($tables as $table) {
                $stmt = $db->prepare("OPTIMIZE TABLE {$table}");
                $stmt->execute();
                $optimized++;
            }
            
            return [
                'success' => true,
                'optimized_tables' => $optimized
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Setup PHP environment for cron execution
     */
    private function setupPhpEnvironment(): void
    {
        // Set timezone
        $timezone = CronConfig::get('CRON_TIMEZONE', 'UTC');
        date_default_timezone_set($timezone);
        
        // Set memory limit
        $memoryLimit = CronConfig::get('CRON_MEMORY_LIMIT', '256M');
        ini_set('memory_limit', $memoryLimit);
        
        // Set execution time limit
        $timeLimit = (int)CronConfig::get('CRON_MAX_EXECUTION_TIME', 300);
        set_time_limit($timeLimit);
        
        // Set error reporting for production
        if (CronConfig::isProduction()) {
            error_reporting(E_ERROR | E_WARNING | E_PARSE);
            ini_set('display_errors', '0');
            ini_set('log_errors', '1');
        } else {
            error_reporting(E_ALL);
            ini_set('display_errors', '1');
        }
    }
    
    /**
     * Get cron job status and statistics
     */
    public function getStatus(): array
    {
        try {
            $paymentStats = $this->paymentService->getPaymentStatistics();
            
            return [
                'success' => true,
                'status' => 'healthy',
                'last_run' => date('Y-m-d H:i:s'),
                'environment' => CronConfig::getEnvironment(),
                'php_version' => PHP_VERSION,
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
                'payment_statistics' => $paymentStats['statistics'] ?? null
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }
}
