<?php
// Test investment creation API fix
require_once 'frontend/config.php';
require_once 'frontend/api.php';

// Initialize session for testing
session_start();

// Set up test session data (you'll need to replace with real session data)
$_SESSION['user_id'] = 1;  // Replace with actual user ID
$_SESSION['token'] = 'test_token';  // Replace with actual token
$_SESSION['email'] = '<EMAIL>';  // Replace with actual email

echo "Testing Investment Creation API Fix\n";
echo "==================================\n\n";

// Test the AJAX endpoint
echo "1. Testing AJAX endpoint with corrected parameters...\n";

// Simulate the request data that comes from the frontend
$testData = [
    'action' => 'create_investment',
    'amount' => 700,
    'plan' => 'basic'
];

// Convert to JSON like the frontend would send
$jsonData = json_encode($testData);

echo "Sending data: " . $jsonData . "\n";

// Use cURL to test the AJAX endpoint
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/tls/frontend/ajax.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($jsonData)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $httpCode\n";
echo "Response: $response\n\n";

// Parse the response
$responseData = json_decode($response, true);
if ($responseData) {
    echo "Parsed Response:\n";
    print_r($responseData);
} else {
    echo "Failed to parse JSON response\n";
    echo "Raw response: $response\n";
}

echo "\n2. Testing API wrapper directly...\n";

// Test the API wrapper
$api = new APIWrapper();
$api->setToken($_SESSION['token']);

try {
    $result = $api->createInvestment(700, 'basic');
    echo "API Wrapper Result:\n";
    print_r($result);
} catch (Exception $e) {
    echo "API Wrapper Error: " . $e->getMessage() . "\n";
}

echo "\nTest complete!\n";
?>
