# 🔧 Sweep Funds "Invalid JSON Response" - Complete Fix Guide

## 🚨 Problem Summary
The `sweep_funds` functionality is returning "Invalid JSON response" error, preventing users from sweeping their wallet funds to the master wallet.

## 🔍 Root Cause Analysis

The issue can be caused by several factors:

1. **Missing Master Wallet Configuration**
2. **P<PERSON> Errors Breaking JSON Output**
3. **TRON API Connection Issues**
4. **Inconsistent Response Formats**
5. **Authentication Problems**

## ✅ Solution Implementation

### Step 1: Check Configuration

Run the configuration check script:

```bash
cd backend
php check_sweep_config.php
```

This will verify:
- ✅ MASTER_ADDRESS is configured
- ✅ MASTER_PRIVATE_KEY is configured  
- ✅ TRON_NETWORK is set
- ✅ USDT_CONTRACT is configured
- ✅ Database connection works

### Step 2: Configure Master Wallet

Add these to your `backend/.env` file:

```env
# Master wallet configuration (REQUIRED for sweep functionality)
MASTER_ADDRESS=your_master_wallet_address_here
MASTER_PRIVATE_KEY=your_master_wallet_private_key_here

# TRON network configuration
TRON_NETWORK=nile  # Use 'mainnet' for production
USDT_CONTRACT=TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf  # Nile testnet USDT

# For mainnet, use:
# USDT_CONTRACT=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
```

### Step 3: Test the Fix

1. **Login to the frontend** to establish a session
2. **Run the debug tool**:
   ```
   http://localhost/tls/frontend/debug_sweep_funds.php
   ```
3. **Run the test script**:
   ```bash
   php test_sweep_functionality.php
   ```

### Step 4: Use the Debug Tool

Access the debug tool at:
```
http://localhost/tls/frontend/debug_sweep_funds.php
```

This tool will:
- ✅ Check your session and authentication
- ✅ Verify backend configuration
- ✅ Test the API directly
- ✅ Show raw responses for debugging

## 🛠️ Code Improvements Made

### 1. Enhanced WalletController

- ✅ Better input validation
- ✅ Consistent response format
- ✅ Detailed error logging
- ✅ Proper success/error handling

### 2. Improved TronService

- ✅ Enhanced error handling
- ✅ Better logging for debugging
- ✅ Fee reserve for TRX sweeps
- ✅ Separate handling for USDT vs TRX

### 3. Robust AJAX Handler

- ✅ Guaranteed JSON responses
- ✅ Exception handling
- ✅ Consistent response format

## 🔧 Manual Testing Steps

### Test 1: Configuration Check
```bash
cd backend
php check_sweep_config.php
```

### Test 2: Frontend Debug Tool
1. Login to the frontend
2. Visit: `http://localhost/tls/frontend/debug_sweep_funds.php`
3. Click "Test Sweep Funds"
4. Check the response format

### Test 3: Complete Functionality Test
```bash
php test_sweep_functionality.php
```

## 📋 Expected Responses

### Successful Sweep (with funds):
```json
{
  "success": true,
  "message": "Funds swept successfully",
  "transaction_hash": "abc123...",
  "amount": "100.000000",
  "status": "swept"
}
```

### Successful Sweep (no funds):
```json
{
  "success": true,
  "message": "No funds available to sweep",
  "amount": "0",
  "status": "no_funds"
}
```

### Error Response:
```json
{
  "success": false,
  "error": "Master address not configured in environment",
  "code": 500
}
```

## 🚨 Common Issues & Solutions

### Issue 1: "Master address not configured"
**Solution**: Add `MASTER_ADDRESS` to your `.env` file

### Issue 2: "Wallet not found"
**Solution**: Create a wallet first using the "Create Wallet" button

### Issue 3: "Invalid or expired token"
**Solution**: Login again to refresh your session

### Issue 4: Still getting "Invalid JSON"
**Solution**: 
1. Check PHP error logs
2. Enable debug mode in `.env`: `API_DEBUG=true`
3. Use the debug tool to see raw responses

## 🔒 Security Notes

- ✅ Master private key is stored securely in environment variables
- ✅ All transactions are signed server-side
- ✅ User authentication is required for all sweep operations
- ✅ Input validation prevents malicious requests

## 📊 Monitoring & Logging

The improved code includes extensive logging:

- All sweep attempts are logged
- Balance checks are logged
- Transaction results are logged
- Errors include stack traces

Check your PHP error logs for detailed information.

## 🎯 Next Steps

1. **Run the configuration check**
2. **Configure your master wallet**
3. **Test using the debug tool**
4. **Verify functionality in the frontend**

The sweep functionality should now work correctly with proper JSON responses!
