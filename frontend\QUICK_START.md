# 🚀 TLS Crypto Wallet - Quick Start Guide

## 🎯 **Get Started in 3 Easy Steps**

### **Step 1: Access the Application**
Open your web browser and visit: **http://localhost:8080**

### **Step 2: Create Your Account**
1. Click on the **"Register"** tab
2. Enter your email address
3. Create a secure password (minimum 6 characters)
4. Confirm your password
5. Click **"Register"** button

### **Step 3: Explore Your Dashboard**
After registration, you'll be automatically logged in and redirected to your dashboard where you can:
- 📊 View your account overview
- 💼 Manage your crypto wallets  
- 📈 View transaction history
- 💰 Make deposits with QR codes

---

## 📱 **Mobile Users**

The application is **mobile-first** designed! Simply:
1. Open http://localhost:8080 on your mobile browser
2. The interface automatically adapts to your screen size
3. Enjoy touch-friendly navigation and optimized layouts

---

## 🔧 **Admin Access**

To access admin features:
1. Register a regular account first
2. Use the backend API or database to promote your account to admin
3. Visit **http://localhost:8080/admin.php** for the admin dashboard

---

## 🧪 **Test Features**

Want to test all features? Visit: **http://localhost:8080/api-test.html**
- Test registration and login flows
- Verify API connectivity
- Check database integrity
- Run integration tests

---

## 📞 **Need Help?**

- **Full Documentation**: See `README.md` for detailed information
- **API Testing**: Use `api-test.html` for debugging
- **Status Report**: Check `DEPLOYMENT_STATUS.md` for technical details

---

## ✨ **Key Features Available Now**

✅ **User Registration & Login**  
✅ **Mobile-Responsive Design**  
✅ **Secure Session Management**  
✅ **Role-Based Access Control**  
✅ **Real-Time Dashboard Updates**  
✅ **Admin Panel with User Management**  
✅ **Transaction History & Analytics**  
✅ **QR Code Generation for Deposits**

**Start exploring your new crypto wallet interface today!** 🎉
