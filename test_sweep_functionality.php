<?php
/**
 * Test Sweep Functionality
 * This script tests the sweep funds functionality end-to-end
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

echo "🧪 Testing Sweep Functionality\n";
echo "==============================\n\n";

// Check if user is logged in
if (!isset($_SESSION['token']) || !isset($_SESSION['user_id'])) {
    echo "❌ No active session found. Please login first.\n";
    echo "You can login through the frontend interface.\n";
    exit(1);
}

echo "✅ Session found for user ID: " . $_SESSION['user_id'] . "\n\n";

// Include required files
require_once 'backend/vendor/autoload.php';
require_once 'frontend/api_psr4.php';

try {
    // Test 1: Configuration Check
    echo "1. Checking Configuration...\n";
    echo "----------------------------\n";
    
    use Simbi\Tls\Config\Config;
    Config::load();
    
    $masterAddress = Config::get('MASTER_ADDRESS', '');
    $usdtContract = Config::get('USDT_CONTRACT', '');
    $tronNetwork = Config::get('TRON_NETWORK', 'nile');
    
    if (empty($masterAddress)) {
        echo "❌ MASTER_ADDRESS not configured\n";
        echo "Please add MASTER_ADDRESS to your .env file\n\n";
        exit(1);
    } else {
        echo "✅ Master address configured: " . substr($masterAddress, 0, 10) . "...\n";
    }
    
    echo "✅ TRON Network: {$tronNetwork}\n";
    echo "✅ USDT Contract: " . ($usdtContract ?: 'Not configured (will use TRX)') . "\n\n";
    
    // Test 2: API Wrapper Test
    echo "2. Testing API Wrapper...\n";
    echo "-------------------------\n";
    
    $api = new APIWrapper();
    $api->setToken($_SESSION['token']);
    
    // Test wallet retrieval first
    echo "Getting user wallet...\n";
    $walletResult = $api->getWallet();
    
    if (isset($walletResult['error'])) {
        echo "❌ Failed to get wallet: " . $walletResult['error'] . "\n";
        
        // Try to create wallet if it doesn't exist
        if (strpos($walletResult['error'], 'not found') !== false) {
            echo "Attempting to create wallet...\n";
            $createResult = $api->createWallet();
            
            if (isset($createResult['success']) && $createResult['success']) {
                echo "✅ Wallet created successfully\n";
                $walletResult = $api->getWallet();
            } else {
                echo "❌ Failed to create wallet: " . ($createResult['error'] ?? 'Unknown error') . "\n";
                exit(1);
            }
        } else {
            exit(1);
        }
    }
    
    if (isset($walletResult['address'])) {
        echo "✅ Wallet found: " . $walletResult['address'] . "\n";
        echo "Balance: " . ($walletResult['balance_formatted'] ?? '0') . " USDT\n\n";
    } else {
        echo "❌ Invalid wallet response\n";
        exit(1);
    }
    
    // Test 3: Sweep Funds Test
    echo "3. Testing Sweep Funds...\n";
    echo "-------------------------\n";
    
    echo "Attempting to sweep funds...\n";
    $sweepResult = $api->sweepFunds();
    
    echo "Raw sweep result:\n";
    print_r($sweepResult);
    echo "\n";
    
    if (isset($sweepResult['success'])) {
        if ($sweepResult['success']) {
            echo "✅ Sweep operation successful!\n";
            
            if (isset($sweepResult['status'])) {
                switch ($sweepResult['status']) {
                    case 'swept':
                        echo "💰 Funds swept successfully\n";
                        echo "Amount: " . ($sweepResult['amount'] ?? 'Unknown') . "\n";
                        echo "Transaction: " . ($sweepResult['transaction_hash'] ?? 'N/A') . "\n";
                        break;
                        
                    case 'no_funds':
                        echo "ℹ️  No funds available to sweep\n";
                        break;
                        
                    default:
                        echo "ℹ️  Status: " . $sweepResult['status'] . "\n";
                }
            }
            
            if (isset($sweepResult['message'])) {
                echo "Message: " . $sweepResult['message'] . "\n";
            }
        } else {
            echo "❌ Sweep operation failed\n";
            echo "Error: " . ($sweepResult['error'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ Invalid sweep response format\n";
        echo "Expected 'success' field in response\n";
    }
    
    echo "\n";
    
    // Test 4: JSON Validation
    echo "4. JSON Response Validation...\n";
    echo "------------------------------\n";
    
    $jsonString = json_encode($sweepResult);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ Response is valid JSON\n";
        echo "JSON length: " . strlen($jsonString) . " characters\n";
    } else {
        echo "❌ Response is not valid JSON\n";
        echo "JSON Error: " . json_last_error_msg() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test failed with exception: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Test complete!\n";
?>
