<?php
// Quick PHP test to verify the balance calculation is working
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Balance Fix Verification Test ===\n";
echo "Testing WalletService and WalletController balance calculation\n\n";

// Test 1: Check if files exist and have our changes
echo "1. Checking file modifications:\n";

$walletServiceFile = __DIR__ . '/backend/src/Services/WalletService.php';
$walletControllerFile = __DIR__ . '/backend/src/Controllers/WalletController.php';

if (file_exists($walletServiceFile)) {
    $serviceContent = file_get_contents($walletServiceFile);
    if (strpos($serviceContent, 'total_invested FROM investments') !== false) {
        echo "✅ WalletService.php has the investment calculation fix\n";
    } else {
        echo "❌ WalletService.php is missing the investment calculation\n";
    }
} else {
    echo "❌ WalletService.php not found\n";
}

if (file_exists($walletControllerFile)) {
    $controllerContent = file_get_contents($walletControllerFile);
    if (strpos($controllerContent, 'use Simbi\Tls\Services\WalletService') !== false) {
        echo "✅ WalletController.php has WalletService dependency\n";
    } else {
        echo "❌ WalletController.php is missing WalletService dependency\n";
    }
    
    if (strpos($controllerContent, '$this->walletService->getBalance') !== false) {
        echo "✅ WalletController.php uses WalletService for balance calculation\n";
    } else {
        echo "❌ WalletController.php is not using WalletService for balance\n";
    }
} else {
    echo "❌ WalletController.php not found\n";
}

echo "\n2. Testing syntax (PHP parse check):\n";

// Check for syntax errors
$serviceCheck = shell_exec("php -l \"$walletServiceFile\" 2>&1");
if (strpos($serviceCheck, 'No syntax errors') !== false) {
    echo "✅ WalletService.php syntax is valid\n";
} else {
    echo "❌ WalletService.php has syntax errors:\n$serviceCheck\n";
}

$controllerCheck = shell_exec("php -l \"$walletControllerFile\" 2>&1");
if (strpos($controllerCheck, 'No syntax errors') !== false) {
    echo "✅ WalletController.php syntax is valid\n";
} else {
    echo "❌ WalletController.php has syntax errors:\n$controllerCheck\n";
}

echo "\n3. Investment.js fix verification:\n";
$investJsFile = __DIR__ . '/frontend/user/js/invest.js';
if (file_exists($investJsFile)) {
    $jsContent = file_get_contents($investJsFile);
    if (strpos($jsContent, 'response.error || response.message') !== false) {
        echo "✅ invest.js has the undefined error fix\n";
    } else {
        echo "❌ invest.js is missing the undefined error fix\n";
    }
} else {
    echo "❌ invest.js not found\n";
}

echo "\n=== Summary ===\n";
echo "Expected Result:\n";
echo "- User ID 1 should show 30 USDT balance (630 wallet - 600 investments)\n";
echo "- Dashboard and investment pages should display 30.00 USDT\n";
echo "- No more 'undefined' errors in the browser console\n\n";

echo "Next Steps:\n";
echo "1. Refresh your browser pages\n";
echo "2. Clear browser cache if needed\n";
echo "3. Test the balance display on dashboard and investment pages\n";
echo "4. Check browser console for any remaining errors\n";

echo "\n=== Test Complete ===\n";
?>
