<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Balance Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #b8daff; }
        .balance-display { font-size: 24px; font-weight: bold; text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; margin: 20px 0; }
        button { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Final Balance Fix Test</h1>
        <p>This test verifies that the balance calculation fix is working correctly.</p>
        
        <div class="balance-display" id="balanceDisplay">
            Balance: Loading...
        </div>
        
        <div style="text-align: center;">
            <button onclick="runCompleteTest()" id="testBtn">Run Complete Test</button>
            <button onclick="testDashboard()" disabled id="dashboardBtn">Open Dashboard</button>
            <button onclick="testInvestment()" disabled id="investmentBtn">Open Investment Page</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function addStatus(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function runCompleteTest() {
            const balanceDisplay = document.getElementById('balanceDisplay');
            const results = document.getElementById('results');
            
            results.innerHTML = '';
            balanceDisplay.textContent = 'Balance: Testing...';
            
            try {
                addStatus('🔄 Starting complete balance test...', 'info');
                
                // Step 1: Login
                addStatus('Step 1: Authenticating user...', 'info');
                const loginResponse = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const loginResult = await loginResponse.json();
                
                if (!loginResult.success) {
                    addStatus('❌ Authentication failed: ' + (loginResult.error || 'Unknown error'), 'error');
                    balanceDisplay.textContent = 'Balance: Auth Failed';
                    return;
                }
                
                addStatus('✅ Authentication successful!', 'success');
                
                // Step 2: Get balance
                addStatus('Step 2: Fetching balance using fixed API...', 'info');
                const balanceResponse = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'get_balance' })
                });
                
                const balanceResult = await balanceResponse.json();
                
                addStatus('Raw API Response:', 'info');
                addStatus(`<pre>${JSON.stringify(balanceResult, null, 2)}</pre>`, 'info');
                
                if (balanceResult.success) {
                    const balance = parseFloat(balanceResult.balance) || 0;
                    balanceDisplay.textContent = `Balance: ${balance.toFixed(2)} USDT`;
                    
                    addStatus(`✅ Balance API successful! Retrieved: ${balance} USDT`, 'success');
                    
                    // Verify the balance is correct
                    if (balance === 30) {
                        addStatus('🎉 PERFECT! Balance is exactly 30 USDT as expected!', 'success');
                        addStatus('✅ Fix confirmed: 630 (wallet) - 600 (investments) = 30 USDT', 'success');
                    } else if (balance > 0 && balance < 630) {
                        addStatus(`✅ Balance calculation working! Got ${balance} USDT (not 630)`, 'success');
                        addStatus('ℹ️ Balance may differ based on actual investment data', 'info');
                    } else if (balance === 630) {
                        addStatus('⚠️ Balance is still showing raw wallet amount (630). Fix may not be active.', 'error');
                    } else {
                        addStatus(`ℹ️ Unexpected balance: ${balance} USDT`, 'info');
                    }
                    
                    // Enable navigation buttons
                    document.getElementById('dashboardBtn').disabled = false;
                    document.getElementById('investmentBtn').disabled = false;
                    
                } else {
                    const errorMsg = balanceResult.error || balanceResult.message || 'Unknown error';
                    addStatus(`❌ Balance API failed: ${errorMsg}`, 'error');
                    balanceDisplay.textContent = 'Balance: API Error';
                }
                
                addStatus('Test complete! You can now test the actual pages.', 'info');
                
            } catch (error) {
                addStatus(`❌ Test error: ${error.message}`, 'error');
                balanceDisplay.textContent = 'Balance: Error';
            }
        }

        function testDashboard() {
            addStatus('🔗 Opening Dashboard in new tab...', 'info');
            window.open('frontend/user/dashboard.php', '_blank');
        }

        function testInvestment() {
            addStatus('🔗 Opening Investment page in new tab...', 'info');
            window.open('frontend/user/invest.php', '_blank');
        }

        // Auto-run test when page loads
        window.addEventListener('load', () => {
            setTimeout(runCompleteTest, 1000);
        });
    </script>
</body>
</html>
