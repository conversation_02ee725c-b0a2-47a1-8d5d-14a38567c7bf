/* Authentication page styles */
.auth-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
    color: white;
}

.auth-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.auth-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

.auth-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
}

.tab-btn {
    flex: 1;
    padding: 12px 24px;
    border: none;
    background: transparent;
    color: white;
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.tab-btn.active {
    background: white;
    color: #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.tab-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.2);
}

.auth-form {
    background: white;
    padding: 32px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 400px;
    display: none;
}

.auth-form.active {
    display: block;
    animation: fadeInUp 0.4s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-form .form-group {
    margin-bottom: 24px;
}

.auth-form label {
    color: #555;
    font-weight: 600;
    margin-bottom: 6px;
    display: block;
}

.auth-form input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.auth-form input:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
}

.auth-form .btn {
    width: 100%;
    padding: 14px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.auth-form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.auth-links {
    text-align: center;
    margin-top: 20px;
}

.auth-links a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-links a:hover {
    color: #764ba2;
    text-decoration: underline;
}

.auth-container .message {
    width: 100%;
    max-width: 400px;
    margin-top: 20px;
    border-radius: 8px;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
    .auth-container {
        padding: 16px;
    }
    
    .auth-header h1 {
        font-size: 2rem;
    }
    
    .auth-header p {
        font-size: 1rem;
    }
    
    .auth-form {
        padding: 24px;
    }
    
    .tab-btn {
        padding: 10px 16px;
        font-size: 14px;
        min-width: 100px;
    }
}

@media (min-width: 768px) {
    .auth-container {
        padding: 40px;
    }
    
    .auth-form {
        padding: 40px;
    }
}

/* Loading state */
.auth-form .btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.auth-form .btn.loading::after {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 8px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
