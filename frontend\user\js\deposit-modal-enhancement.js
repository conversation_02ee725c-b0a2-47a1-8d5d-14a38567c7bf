// Enhanced Deposit Success Modal JavaScript Enhancement
// This script enhances the existing deposit success modal functionality

document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 Enhanced Deposit Success Modal Script Loaded');
    
    // Override the existing closeDepositSuccessAlert function to add enhanced behavior
    window.closeDepositSuccessAlert = function() {
        const overlay = document.getElementById('depositSuccessOverlay');
        if (overlay) {
            // Add fade out animation
            overlay.style.animation = 'depositModalFadeOut 0.3s ease-in forwards';
            
            setTimeout(() => {
                overlay.remove();
                // Restore body scroll
                document.body.style.overflow = '';
            }, 300);
        } else {
            // Fallback: just restore body scroll
            document.body.style.overflow = '';
        }
    };
    
    // Enhanced showDepositSuccessAlert function with proper modal behavior
    window.showDepositSuccessAlert = function(amount, newBalance) {
        console.log('🎉 Showing enhanced deposit success modal');
        
        // Create success modal/alert
        const alertHTML = `
            <div class="deposit-success-overlay" id="depositSuccessOverlay">
                <div class="deposit-success-modal">
                    <div class="success-header">
                        <div class="success-icon">✅</div>
                        <h3>Deposit Received!</h3>
                    </div>
                    <div class="success-content">
                        <p><strong>Amount Detected:</strong> ${amount.toFixed(6)} USDT</p>
                        <p><strong>New Balance:</strong> ${newBalance.toFixed(6)} USDT</p>
                        <p>Your deposit has been automatically detected and will be credited to your account shortly.</p>
                    </div>
                    <div class="success-actions">
                        <button class="btn btn-primary" onclick="closeDepositSuccessAlert()">Continue</button>
                        <button class="btn btn-success" onclick="window.location.href='invest.php'">Invest Now</button>
                        <button class="btn btn-secondary" onclick="window.location.href='wallet.php'">View Wallet</button>
                    </div>
                </div>
            </div>
        `;
        
        // Add to page
        document.body.insertAdjacentHTML('beforeend', alertHTML);
        
        // Prevent body scroll when modal is open
        document.body.style.overflow = 'hidden';
        
        // Add click outside to close functionality
        const overlay = document.getElementById('depositSuccessOverlay');
        if (overlay) {
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeDepositSuccessAlert();
                }
            });
            
            // Add escape key to close
            const handleEscape = function(e) {
                if (e.key === 'Escape') {
                    closeDepositSuccessAlert();
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);
        }
        
        console.log('✅ Enhanced deposit success modal displayed with proper positioning');
    };
    
    // Test function for demonstration purposes
    window.testDepositModal = function() {
        showDepositSuccessAlert(125.50, 1275.50);
    };
    
    console.log('🚀 Enhanced deposit modal functionality is ready');
    console.log('💡 Test with: testDepositModal() in console');
});

console.log('📦 Enhanced Deposit Success Modal Enhancement Script Loaded');
