<?php
/**
 * Test the specific case: 630 investment with 630+ balance
 */

echo "=== TESTING 630 INVESTMENT SPECIFICALLY ===\n\n";

// Test Configuration
$frontendUrl = 'http://localhost:8080';
$testEmail = '<EMAIL>';
$testPassword = 'password123';

// Cookie jar to maintain session across requests
$cookieJar = tempnam(sys_get_temp_dir(), 'test_630');

// Function to make HTTP requests with session maintenance
function makeRequest($url, $data = null, $cookieJar = null, $method = 'GET') {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if ($cookieJar) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieJar);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieJar);
    }
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Login
$loginData = json_encode([
    'action' => 'login',
    'email' => $testEmail,
    'password' => $testPassword
]);

$loginResult = makeRequest("$frontendUrl/frontend/ajax.php", $loginData, $cookieJar, 'POST');
$loginResponse = json_decode($loginResult['response'], true);

if (!isset($loginResponse['success']) || !$loginResponse['success']) {
    echo "❌ Login failed\n";
    exit(1);
}

echo "✅ Logged in successfully\n";

// Test 630 investment
echo "\nTesting 630 USDT investment:\n";
echo "============================\n";

$investmentData = json_encode([
    'action' => 'create_investment',
    'plan' => 'basic',
    'amount' => 630
]);

$investmentResult = makeRequest("$frontendUrl/frontend/ajax.php", $investmentData, $cookieJar, 'POST');
$investmentResponse = json_decode($investmentResult['response'], true);

echo "Investment Response:\n";
echo json_encode($investmentResponse, JSON_PRETTY_PRINT) . "\n\n";

if (isset($investmentResponse['success']) && $investmentResponse['success']) {
    echo "🎉 SUCCESS! 630 USDT investment created successfully!\n";
    echo "Investment ID: " . ($investmentResponse['investment_id'] ?? 'Not provided') . "\n";
    echo "Message: " . ($investmentResponse['message'] ?? 'Not provided') . "\n";
    
    echo "\n✅ ISSUE RESOLVED!\n";
    echo "The 'Insufficient balance' error when trying to invest 630 USDT with 630+ balance has been fixed.\n";
    
} else {
    echo "❌ Investment still failed:\n";
    echo "Error: " . ($investmentResponse['error'] ?? $investmentResponse['message'] ?? 'Unknown error') . "\n";
    
    if (isset($investmentResponse['message']) && strpos($investmentResponse['message'], 'Insufficient balance') !== false) {
        echo "\n🔍 The 'Insufficient balance' error persists - further investigation needed.\n";
    }
}

// Cleanup
if (file_exists($cookieJar)) {
    unlink($cookieJar);
}

echo "\n=== TEST COMPLETE ===\n";

?>
