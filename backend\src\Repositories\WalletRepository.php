<?php

namespace Simbi\Tls\Repositories;

use PDO;
use Exception;

class WalletRepository
{
    private PDO $pdo;

    public function __construct(PDO $pdo)
    {
        $this->pdo = $pdo;
    }    
      public function create(int $userId, string $address, string $hexAddress, string $privateKey): bool
    {
        try {
            $stmt = $this->pdo->prepare("INSERT INTO wallets (user_id, address, hex_address, private_key) VALUES (?, ?, ?, ?)");
            return $stmt->execute([$userId, $address, $hexAddress, $privateKey]);
        } catch (Exception $e) {
            error_log("Failed to create wallet: " . $e->getMessage());
            throw $e;
        }
    }

    public function findByUserId(int $userId): ?array
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM wallets WHERE user_id = ?");
            $stmt->execute([$userId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ?: null;
        } catch (Exception $e) {
            error_log("Failed to find wallet by user ID: " . $e->getMessage());
            return null;
        }
    }

    public function findByAddress(string $address): ?array
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM wallets WHERE address = ?");
            $stmt->execute([$address]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ?: null;
        } catch (Exception $e) {
            error_log("Failed to find wallet by address: " . $e->getMessage());
            return null;
        }
    }    public function findById(int $walletId): ?array
    {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM wallets WHERE id = ?");
            $stmt->execute([$walletId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result ?: null;
        } catch (Exception $e) {
            error_log("Failed to find wallet by ID: " . $e->getMessage());
            return null;
        }
    }

    public function updateBalance(int $walletId, float $newBalance): bool
    {
        try {
            $stmt = $this->pdo->prepare("UPDATE wallets SET balance = ?, updated_at = NOW() WHERE id = ?");
            return $stmt->execute([$newBalance, $walletId]);
        } catch (Exception $e) {
            error_log("Failed to update wallet balance: " . $e->getMessage());
            return false;
        }
    }
}
