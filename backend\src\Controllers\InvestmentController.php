<?php

namespace Simbi\Tls\Controllers;

use Simbi\Tls\Services\InvestmentService;
use Simbi\Tls\Services\WalletService;
use Simbi\Tls\Services\ConfigurationService;
use Exception;

class InvestmentController
{
    private $investmentService;
    private $walletService;
    private $configurationService;

    public function __construct()
    {
        $this->investmentService = new InvestmentService();
        $this->walletService = new WalletService();
        $this->configurationService = new ConfigurationService();
    }    public function createInvestment($data, $user = null)
    {
        try {
            // Validate input
            if (!isset($data['amount']) || !isset($data['plan'])) {
                return [
                    'success' => false,
                    'message' => 'Amount and plan are required'
                ];
            }

            $amount = floatval($data['amount']);
            $plan = $data['plan'];
            
            // Get user ID from authenticated user or session (for backward compatibility)
            $userId = null;
            if ($user && isset($user['id'])) {
                // Backend API with JWT authentication
                $userId = $user['id'];
            } elseif (isset($_SESSION['user_id'])) {
                // Frontend session authentication
                $userId = $_SESSION['user_id'];
            }

            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            // Validate plan
            $validPlans = ['basic', 'premium', 'vip'];
            if (!in_array($plan, $validPlans)) {
                return [
                    'success' => false,
                    'message' => 'Invalid investment plan'
                ];
            }

            // Get plan details
            $planDetails = $this->getPlanDetails($plan);
            
            // Validate minimum amount
            if ($amount < $planDetails['minAmount']) {
                return [
                    'success' => false,
                    'message' => "Minimum investment for {$planDetails['name']} is {$planDetails['minAmount']} TRX"
                ];
            }

            // Check user balance
            $balance = $this->walletService->getBalance($userId);
            if ($balance < $amount) {
                return [
                    'success' => false,
                    'message' => 'Insufficient balance'
                ];
            }            // Create investment
            $result = $this->investmentService->createInvestment($userId, $amount, $plan, $planDetails);
            
            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => $result['message'],
                    'investment_id' => $result['investment_id']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['message']
                ];
            }

        } catch (Exception $e) {
            error_log("Investment creation error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while creating investment'
            ];
        }
    }    public function getActiveInvestments($user = null)
    {
        try {
            // Get user ID from authenticated user or session (for backward compatibility)
            $userId = null;
            if ($user && isset($user['id'])) {
                // Backend API with JWT authentication
                $userId = $user['id'];
            } elseif (isset($_SESSION['user_id'])) {
                // Frontend session authentication
                $userId = $_SESSION['user_id'];
            }

            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            $investments = $this->investmentService->getActiveInvestments($userId);

            return [
                'success' => true,
                'investments' => $investments
            ];

        } catch (Exception $e) {
            error_log("Get active investments error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while fetching investments'
            ];
        }
    }    public function getInvestmentHistory($user = null)
    {
        try {
            // Get user ID from authenticated user or session (for backward compatibility)
            $userId = null;
            if ($user && isset($user['id'])) {
                // Backend API with JWT authentication
                $userId = $user['id'];
            } elseif (isset($_SESSION['user_id'])) {
                // Frontend session authentication
                $userId = $_SESSION['user_id'];
            }

            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            $history = $this->investmentService->getInvestmentHistory($userId);

            return [
                'success' => true,
                'history' => $history
            ];

        } catch (Exception $e) {
            error_log("Get investment history error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while fetching investment history'
            ];
        }
    }

    public function getBalance($data)
    {
        try {
            $userId = $_SESSION['user_id'] ?? null;

            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            $balance = $this->walletService->getBalance($userId);

            return [
                'success' => true,
                'balance' => $balance
            ];

        } catch (Exception $e) {
            error_log("Get balance error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while fetching balance'
            ];
        }
    }    public function getInvestmentPlans()
    {
        try {
            $result = $this->investmentService->getInvestmentPlans();
            
            if ($result['success']) {
                // Format response for frontend API consumption
                return [
                    'success' => true,
                    'data' => $result['data'],
                    'plans' => $result['data'] // Add backward compatibility
                ];
            } else {
                return $result;
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch investment plans: ' . $e->getMessage()
            ];
        }
    }private function getPlanDetails($plan)
    {
        try {
            // Fetch plan details from database
            $planData = $this->investmentService->getInvestmentPlans();
            
            if ($planData['success'] && !empty($planData['plans'])) {
                foreach ($planData['plans'] as $dbPlan) {
                    if ($dbPlan['plan_code'] === $plan) {
                        return [
                            'name' => $dbPlan['plan_name'],
                            'dailyRate' => floatval($dbPlan['daily_rate']),
                            'duration' => intval($dbPlan['duration']),
                            'minAmount' => floatval($dbPlan['min_amount'])
                        ];
                    }
                }
            }
            
            // Fallback to updated rates if database fetch fails
            $plans = [
                'basic' => [
                    'name' => 'Basic Plan',
                    'dailyRate' => 0.0167,
                    'duration' => 30,
                    'minAmount' => 600
                ],
                'premium' => [
                    'name' => 'Premium Plan',
                    'dailyRate' => 0.0167,
                    'duration' => 30,
                    'minAmount' => 2000
                ],
                'vip' => [
                    'name' => 'VIP Plan',
                    'dailyRate' => 0.0167,
                    'duration' => 30,
                    'minAmount' => 5000
                ]
            ];

            return $plans[$plan] ?? $plans['basic'];
            
        } catch (Exception $e) {
            error_log("Error fetching plan details: " . $e->getMessage());
              // Fallback to basic plan with updated rate
            return [
                'name' => 'Basic Plan',
                'dailyRate' => 0.0167,
                'duration' => 30,
                'minAmount' => 600
            ];
        }
    }    public function getInvestmentStatistics($user = null)
    {
        try {
            // Get user ID from authenticated user or session (for backward compatibility)
            $userId = null;
            if ($user && isset($user['id'])) {
                // Backend API with JWT authentication
                $userId = $user['id'];
            } elseif (isset($_SESSION['user_id'])) {
                // Frontend session authentication
                $userId = $_SESSION['user_id'];
            }

            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            $statistics = $this->investmentService->getInvestmentStatistics($userId);

            return [
                'success' => true,
                'statistics' => $statistics
            ];

        } catch (Exception $e) {
            error_log("Get investment statistics error: " . $e->getMessage());            return [
                'success' => false,
                'message' => 'An error occurred while fetching investment statistics'
            ];
        }
    }

    public function getInvestmentDetails($investmentId, $user = null)
    {
        try {
            // Get user ID from authenticated user or session (for backward compatibility)
            $userId = null;
            if ($user && isset($user['id'])) {
                // Backend API with JWT authentication
                $userId = $user['id'];
            } elseif (isset($_SESSION['user_id'])) {
                // Frontend session authentication
                $userId = $_SESSION['user_id'];
            }

            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            $investment = $this->investmentService->getInvestmentDetails($investmentId, $userId);

            if (!$investment) {
                return [
                    'success' => false,
                    'message' => 'Investment not found or access denied'
                ];
            }

            return [
                'success' => true,
                'investment' => $investment
            ];

        } catch (Exception $e) {
            error_log("Get investment details error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while fetching investment details'
            ];
        }
    }

    public function getInvestmentEarnings($investmentId, $user = null)
    {
        try {
            // Get user ID from authenticated user or session (for backward compatibility)
            $userId = null;
            if ($user && isset($user['id'])) {
                // Backend API with JWT authentication
                $userId = $user['id'];
            } elseif (isset($_SESSION['user_id'])) {
                // Frontend session authentication
                $userId = $_SESSION['user_id'];
            }

            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            $earnings = $this->investmentService->getInvestmentEarnings($investmentId, $userId);

            return [
                'success' => true,
                'earnings' => $earnings
            ];

        } catch (Exception $e) {
            error_log("Get investment earnings error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while fetching investment earnings'            ];
        }
    }

    public function getSystemConfiguration()
    {
        try {
            $result = $this->configurationService->getSystemConfiguration();
            
            if ($result['success']) {
                return [
                    'success' => true,
                    'data' => $result['data']
                ];
            } else {
                return $result;
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch system configuration: ' . $e->getMessage()
            ];
        }
    }
}
