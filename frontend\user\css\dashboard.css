/* Dashboard page styles */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.app-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.header-actions .btn {
    padding: 8px 16px;
    font-size: 14px;
    min-height: 36px;
}

/* Navigation */
.app-nav {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    position: sticky;
    top: 64px;
    z-index: 99;
}

.app-nav {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.app-nav::-webkit-scrollbar {
    display: none;
}

.nav-btn {
    background: none;
    border: none;
    padding: 16px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
    font-size: 14px;
}

.nav-btn:hover {
    color: #495057;
    background-color: #f8f9fa;
}

.nav-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

/* Main content */
.app-main {
    flex: 1;
    padding: 24px 0;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
}

.tab-content {
    display: none;
}

.tab-content {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
    opacity: 1;
    transform: translateY(0);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

/* Balance card */
.balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
}

.balance-card h3 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
    font-size: 1.1rem;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.balance-currency {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Stats card */
.stats-card {
    background: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

/* Stat card styling */
.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: box-shadow 0.3s ease;
}

.stat-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-icon svg {
    width: 24px;
    height: 24px;
}

.stat-info {
    flex: 1;
    min-width: 0;
}

.stat-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.stat-info p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    font-weight: 500;
}

/* Additional dashboard improvements */
.stats-grid .stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-grid .stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-grid .stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-grid .stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Loading states */
.stat-info h3:empty::after {
    content: "Loading...";
    color: #6c757d;
    font-weight: 400;
    font-size: 1rem;
}

#recentTransactionsList:empty::after {
    content: "Loading recent transactions...";
    color: #6c757d;
    font-style: italic;
    display: block;
    text-align: center;
    padding: 20px;
}

/* Recent transactions */
.recent-transactions {
    background: white;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    flex: 1;
}

.transaction-type {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.transaction-date {
    color: #6c757d;
    font-size: 12px;
    margin-top: 2px;
}

.transaction-amount {
    font-weight: 600;
    font-size: 14px;
}

.transaction-amount.positive {
    color: #28a745;
}

.transaction-amount.negative {
    color: #dc3545;
}

/* Wallet section */
.wallet-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

.wallet-info {
    display: grid;
    gap: 20px;
}

.wallet-field {
    margin-bottom: 20px;
}

.wallet-field label {
    display: block;
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
}

.wallet-address-input,
.wallet-balance-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #495057;
    min-height: 44px;
    box-sizing: border-box;
}

.wallet-address-input:focus,
.wallet-balance-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.address-display,
.balance-display {
    display: flex;
    align-items: center;
    gap: 12px;
}

.address-display .wallet-address-input,
.balance-display .wallet-balance-input {
    flex: 1;
}

/* Transactions section */
.transactions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.transaction-filters {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.transaction-filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.transaction-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.transaction-row {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
}

.transaction-row:last-child {
    border-bottom: none;
}

.transaction-details {
    min-width: 0;
}

.transaction-hash {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #6c757d;
    word-break: break-all;
}

.transaction-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.transaction-status.confirmed {
    background-color: #d4edda;
    color: #155724;
}

.transaction-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.transaction-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* Deposit section */
.deposit-info {
    text-align: center;
    margin-bottom: 32px;
}

.deposit-info > p {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 24px;
    text-align: center;
}

.deposit-address {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 24px;
    border-radius: 12px;
    margin: 24px 0;
    border: 2px solid #dee2e6;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.address-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.address-container label {
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
    margin-bottom: 8px;
}

.address-display-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
}

.deposit-address-text {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    word-break: break-all;
    border: 1px solid #ced4da;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    color: #495057;
}

.deposit-warnings {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-top: 24px;
}

.deposit-warnings h4 {
    color: #856404;
    margin-bottom: 12px;
    font-size: 1.1rem;
    font-weight: 600;
}

.deposit-warnings ul {
    margin: 0;
    padding-left: 20px;
    color: #856404;
}

.deposit-warnings li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.deposit-qr {
    margin-top: 24px;
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.manual-deposit {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 24px;
    margin-top: 32px;
    border: 1px solid #e9ecef;
}

.manual-deposit h4 {
    margin-bottom: 16px;
    color: #495057;
    font-size: 1.2rem;
    font-weight: 600;
}

.manual-deposit p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 16px;
}

.support-info {
    background: #e7f3ff;
    border: 1px solid #b8daff;
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

.support-info p {
    color: #004085;
    margin: 0;
    font-size: 0.9rem;
}

/* Copy button styling */
#copyDepositBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

#copyDepositBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Sticky Footer Menu */
.footer-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e9ecef;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: block !important; /* Always visible on all screen sizes */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform: translateY(0) !important; /* Always shown */
    transition: transform 0.3s ease;
}

.footer-menu.show {
    transform: translateY(0);
}

.footer-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 12px 8px 8px 8px;
    max-width: 100%;
    background: rgba(255, 255, 255, 0.95);
}

.footer-btn {
    background: none;
    border: none;
    padding: 8px 4px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
    border-radius: 8px;
    position: relative;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.footer-btn:hover,
.footer-btn:focus {
    color: #495057;
    background-color: rgba(102, 126, 234, 0.1);
    outline: none;
}

.footer-btn:active {
    transform: scale(0.95);
}

.footer-btn.active {
    color: #667eea;
}

.footer-btn.active::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background: #667eea;
    border-radius: 2px;
}

.footer-icon {
    width: 22px;
    height: 22px;
    transition: all 0.3s ease;
}

.footer-btn.active .footer-icon {
    transform: scale(1.1);
}

.footer-btn span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    margin-top: 2px;
}

/* Add bottom padding to main content when footer is visible */
.app-container.with-footer {
    padding-bottom: 70px;
}

/* Safe area insets for modern devices (iPhone X, etc.) */
@supports (padding: max(0px)) {
    .footer-menu {
        padding-bottom: max(8px, env(safe-area-inset-bottom));
    }
    
    .app-container {
        padding-bottom: calc(70px + max(0px, env(safe-area-inset-bottom)));
    }
}

/* Dark mode support for footer */
@media (prefers-color-scheme: dark) {
    .footer-menu {
        background: #1a1a1a;
        border-top-color: #333;
    }
    
    .footer-nav {
        background: rgba(26, 26, 26, 0.95);
    }
    
    .footer-btn {
        color: #a0a0a0;
    }
    
    .footer-btn:hover {
        color: #ffffff;
        background-color: rgba(102, 126, 234, 0.2);
    }
    
    .footer-btn.active {
        color: #667eea;
    }
}

/* Desktop and all screen sizes - always show footer menu */
@media (min-width: 769px) {
    .footer-menu {
        display: block !important;
        transform: translateY(0) !important;
    }
    
    /* Ensure navigation is visible on desktop */
    .app-nav {
        display: flex;
    }
    
    /* Maintain bottom padding for footer on desktop */
    .app-container {
        padding-bottom: 80px;
    }
    
    .app-main {
        padding-bottom: 80px;
    }
}

/* Desktop and all screen sizes - always show footer menu */
@media (min-width: 769px) {
    .footer-menu {
        display: block !important;
        transform: translateY(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Ensure navigation is visible on desktop */
    .app-nav {
        display: flex;
    }
    
    /* Maintain bottom padding for footer on desktop */
    .app-container {
        padding-bottom: 80px;
    }
    
    .app-main {
        padding-bottom: 80px;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    /* Hide regular navigation on mobile */
    .app-nav {
        display: none;
    }
    
    /* Show footer menu on mobile */
    .footer-menu {
        display: block !important;
        transform: translateY(0);
    }
    
    /* Add padding to prevent content from being hidden behind footer */
    .app-container {
        padding-bottom: 70px;
    }
    
    .header-content {
        padding: 0 16px;
    }
    
    .app-main {
        padding: 16px;
        padding-bottom: 80px; /* Extra padding for footer */
    }
    
    .balance-amount {
        font-size: 2rem;
    }
    
    .wallet-actions {
        flex-direction: column;
    }
    
    .wallet-actions .btn {
        width: 100%;
    }
    
    .transactions-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .transaction-filters {
        width: 100%;
    }
    
    .transaction-filters select {
        flex: 1;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 12px;
    }
    
    .app-main {
        padding: 12px;
    }
    
    .header-actions {
        gap: 6px;
    }
    
    .header-actions .btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .footer-btn {
        padding: 6px 2px;
        font-size: 9px;
        min-width: 50px;
    }
    
    .footer-icon {
        width: 18px;
        height: 18px;
    }
}

@media (min-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .balance-card {
        grid-column: 1 / -1;
    }
      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .wallet-section {
        grid-template-columns: 1fr 1fr;
    }
    
    .manual-deposit {
        grid-column: 1 / -1;
    }
    
    .transaction-row {
        grid-template-columns: auto 1fr auto auto;
    }
}

@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .balance-card {
        grid-column: 1 / -1;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
    
    .recent-transactions {
        grid-column: 1;
        grid-row: 2;
    }
    
    .stats-card {
        grid-column: 2;
        grid-row: 2;
    }
}

/* Profile Page Styles */
.profile-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.profile-card {
    margin-bottom: 24px;
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.profile-header h2 {
    margin: 0 0 4px 0;
    color: #212529;
    font-size: 1.75rem;
    font-weight: 600;
}

.profile-header p {
    margin: 0;
    color: #6c757d;
    font-size: 0.95rem;
}

.profile-section {
    margin-bottom: 32px;
}

.profile-section h3 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.profile-info {
    display: grid;
    gap: 16px;
}

.profile-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.profile-field:last-child {
    border-bottom: none;
}

.profile-field label {
    font-weight: 500;
    color: #495057;
    margin: 0;
}

.profile-field span {
    color: #212529;
    font-weight: 400;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.security-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.security-actions .btn {
    display: flex;
    align-items: center;
    gap: 8px;
}

.wallet-summary {
    display: grid;
    gap: 16px;
    margin-bottom: 20px;
}

.wallet-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.wallet-stat:last-child {
    border-bottom: none;
}

.wallet-stat label {
    font-weight: 500;
    color: #495057;
    margin: 0;
}

.wallet-stat span {
    color: #212529;
    font-weight: 400;
}

.wallet-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.change-password-card {
    border-left: 4px solid #007bff;
}

.change-password-card .profile-header {
    margin-bottom: 20px;
}

.change-password-card .profile-header h3 {
    margin: 0;
    color: #007bff;
}

.password-requirements {
    margin-top: 4px;
}

.password-requirements small {
    color: #6c757d;
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

/* Dashboard content layout */
.dashboard-content {
    padding: 24px 0;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 100px; /* Extra space for footer */
}

.page-header {
    margin-bottom: 32px;
    text-align: center;
}

.page-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
}

.page-header p {
    font-size: 1.1rem;
    color: #6c757d;
    margin: 0 0 16px 0;
}

.page-header .btn {
    margin-top: 8px;
}

/* Card styling */
.card {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    margin-bottom: 24px;
}

.card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 20px 0;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 12px;
}

.card-footer {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #f1f3f4;
    text-align: center;
}

/* Enhanced Deposit Form Styles */
.deposit-form {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    margin-bottom: 24px;
}

.deposit-form .form-group {
    margin-bottom: 20px;
    position: relative;
}

.deposit-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.deposit-form input[type="number"],
.deposit-form input[type="text"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fff;
    box-sizing: border-box;
}

.deposit-form input[type="number"]:focus,
.deposit-form input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.deposit-form input.error {
    border-color: #dc3545;
    background-color: #fff5f5;
    animation: shake 0.3s ease-in-out;
}

.deposit-form input.error:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.form-help {
    display: block;
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 6px;
    font-weight: 400;
}

.field-error {
    display: none;
    font-size: 0.85rem;
    margin-top: 6px;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;
}

.field-error.error {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

.field-error.success {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    flex-wrap: wrap;
}

.form-actions .btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.form-actions .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.form-actions .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.form-actions .btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.form-actions .btn-outline {
    background: transparent;
    border: 2px solid #e9ecef;
    color: #6c757d;
}

.form-actions .btn-outline:hover {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
}

/* Payment Details Section */
#paymentDetails {
    animation: slideIn 0.4s ease-out;
    transform-origin: top;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 1000px;
    }
}

.payment-info {
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.deposit-summary {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.deposit-summary h4 {
    color: #495057;
    margin-bottom: 16px;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid #f1f3f4;
    padding-bottom: 8px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item .label {
    font-weight: 500;
    color: #6c757d;
}

.summary-item .value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.summary-item .currency {
    color: #667eea;
    font-weight: 600;
    margin-left: 4px;
}

/* Payment Instructions */
.payment-instructions {
    margin-bottom: 24px;
}

.payment-instructions h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.payment-instructions h4::before {
    content: "📋";
    font-size: 1.1rem;
}

.instruction-step {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.instruction-step:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.step-number {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    transition: all 0.3s ease;
}

.step-content {
    flex: 1;
    min-width: 0;
}

.step-content h5 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.step-content p {
    margin: 0;
    color: #6c757d;
    line-height: 1.5;
}

/* Wallet Address Section */
.wallet-address-section {
    margin-top: 12px;
}

.wallet-address-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.address-display-container {
    display: flex;
    gap: 12px;
    align-items: stretch;
    flex-wrap: wrap;
}

#paymentAddress {
    flex: 1;
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #495057;
    border: 1px solid #e9ecef;
    word-break: break-all;
    min-width: 200px;
    transition: all 0.3s ease;
}

#paymentAddress:hover {
    background: #e9ecef;
}

#copyPaymentBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    white-space: nowrap;
}

#copyPaymentBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* QR Code Section */
.qr-code-section {
    text-align: center;
    margin-top: 12px;
}

.qr-code-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    display: inline-block;
    margin-bottom: 12px;
}

.qr-help {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
    font-style: italic;
}

/* Enhanced QR Code Styling */
.qr-code-display {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
}

.qr-image-container {
    position: relative;
    display: inline-block;
    margin-bottom: 16px;
}

.qr-code-image {
    border-radius: 8px;
    border: 2px solid #f8f9fa;
    transition: all 0.3s ease;
    max-width: 100%;
    height: auto;
}

.qr-code-image:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.qr-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    font-weight: 500;
    color: #6c757d;
}

.qr-info {
    margin-bottom: 16px;
}

.qr-amount {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 4px 0;
}

.qr-address-short {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

.qr-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 16px;
}

.qr-actions .btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    font-size: 0.9rem;
}

.qr-actions .btn-icon {
    font-size: 1rem;
}

.qr-error {
    background: #f8d7da;
    color: #721c24;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #f5c6cb;
}

.qr-error p {
    margin: 0 0 12px 0;
    font-weight: 500;
}

/* Enhanced TRON QR Code Styling */
.tron-qr-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    border: 2px solid #e9ecef;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.tron-qr-container::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.qr-header {
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.qr-header h5 {
    color: #2c3e50;
    margin: 0 0 4px 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.qr-header p {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

.qr-image-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.tron-qr-image {
    border-radius: 12px;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    background: white;
}

.qr-overlay {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.tron-logo {
    font-family: monospace;
}

.qr-details {
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.qr-amount-large {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.qr-network {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.qr-instructions {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    padding: 16px;
    position: relative;
    z-index: 1;
}

.qr-instructions p {
    margin: 0 0 6px 0;
    font-size: 0.85rem;
    color: #495057;
    font-weight: 500;
    text-align: left;
}

.qr-instructions p:last-child {
    margin-bottom: 0;
}

/* Mobile responsive QR code */
@media (max-width: 768px) {
    .qr-code-display {
        padding: 16px;
    }
    
    .tron-qr-container {
        padding: 20px;
        margin: 0 -4px;
    }
    
    .qr-actions {
        flex-direction: column;
    }
    
    .qr-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .qr-amount-large {
        font-size: 1.3rem;
    }
    
    .qr-header h5 {
        font-size: 1.1rem;
    }
}

/* QR Code Animation */
@keyframes qrFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8) rotate(5deg);
    }
    to {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

.qr-code-image,
.tron-qr-image {
    animation: qrFadeIn 0.6s ease-out;
}

/* QR Code Success State */
.qr-success-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(40, 167, 69, 0.9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-size: 2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.qr-success-overlay.show {
    opacity: 1;
}

/* Balance Monitoring Status Styles */
.balance-monitoring-status {
    margin-top: 1rem;
    padding: 0;
}

.monitoring-indicator {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    gap: 1rem;
    animation: slideInUp 0.4s ease-out;
}

.monitoring-indicator.active {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 2px solid #4CAF50;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.1);
}

.monitoring-indicator.stopped {
    background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
    border: 2px solid #ddd;
}

.monitoring-pulse {
    width: 20px;
    height: 20px;
    background: #4CAF50;
    border-radius: 50%;
    position: relative;
    flex-shrink: 0;
}

.monitoring-pulse::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.monitoring-text {
    flex: 1;
}

.monitoring-text strong {
    display: block;
    color: #2c5530;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.monitoring-text p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Deposit Success Modal Styles */
.deposit-success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
}

.deposit-success-modal {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    max-width: 450px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: scaleIn 0.4s ease-out;
}

.success-header {
    margin-bottom: 1.5rem;
}

.success-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.success-header h3 {
    color: #2c5530;
    margin: 0;
    font-size: 1.5rem;
}

.success-content {
    margin-bottom: 2rem;
    text-align: left;
}

.success-content p {
    margin: 0.75rem 0;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}

.success-content strong {
    color: #2c5530;
}

.success-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.success-actions .btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scaleIn {
    from { 
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    to { 
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile responsiveness for monitoring status */
@media (max-width: 768px) {
    .monitoring-indicator {
        padding: 0.875rem;
        gap: 0.75rem;
    }
    
    .monitoring-pulse {
        width: 16px;
        height: 16px;
    }
    
    .monitoring-text strong {
        font-size: 0.9rem;
    }
    
    .monitoring-text p {
        font-size: 0.8rem;
    }
    
    .deposit-success-modal {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .success-actions {
        flex-direction: column;
    }
    
    .success-actions .btn {
        flex: none;
        width: 100%;
    }
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    border-left: 4px solid #007bff;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

.notification.info {
    border-left-color: #17a2b8;
}

.notification-content {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.notification-message {
    font-size: 14px;
    color: #333;
    line-height: 1.4;
}

.btn-spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading states */
.loading {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.no-data {
    text-align: center;
    padding: 20px;
    color: #999;
}

.qr-fallback, .qr-error {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 14px;
}

/* ========================================
   ENHANCED ACTIVE INVESTMENTS STYLING
   ======================================== */

/* Investment Summary Cards */
.investment-summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    border-radius: 16px;
    margin-bottom: 20px;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.investment-summary-card h4 {
    color: rgba(255, 255, 255, 0.95);
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 16px;
}

.summary-stat {
    text-align: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.summary-stat:hover {
    transform: translateY(-2px);
}

.summary-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 4px;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.summary-stat-label {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Investment Grid */
.investment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Investment Item Cards */
.investment-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 20px;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    overflow: hidden;
    margin-bottom: 16px;
}

.investment-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 16px 16px 0 0;
}

.investment-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
    border-color: #667eea;
}

.investment-item.pulse {
    animation: investmentPulse 2s infinite;
}

.investment-item.new-investment {
    animation: investmentEntrance 0.6s ease-out;
}

/* Investment Status Badge */
.investment-status {
    position: absolute;
    top: 16px;
    right: 16px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.investment-status.active {
    background: rgba(40, 167, 69, 0.15);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.investment-status.completed {
    background: rgba(23, 162, 184, 0.15);
    color: #17a2b8;
    border: 1px solid rgba(23, 162, 184, 0.3);
}

/* Investment Info Layout */
.investment-info {
    margin-top: 12px;
}

.investment-details {
    margin-bottom: 16px;
}

.investment-plan {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.investment-amount {
    font-size: 1.1rem;
    font-weight: 600;
    color: #28a745;
    margin-bottom: 8px;
}

.performance-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.performance-indicator.positive {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.performance-indicator.neutral {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

/* Investment Earnings */
.investment-earnings {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-top: 1px solid #f1f3f4;
    margin: 16px 0;
}

.daily-return {
    font-size: 0.95rem;
    font-weight: 600;
    color: #28a745;
}

.total-earned {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Enhanced Progress Bars */
.investment-progress {
    margin-top: 16px;
}

.progress-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 12px;
    transition: width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #495057;
    font-weight: 500;
}

.progress-text::after {
    content: attr(data-percentage);
    font-weight: 600;
    color: #28a745;
}

/* Additional Modal Enhancements */
.modal.show {
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(4px);
    }
}

/* Additional Button Styles */
.btn.btn-outline {
    background: transparent;
    border: 2px solid #e9ecef;
    color: #6c757d;
    font-weight: 600;
}

.btn.btn-outline:hover {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-1px);
}

.btn.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(220, 53, 69, 0.3);
}

/* ========================================
   INVEST PAGE BALANCE-BASED CONTENT
   ======================================== */

/* Balance-based container styles */
.insufficient-balance-container,
.investment-available-container,
.error-container {
    padding: 20px 0;
}

/* Warning card for insufficient balance */
.warning-card,
.error-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffeaa7;
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.1);
}

.error-card {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #f5c6cb;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.1);
}

.warning-icon,
.error-icon,
.status-icon {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.warning-card h4,
.error-card h4 {
    color: #856404;
    margin: 0 0 16px 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.error-card h4 {
    color: #721c24;
}

.warning-card p,
.error-card p {
    color: #856404;
    margin: 0 0 12px 0;
    font-size: 1rem;
    line-height: 1.5;
}

.error-card p {
    color: #721c24;
}

.balance-needed {
    background: rgba(255, 193, 7, 0.2);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    padding: 12px 16px;
    margin: 16px 0;
    font-size: 0.95rem;
}

.needed-amount {
    color: #856404;
    font-weight: 600;
}

.warning-actions,
.error-actions,
.investment-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 24px;
    flex-wrap: wrap;
}

/* Balance status card for sufficient balance */
.balance-status-card {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 2px solid #c3e6cb;
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.1);
}

.balance-status-card h4 {
    color: #155724;
    margin: 0 0 12px 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.balance-status-card p {
    color: #155724;
    margin: 0 0 8px 0;
    font-size: 1rem;
    line-height: 1.5;
}

.status-content {
    margin-bottom: 20px;
}

/* Button styling for invest page */
.investment-actions .btn,
.warning-actions .btn,
.error-actions .btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.investment-actions .btn-primary,
.warning-actions .btn-primary,
.error-actions .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.investment-actions .btn-primary:hover,
.warning-actions .btn-primary:hover,
.error-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.investment-actions .btn-secondary,
.warning-actions .btn-secondary,
.error-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.investment-actions .btn-secondary:hover,
.warning-actions .btn-secondary:hover,
.error-actions .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.investment-actions .btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    box-shadow: none;
}

.investment-actions .btn-outline:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

/* ========================================
   INVEST PAGE LAYOUT STYLES
   ======================================== */

/* Invest Page Layout */
.invest-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
}

/* Page Header */
.invest-page .page-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.invest-page .page-header h2 {
    color: #2c3e50;
    font-size: 2rem;
    margin: 0 0 10px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.invest-page .page-header p {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

/* Balance Overview */
.invest-page .balance-overview {
    margin-bottom: 30px;
}

.invest-page .balance-overview .balance-card {
    max-width: 400px;
    margin: 0 auto;
}

/* Investment Cards and Layout */
.invest-page .card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    width: 100%;
    box-sizing: border-box;
}

.invest-page .card h3 {
    color: #2c3e50;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 20px 0;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 12px;
}

/* Balance-based Container */
.invest-page #balanceBasedContainer {
    min-height: 150px;
    width: 100%;
}

/* Investment Form Container */
.invest-page .investment-form-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

/* Insufficient Balance Message */
.invest-page .insufficient-balance {
    text-align: center;
    padding: 40px 20px;
    width: 100%;
}

.invest-page .warning-card {
    max-width: 500px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}

/* Investment Form */
.invest-page .investment-form {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.invest-page .form-group {
    margin-bottom: 20px;
    width: 100%;
}

.invest-page .form-group input {
    width: 100%;
    box-sizing: border-box;
}

/* Investment Summary */
.invest-page .investment-summary {
    width: 100%;
    max-width: 500px;
    margin: 20px auto;
}

/* Form Actions */
.invest-page .form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    width: 100%;
    justify-content: center;
}

.invest-page .form-actions .btn {
    flex: 1;
    max-width: 200px;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
}

/* Active Investments and History */
.invest-page #activeInvestmentsList,
.invest-page #investmentHistoryList {
    width: 100%;
    min-height: 100px;
}

/* Responsive Design for Invest Page */
@media (max-width: 768px) {
    .invest-page {
        padding: 16px;
    }
    
    .invest-page .card {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .invest-page .page-header h2 {
        font-size: 1.75rem;
    }
    
    .invest-page .balance-overview .balance-card {
        max-width: 100%;
    }
    
    .invest-page .form-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .invest-page .form-actions .btn {
        width: 100%;
        max-width: 250px;
    }
    
    .invest-page .investment-form-container,
    .invest-page .investment-form,
    .invest-page .investment-summary {
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .invest-page {
        padding: 12px;
    }
    
    .invest-page .card {
        padding: 16px;
    }
    
    .invest-page .page-header h2 {
        font-size: 1.5rem;
    }
    
    .invest-page .warning-card {
        padding: 20px;
    }
}

/* ========================================
   MAKE INVESTMENT PAGE WIDTH FIXES
   ======================================== */

/* Ensure make investment page containers have proper width constraints */
.make-investment-page .card {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.make-investment-page .warning-card,
.make-investment-page .error-card,
.make-investment-page .balance-status-card {
    width: 100%;
    max-width: 600px;
    margin: 20px auto;
    box-sizing: border-box;
}

.make-investment-page .investment-form-container {
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
}

.make-investment-page .investment-form {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.make-investment-page .selected-plan-summary {
    width: 100%;
    max-width: 600px;
    margin: 0 auto 24px auto;
}

.make-investment-page .balance-info {
    width: 100%;
    max-width: 500px;
    margin: 0 auto 24px auto;
}

.make-investment-page .modal-content {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

/* Low Funds Modal Width Fix */
.make-investment-page .low-funds-content {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.make-investment-page .balance-comparison {
    width: 100%;
    max-width: 400px;
    margin: 16px auto;
}

/* Investment Plans Grid Width Fix */
.make-investment-page .investment-plans {
    width: 100%;
    max-width: 1000px;
    margin: 20px auto;
}

.make-investment-page .plan-card {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* Active Investments Width Fix */
.make-investment-page #activeInvestmentsList,
.make-investment-page #investmentHistoryList {
    width: 100%;
    max-width: 100%;
}

.make-investment-page .investment-grid {
    width: 100%;
    max-width: 100%;
}

.make-investment-page .investment-item {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* Mobile Width Fixes */
@media (max-width: 768px) {
    .make-investment-page .warning-card,
    .make-investment-page .error-card,
    .make-investment-page .balance-status-card {
              
                            
               max-width: 100%;
        margin: 16px 0;
    }
    
    .make-investment-page .investment-form-container,
    .make-investment-page .investment-form,
    .make-investment-page .selected-plan-summary,
    .make-investment-page .balance-info {
        max-width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
    
    .make-investment-page .investment-plans {
        grid-template-columns: 1fr;
        max-width: 100%;
    }
    
    .make-investment-page .modal-content {
        max-width: 95%;
        margin: 0 auto;
    }
}

/* ========================================
   GLOBAL WIDTH AND CONTAINER FIXES
   ======================================== */

/* Ensure all containers use border-box sizing */
.invest-page *,
.make-investment-page * {
    box-sizing: border-box;
}

/* Prevent horizontal overflow */
.invest-page,
.make-investment-page {
    overflow-x: hidden;
    width: 100%;
    max-width: 1200px;
}

/* Fix card containers to prevent width issues */
.invest-page .card,
.make-investment-page .card {
    overflow: hidden;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Fix button containers to prevent overflow */
.warning-actions,
.error-actions,
.investment-actions {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    flex-wrap: wrap;
}

.warning-actions .btn,
.error-actions .btn,
.investment-actions .btn {
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Fix input and form element widths */
.invest-page input,
.make-investment-page input,
.invest-page select,
.make-investment-page select,
.invest-page textarea,
.make-investment-page textarea {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* Fix table and grid layouts */
.invest-page .summary-details,
.make-investment-page .summary-details {
    width: 100%;
    overflow-x: auto;
}

.invest-page .summary-row,
.make-investment-page .summary-row {
    width: 100%;
    flex-wrap: wrap;
}

/* Fix image and media content */
.invest-page img,
.make-investment-page img {
    max-width: 100%;
    height: auto;
}

/* Responsive container padding */
@media (max-width: 1200px) {
    .invest-page,
    .make-investment-page {
        padding-left: 16px;
        padding-right: 16px;
    }
}

@media (max-width: 768px) {
    .invest-page,
    .make-investment-page {
        padding-left: 12px;
        padding-right: 12px;
    }
}

@media (max-width: 480px) {
    .invest-page,
    .make-investment-page {
        padding-left: 8px;
        padding-right: 8px;
    }
}

/* Page Leave Warning Styles */
#paymentStatusIndicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #dc3545;
    color: white;
    text-align: center;
    padding: 12px;
    font-weight: bold;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    animation: slideInDown 0.5s ease-out;
    border-bottom: 3px solid #b02a37;
}

#paymentStatusIndicator:before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    height: 3px;
    width: 100%;
    background: linear-gradient(90deg, #ff6b6b, #feca57, #48cae4, #ff6b6b);
    background-size: 200% 100%;
    animation: gradientFlow 2s linear infinite;
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes gradientFlow {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Notification styles for page leave warnings */
.notification.warning {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    border-left: 4px solid #ff4757;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Balance monitoring status indicator */
.balance-monitoring-status {
    margin-top: 15px;
    padding: 15px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.monitoring-indicator {
    display: flex;
    align-items: center;
    gap: 15px;
}

.monitoring-pulse {
    width: 20px;
    height: 20px;
    background: #4CAF50;
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
    flex-shrink: 0;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.monitoring-text {
    flex: 1;
}

.monitoring-text strong {
    display: block;
    font-size: 16px;
    margin-bottom: 5px;
}

.monitoring-text p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

.monitoring-indicator.stopped {
    background: linear-gradient(135deg, #ff6b6b, #ff9ff3);
}

.monitoring-indicator.stopped .monitoring-pulse {
    background: #ff4757;
    animation: none;
}

/* Payment details warning section enhancement */
.payment-warning {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    border: 2px solid #ff4757;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    color: #2d3436;
    font-weight: 600;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
    animation: warningPulse 2s ease-in-out infinite;
}

@keyframes warningPulse {
    0%, 100% {
        box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
        border-color: #ff4757;
    }
    50% {
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.5);
        border-color: #ff3742;
    }
}

.payment-warning h5 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 18px;
}

.payment-warning ul {
    margin: 15px 0 10px 20px;
    padding: 0;
}

.payment-warning li {
    margin-bottom: 8px;
    line-height: 1.5;
    font-size: 15px;
}

/* Responsive adjustments for page leave warning */
@media (max-width: 768px) {
    #paymentStatusIndicator {
        padding: 10px 15px;
        font-size: 13px;
    }
    
    .balance-monitoring-status {
        margin-top: 10px;
        padding: 12px;
    }
    
    .monitoring-indicator {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .payment-warning {
        padding: 15px;
        margin: 15px 0;
    }
    
    .payment-warning h5 {
        font-size: 16px;
    }
}
