<?php
require_once 'vendor/autoload.php';

use Simbi\Tls\Config\Database;

try {
    $pdo = Database::getConnection();
    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }
    
    echo "=== Investment Plans Simple Interest Update ===\n";
    echo "Updating all plans to 1.67% daily simple interest\n\n";
    
    // Check current plan rates
    echo "Current Plan Rates:\n";
    echo "==================\n";
    $stmt = $pdo->query("
        SELECT plan_code, plan_name, daily_rate, (daily_rate * 100) as percentage_rate, is_active 
        FROM investment_plans 
        ORDER BY display_order
    ");
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $status = $row['is_active'] ? 'ACTIVE' : 'INACTIVE';
        echo "• {$row['plan_name']} ({$row['plan_code']}): {$row['percentage_rate']}% daily - {$status}\n";
    }
    
    echo "\nUpdating to 1.67% daily simple interest...\n";
    
    // Update all plans to 1.67% daily rate
    $updateStmt = $pdo->prepare("
        UPDATE investment_plans 
        SET 
            daily_rate = 0.0167,
            description = CASE 
                WHEN plan_code = 'basic' THEN 'Perfect for beginners with steady 1.67% daily simple interest returns'
                WHEN plan_code = 'premium' THEN 'Enhanced returns with 1.67% daily simple interest for experienced investors'
                WHEN plan_code = 'vip' THEN 'Maximum returns with 1.67% daily simple interest for high-value investors'
                ELSE description
            END,
            features = CASE 
                WHEN plan_code = 'basic' THEN JSON_ARRAY('1.67% daily simple interest', '30-day duration', 'Non-compounding', 'Low risk', 'Beginner friendly')
                WHEN plan_code = 'premium' THEN JSON_ARRAY('1.67% daily simple interest', '30-day duration', 'Non-compounding', 'Medium risk', 'Advanced features')
                WHEN plan_code = 'vip' THEN JSON_ARRAY('1.67% daily simple interest', '30-day duration', 'Non-compounding', 'Premium service', 'Maximum profits')
                ELSE features
            END,
            updated_at = CURRENT_TIMESTAMP
        WHERE plan_code IN ('basic', 'premium', 'vip')
    ");
    
    $result = $updateStmt->execute();
    $affectedRows = $updateStmt->rowCount();
    
    if ($result) {
        echo "✅ Successfully updated {$affectedRows} investment plans\n\n";
    } else {
        throw new Exception("Failed to update investment plans");
    }
    
    // Verify the updates
    echo "Updated Plan Rates:\n";
    echo "==================\n";
    $stmt = $pdo->query("
        SELECT 
            plan_code, 
            plan_name, 
            daily_rate, 
            (daily_rate * 100) as percentage_rate,
            (daily_rate * duration * 100) as total_return_percentage,
            is_active,
            features
        FROM investment_plans 
        ORDER BY display_order
    ");
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $status = $row['is_active'] ? 'ACTIVE' : 'INACTIVE';
        $features = json_decode($row['features'], true);
        echo "• {$row['plan_name']} ({$row['plan_code']}): {$row['percentage_rate']}% daily - {$status}\n";
        echo "  Total return over 30 days: {$row['total_return_percentage']}%\n";
        echo "  Features: " . implode(', ', $features) . "\n\n";
    }
    
    // Show calculation examples
    echo "Example Investment Calculations (Simple Interest):\n";
    echo "================================================\n";
    
    $examples = [
        ['amount' => 600, 'plan' => 'Basic (minimum)'],
        ['amount' => 1000, 'plan' => 'Basic'],
        ['amount' => 2000, 'plan' => 'Premium (minimum)'],
        ['amount' => 5000, 'plan' => 'VIP (minimum)'],
        ['amount' => 10000, 'plan' => 'VIP']
    ];
    
    foreach ($examples as $example) {
        $principal = $example['amount'];
        $dailyReturn = $principal * 0.0167;
        $totalInterest = $dailyReturn * 30;
        $finalAmount = $principal + $totalInterest;
        $totalReturnPercentage = ($totalInterest / $principal) * 100;
        
        echo "Investment: {$principal} USDT ({$example['plan']})\n";
        echo "  Daily return: " . number_format($dailyReturn, 2) . " USDT (1.67%)\n";
        echo "  Total interest (30 days): " . number_format($totalInterest, 2) . " USDT\n";
        echo "  Final amount: " . number_format($finalAmount, 2) . " USDT\n";
        echo "  Total return: " . number_format($totalReturnPercentage, 2) . "%\n\n";
    }
    
    echo "✅ Investment plans successfully updated to 1.67% daily simple interest!\n";
    echo "\nKey Features:\n";
    echo "• Non-compounding simple interest\n";
    echo "• 1.67% daily rate for all plans\n";
    echo "• 30-day duration\n";
    echo "• Total return: 50.1% over 30 days\n";
    echo "• Predictable daily returns\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
