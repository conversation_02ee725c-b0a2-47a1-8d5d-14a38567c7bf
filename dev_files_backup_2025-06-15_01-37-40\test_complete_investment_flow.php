<?php
// Test investment creation with simulated user session
session_start();

// Set up a test session (you would normally get this from login)
$_SESSION['user_id'] = 1;
$_SESSION['email'] = '<EMAIL>';
$_SESSION['token'] = 'mock_token_for_testing';

require_once 'frontend/config.php';
require_once 'frontend/api.php';

echo "Testing Investment Creation with Mock Session\n";
echo "============================================\n\n";

echo "Session Data:\n";
echo "- User ID: " . ($_SESSION['user_id'] ?? 'not set') . "\n";
echo "- Email: " . ($_SESSION['email'] ?? 'not set') . "\n";
echo "- Token: " . ($_SESSION['token'] ?? 'not set') . "\n\n";

// Test the API wrapper directly
echo "Testing API Wrapper...\n";
$api = new APIWrapper();
$api->setToken($_SESSION['token']);

try {
    $result = $api->createInvestment(700, 'basic');
    echo "API Wrapper Result:\n";
    print_r($result);
    
    if (isset($result['error'])) {
        if (strpos($result['error'], 'Invalid or expired token') !== false) {
            echo "\n✅ Success! API is returning JSON error instead of HTML.\n";
            echo "The authentication error is expected with mock token.\n";
        } else {
            echo "\n⚠️  Different error returned: " . $result['error'] . "\n";
        }
    } else {
        echo "\n✅ Investment creation successful!\n";
    }
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Testing AJAX Endpoint...\n";

// Test the AJAX endpoint
$input = json_encode([
    'action' => 'create_investment',
    'amount' => 700,
    'plan' => 'basic'
]);

// Capture the output of the AJAX script
ob_start();

// Simulate the AJAX request
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST = [];
file_put_contents('php://input', $input);

// Include the AJAX handler
try {
    include 'frontend/ajax.php';
} catch (Exception $e) {
    echo "Error including ajax.php: " . $e->getMessage() . "\n";
}

$ajaxOutput = ob_get_clean();

echo "AJAX Output:\n";
echo $ajaxOutput . "\n";

// Try to parse as JSON
$ajaxResult = json_decode($ajaxOutput, true);
if ($ajaxResult) {
    echo "\n✅ AJAX returned valid JSON!\n";
    if (isset($ajaxResult['error'])) {
        echo "Error message: " . $ajaxResult['error'] . "\n";
    }
} else {
    echo "\n❌ AJAX did not return valid JSON\n";
    echo "Raw output: " . substr($ajaxOutput, 0, 200) . "...\n";
}

echo "\nTest complete!\n";
?>
