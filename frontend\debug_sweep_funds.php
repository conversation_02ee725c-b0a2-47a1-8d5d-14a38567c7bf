<?php
/**
 * Debug Sweep Funds Functionality
 * This script helps diagnose the "Invalid JSON response" error
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start session
session_start();

// Include the API wrapper
require_once 'api_psr4.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Sweep Funds Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
        .test-btn { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>";

echo "<h1>🔍 Sweep Funds Debug Tool</h1>";

// Test 1: Check Session and Authentication
echo "<div class='section info'>
<h2>1. Session & Authentication Check</h2>";

if (isset($_SESSION['token']) && isset($_SESSION['user_id'])) {
    echo "✅ Session active<br>";
    echo "User ID: " . $_SESSION['user_id'] . "<br>";
    echo "Token exists: " . (strlen($_SESSION['token']) > 10 ? 'Yes' : 'No') . "<br>";
} else {
    echo "❌ No active session found<br>";
    echo "<strong>Please login first!</strong><br>";
}
echo "</div>";

// Test 2: Check Backend Configuration
echo "<div class='section info'>
<h2>2. Backend Configuration Check</h2>";

try {
    // Include backend autoloader
    require_once __DIR__ . '/../backend/vendor/autoload.php';
    
    use Simbi\Tls\Config\Config;
    
    Config::load();
    
    $masterAddress = Config::get('MASTER_ADDRESS', '');
    $masterPrivateKey = Config::get('MASTER_PRIVATE_KEY', '');
    $usdtContract = Config::get('USDT_CONTRACT', '');
    $tronNetwork = Config::get('TRON_NETWORK', 'nile');
    
    echo "TRON Network: " . $tronNetwork . "<br>";
    echo "Master Address: " . ($masterAddress ? '✅ Configured' : '❌ Missing') . "<br>";
    echo "Master Private Key: " . ($masterPrivateKey ? '✅ Configured' : '❌ Missing') . "<br>";
    echo "USDT Contract: " . ($usdtContract ? '✅ Configured (' . $usdtContract . ')' : '❌ Missing') . "<br>";
    
    if (!$masterAddress) {
        echo "<div class='error'><strong>⚠️ MASTER_ADDRESS not configured in .env file!</strong></div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Configuration Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 3: Direct API Test
echo "<div class='section info'>
<h2>3. Direct API Test</h2>";

if (isset($_SESSION['token'])) {
    try {
        $api = new APIWrapper();
        $api->setToken($_SESSION['token']);
        
        echo "<button class='test-btn' onclick='testSweepFunds()'>🧪 Test Sweep Funds</button><br><br>";
        echo "<div id='sweep-result'></div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ API Wrapper Error: " . $e->getMessage() . "</div>";
    }
} else {
    echo "❌ Cannot test - no authentication token";
}

echo "</div>";

// Test 4: Raw Backend Test
echo "<div class='section info'>
<h2>4. Raw Backend API Test</h2>";

if (isset($_SESSION['token'])) {
    echo "<button class='test-btn' onclick='testRawBackend()'>🔧 Test Raw Backend</button><br><br>";
    echo "<div id='backend-result'></div>";
} else {
    echo "❌ Cannot test - no authentication token";
}

echo "</div>";

// JavaScript for testing
echo "<script>
async function testSweepFunds() {
    const resultDiv = document.getElementById('sweep-result');
    resultDiv.innerHTML = '⏳ Testing sweep funds...';
    
    try {
        const response = await fetch('ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'sweep_funds'
            })
        });
        
        const responseText = await response.text();
        console.log('Raw response:', responseText);
        
        try {
            const jsonData = JSON.parse(responseText);
            resultDiv.innerHTML = '<div class=\"success\">✅ Valid JSON Response:</div><pre>' + JSON.stringify(jsonData, null, 2) + '</pre>';
        } catch (jsonError) {
            resultDiv.innerHTML = '<div class=\"error\">❌ Invalid JSON Response:</div><pre>' + responseText + '</pre><br><strong>JSON Parse Error:</strong> ' + jsonError.message;
        }
        
    } catch (error) {
        resultDiv.innerHTML = '<div class=\"error\">❌ Network Error: ' + error.message + '</div>';
    }
}

async function testRawBackend() {
    const resultDiv = document.getElementById('backend-result');
    resultDiv.innerHTML = '⏳ Testing raw backend...';
    
    try {
        const response = await fetch('../backend/src/index.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer " . ($_SESSION['token'] ?? '') . "'
            },
            body: JSON.stringify({})
        });
        
        const responseText = await response.text();
        console.log('Raw backend response:', responseText);
        
        try {
            const jsonData = JSON.parse(responseText);
            resultDiv.innerHTML = '<div class=\"success\">✅ Valid JSON Response:</div><pre>' + JSON.stringify(jsonData, null, 2) + '</pre>';
        } catch (jsonError) {
            resultDiv.innerHTML = '<div class=\"error\">❌ Invalid JSON Response:</div><pre>' + responseText + '</pre><br><strong>JSON Parse Error:</strong> ' + jsonError.message;
        }
        
    } catch (error) {
        resultDiv.innerHTML = '<div class=\"error\">❌ Network Error: ' + error.message + '</div>';
    }
}
</script>";

echo "</body></html>";
?>
