// Dashboard JavaScript - Simplified for Dashboard Only
document.addEventListener('DOMContentLoaded', function() {
    initDashboard();
});

function initDashboard() {
    loadDashboardData();
    initFooterMenu();
}

async function loadDashboardData() {
    try {
        // Load balance for dashboard
        const balanceResponse = await apiCall('get_balance');
        if (balanceResponse.success) {
            document.getElementById('totalBalance').textContent = balanceResponse.balance_formatted || '0.000000';
        } else {
            document.getElementById('totalBalance').textContent = '0.000000';
        }
        
        // Load transaction statistics
        const statsResponse = await apiCall('get_transaction_statistics');
        if (statsResponse.success) {
            const stats = statsResponse.statistics;
            document.getElementById('totalTransactions').textContent = stats.total_transactions || '0';
            document.getElementById('totalDeposits').textContent = stats.total_deposits || '0';
            document.getElementById('totalVolume').textContent = (stats.total_volume || '0') + ' TRX';
        }
        
        // Load recent transactions for dashboard
        const transactionsResponse = await apiCall('get_transactions', { limit: 5, page: 0 });
        if (transactionsResponse.success) {
            displayRecentTransactions(transactionsResponse.transactions);
        }
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showMessage('Error loading dashboard data', 'error');
    }
}

function displayRecentTransactions(transactions) {
    const container = document.getElementById('recentTransactionsList');
    
    if (!transactions || transactions.length === 0) {
        container.innerHTML = '<div class="no-data">No recent transactions</div>';
        return;
    }
    
    const transactionsHtml = transactions.map(tx => `
        <div class="transaction-item">
            <div class="transaction-info">
                <div class="transaction-type">${formatTransactionType(tx.type)}</div>
                <div class="transaction-date">${formatDate(tx.created_at)}</div>
            </div>
            <div class="transaction-amount ${tx.type === 'deposit' ? 'positive' : 'negative'}">
                ${tx.type === 'deposit' ? '+' : '-'}${tx.amount_formatted || tx.amount} TRX
            </div>
        </div>
    `).join('');
    
    container.innerHTML = transactionsHtml;
}

function formatTransactionType(type) {
    const types = {
        'deposit': 'Deposit',
        'withdrawal': 'Withdrawal',
        'sweep': 'Sweep'
    };
    return types[type] || type;
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// Shared utility functions
async function apiCall(endpoint, data = null) {
    const url = `ajax.php`;
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: endpoint,
            ...data
        })
    };
    
    const response = await fetch(url, options);
    return await response.json();
}

function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';
    
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 3000);
}

function initFooterMenu() {
    // Add smooth entrance animation for footer menu
    const footerMenu = document.querySelector('.footer-menu');
    if (footerMenu) {
        // Always show footer menu
        footerMenu.classList.add('show');
        
        // Optional: Handle window resize for future mobile-specific behavior
        window.addEventListener('resize', function() {
            // Footer is always visible now, but this can be customized later
            footerMenu.classList.add('show');
        });
    }
}
