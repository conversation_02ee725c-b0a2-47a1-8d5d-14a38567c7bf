<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

// Check if admin - note: regular users can access dashboard
$isAdmin = SessionService::isAdmin();
$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'TRON Wallet - Dashboard';
$currentPage = 'dashboard';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>

            <!-- Dashboard Content -->
            <div class="dashboard-content">                <div class="page-header">
                    <h2>Dashboard</h2>
                    <p>Welcome back, <?php echo htmlspecialchars($user['email']); ?>!</p>
                    <?php if ($isAdmin): ?>
                        <a href="../admin.php" class="btn btn-secondary">Admin Panel</a>
                    <?php endif; ?>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 18v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2" fill="none"/>
                                <path d="M16 8h4a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-4" stroke="currentColor" stroke-width="2" fill="none"/>
                                <circle cx="16" cy="12" r="1" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalBalance">Loading...</h3>
                            <p>Total Balance</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="2" fill="none"/>
                                <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="2" fill="none"/>
                            </svg>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalTransactions">Loading...</h3>
                            <p>Total Transactions</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2v20" stroke="currentColor" stroke-width="2"/>
                                <path d="M17 7l-5-5-5 5" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalDeposits">Loading...</h3>
                            <p>Total Deposits</p>
                        </div>
                    </div>                    <div class="stat-card">
                        <div class="stat-icon">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 1v22" stroke="currentColor" stroke-width="2"/>
                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalInvested">Loading...</h3>
                            <p>Total Invested</p>
                        </div>
                    </div>
                </div>

                <!-- Active Investments -->
                <div class="card">
                    <h3>Active Investments</h3>
                    <div id="activeInvestmentsList">Loading...</div>
                    <div class="card-footer">
                        <a href="make_investment.php" class="btn btn-primary">Make Investment</a>
                        <a href="invest.php" class="btn btn-outline">View All Investments</a>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card">
                    <h3>Recent Transactions</h3>
                    <div id="recentTransactionsList">Loading...</div>
                    <div class="card-footer">
                        <a href="transactions.php" class="btn btn-outline">View All Transactions</a>
                    </div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>

<script src="js/qr-generator.js"></script>
<script src="js/dashboard-simple.js"></script>
