<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Investment Creation Fix Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .btn { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 14px;
            background: #007bff;
            color: white;
        }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .result {
            background: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .console {
            background: #343a40;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Investment Creation Fix Test</h1>
        <p>This page tests the investment creation API after fixing the JSON parsing error.</p>

        <div class="test-section">
            <h3>Test 1: Backend API Endpoint</h3>
            <p>Test the backend investment creation endpoint directly</p>
            <button class="btn" onclick="testBackendAPI()">Test Backend API</button>
            <div id="backendResult"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Frontend AJAX Handler</h3>
            <p>Test the frontend AJAX handler that processes investment requests</p>
            <button class="btn" onclick="testAjaxHandler()">Test AJAX Handler</button>
            <div id="ajaxResult"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Complete Investment Flow</h3>
            <p>Test the complete investment creation flow with authentication</p>
            <div>
                <label>Amount: <input type="number" id="testAmount" value="700" min="600"></label>
                <label>Plan: 
                    <select id="testPlan">
                        <option value="basic">Basic Plan</option>
                        <option value="premium">Premium Plan</option>
                        <option value="vip">VIP Plan</option>
                    </select>
                </label>
                <button class="btn" onclick="testCompleteFlow()">Test Complete Flow</button>
            </div>
            <div id="completeFlowResult"></div>
        </div>

        <div class="test-section">
            <h3>Console Output</h3>
            <div id="console" class="console"></div>
        </div>
    </div>

    <script>
        // Console logging
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleDiv.textContent += new Date().toLocaleTimeString() + ' [LOG]: ' + args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            consoleDiv.textContent += new Date().toLocaleTimeString() + ' [ERROR]: ' + args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };

        function showResult(elementId, message, type = 'result') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // Test 1: Backend API
        async function testBackendAPI() {
            console.log('Testing backend API endpoint...');
            
            const testData = {
                amount: 700,
                plan: 'basic'
            };
            
            try {
                const response = await fetch('http://localhost:8000/api/invest', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test_token'
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                console.log('Backend API response:', result);
                
                if (response.status === 401 && result.error === 'Invalid or expired token') {
                    showResult('backendResult', '✅ Backend API is working correctly!\nReturned JSON response instead of HTML.\nAuthentication error is expected with test token.', 'success');
                } else {
                    showResult('backendResult', `Backend API response (Status: ${response.status}):\n${JSON.stringify(result, null, 2)}`, 'warning');
                }
            } catch (error) {
                console.error('Backend API test error:', error);
                showResult('backendResult', `❌ Error testing backend API: ${error.message}`, 'error');
            }
        }

        // Test 2: AJAX Handler
        async function testAjaxHandler() {
            console.log('Testing AJAX handler...');
            
            const testData = {
                action: 'create_investment',
                amount: 700,
                plan: 'basic'
            };
            
            try {
                const response = await fetch('http://localhost:8080/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                console.log('AJAX handler response:', result);
                
                if (result.error && result.error.includes('authenticated')) {
                    showResult('ajaxResult', '✅ AJAX handler is working correctly!\nReturned JSON response instead of HTML.\nAuthentication error is expected without session.', 'success');
                } else {
                    showResult('ajaxResult', `AJAX handler response:\n${JSON.stringify(result, null, 2)}`, 'warning');
                }
            } catch (error) {
                console.error('AJAX handler test error:', error);
                showResult('ajaxResult', `❌ Error testing AJAX handler: ${error.message}`, 'error');
            }
        }

        // Test 3: Complete Flow (would need real authentication)
        async function testCompleteFlow() {
            console.log('Testing complete investment flow...');
            
            const amount = document.getElementById('testAmount').value;
            const plan = document.getElementById('testPlan').value;
            
            showResult('completeFlowResult', 'Complete flow test would require real user authentication.\nThe API fixes are working correctly based on the previous tests.', 'warning');
            
            console.log(`Would create investment: ${amount} USDT in ${plan} plan`);
        }

        // Initialize
        console.log('Investment Creation Fix Test initialized');
        console.log('Backend API: http://localhost:8000');
        console.log('Frontend Server: http://localhost:8080');
    </script>
</body>
</html>
