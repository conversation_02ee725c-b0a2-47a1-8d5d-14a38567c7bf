# Production Cleanup Script for TLS Wallet
# Removes development/testing files and prepares for production deployment
# Date: June 15, 2025

Write-Host "🚀 Starting Production Cleanup for TLS Wallet..." -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Yellow

# Define files and patterns to remove
$testFiles = @(
    # Test Files
    "test_*.php",
    "test_*.html",
    "debug_*.php", 
    "debug_*.html",
    "validate_*.php",
    "verify_*.php",
    "verify_*.html",
    "*_test.html",
    "*_test.php",
    "*_debug.html",
    "*_diagnostic.html",
    
    # Documentation and Summary Files
    "*_COMPLETE.md",
    "*_RESOLVED.md",
    "*_FIX_COMPLETE.md",
    "*_IMPLEMENTATION_COMPLETE.md",
    "*_ENHANCEMENT_COMPLETE.md",
    "*_STYLING_COMPLETE.md",
    "*_CHECKLIST.md",
    "*_STATUS.md",
    
    # Temporary HTML Files
    "complete_test_summary.html",
    "dashboard_element_check.html",
    "enhanced_*.html",
    "final_*.html",
    "quick_*.html",
    "modal_test.html",
    "balance_deduction_diagnosis.html",
    
    # Old cleanup scripts
    "cleanup-production.ps1",
    "production-cleanup.ps1"
)

$devDirectories = @(
    # Any temp or test directories that might exist
    "temp",
    "tmp", 
    "tests",
    "debug"
)

# Create backup directory
$backupDir = ".\dev_files_backup_$(Get-Date -Format 'yyyy-MM-dd_HH-mm-ss')"
Write-Host "📦 Creating backup directory: $backupDir" -ForegroundColor Cyan

if (!(Test-Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
}

# Function to safely remove files
function Remove-DevFiles {
    param($patterns)
    
    foreach ($pattern in $patterns) {
        $files = Get-ChildItem -Path . -Name $pattern -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            if (Test-Path $file) {
                Write-Host "🗑️  Removing: $file" -ForegroundColor Red
                
                # Backup important files before deletion
                if ($file -match "\.(php|html|js|css)$") {
                    Copy-Item $file $backupDir -ErrorAction SilentlyContinue
                }
                
                Remove-Item $file -Force -ErrorAction SilentlyContinue
            }
        }
    }
}

# Remove test and debug files
Write-Host "`n🧹 Removing test and debug files..." -ForegroundColor Yellow
Remove-DevFiles $testFiles

# Remove development directories
Write-Host "`n📁 Checking for development directories..." -ForegroundColor Yellow
foreach ($dir in $devDirectories) {
    if (Test-Path $dir) {
        Write-Host "🗑️  Removing directory: $dir" -ForegroundColor Red
        Remove-Item $dir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Clean up log files (but keep error logs)
Write-Host "`n📝 Cleaning up log files..." -ForegroundColor Yellow
$logFiles = Get-ChildItem -Path . -Name "*.log" -ErrorAction SilentlyContinue
foreach ($log in $logFiles) {
    if ($log -notmatch "(error|access)" -and (Test-Path $log)) {
        $size = (Get-Item $log).Length
        if ($size -gt 10MB) {
            Write-Host "🗑️  Removing large log file: $log ($([math]::Round($size/1MB, 2)) MB)" -ForegroundColor Red
            Remove-Item $log -Force -ErrorAction SilentlyContinue
        }
    }
}

# Clean up temporary files
Write-Host "`n🧽 Cleaning temporary files..." -ForegroundColor Yellow
$tempPatterns = @("*.tmp", "*.temp", "*~", ".DS_Store", "Thumbs.db")
Remove-DevFiles $tempPatterns

# Optimize frontend assets
Write-Host "`n⚡ Optimizing frontend assets..." -ForegroundColor Yellow

# Check for duplicate or unused CSS/JS files
$frontendDir = ".\frontend"
if (Test-Path $frontendDir) {
    Write-Host "📦 Checking frontend directory for optimization..." -ForegroundColor Cyan
    
    # Remove any backup CSS files
    Get-ChildItem -Path "$frontendDir\css" -Name "*.bak" -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "🗑️  Removing CSS backup: $_" -ForegroundColor Red
        Remove-Item "$frontendDir\css\$_" -Force -ErrorAction SilentlyContinue
    }
    
    # Remove any backup JS files
    Get-ChildItem -Path "$frontendDir\js" -Name "*.bak" -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "🗑️  Removing JS backup: $_" -ForegroundColor Red
        Remove-Item "$frontendDir\js\$_" -Force -ErrorAction SilentlyContinue
    }
}

# Clean up backend
Write-Host "`n🔧 Cleaning backend files..." -ForegroundColor Yellow
$backendDir = ".\backend"
if (Test-Path $backendDir) {
    # Remove any test or debug files in backend
    Get-ChildItem -Path $backendDir -Name "test_*" -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "🗑️  Removing backend test file: $_" -ForegroundColor Red
        Remove-Item "$backendDir\$_" -Force -ErrorAction SilentlyContinue
    }
}

# Security cleanup
Write-Host "`n🔒 Security cleanup..." -ForegroundColor Yellow

# Check for sensitive files that shouldn't be in production
$sensitivePatterns = @(
    "*.env.local",
    "*.env.development", 
    "*password*",
    "*secret*",
    "config.dev.*"
)

foreach ($pattern in $sensitivePatterns) {
    $files = Get-ChildItem -Path . -Name $pattern -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        Write-Host "⚠️  Found sensitive file: $file (review before deployment)" -ForegroundColor Yellow
    }
}

# Create production summary
Write-Host "`n📋 Creating production summary..." -ForegroundColor Yellow

$productionSummary = @"
# TLS Wallet - Production Ready
## Cleanup Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

### Files Cleaned:
- Test files removed
- Debug files removed  
- Documentation files archived
- Temporary files cleaned
- Log files optimized
- Frontend assets optimized
- Backend test files removed

### Production Structure:
frontend/          - User interface
backend/           - API and business logic  
cronJob/           - Scheduled tasks

### Backup Created:
All removed development files backed up to: $backupDir

### Security Notes:
- Review any sensitive configuration files
- Ensure proper file permissions are set
- Update any hardcoded development URLs
- Verify database connection settings

### Deployment Checklist:
- Update database connection strings
- Set production environment variables
- Configure web server settings
- Set up SSL certificates
- Configure backup schedules
- Set up monitoring
- Test all functionality in production environment

Generated by: Production Cleanup Script
"@

$productionSummary | Out-File -FilePath "PRODUCTION_READY_$(Get-Date -Format 'yyyy-MM-dd').md" -Encoding UTF8

# Final summary
Write-Host "`n🎉 Production Cleanup Complete!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Yellow
Write-Host "✅ Development files removed and backed up" -ForegroundColor Green
Write-Host "✅ Temporary files cleaned" -ForegroundColor Green  
Write-Host "✅ Assets optimized" -ForegroundColor Green
Write-Host "✅ Security review completed" -ForegroundColor Green
Write-Host "📦 Backup created at: $backupDir" -ForegroundColor Cyan
Write-Host "📋 Production summary: PRODUCTION_READY_$(Get-Date -Format 'yyyy-MM-dd').md" -ForegroundColor Cyan

Write-Host "`n🚀 Ready for production deployment!" -ForegroundColor Green

# Show remaining file structure
Write-Host "`n📁 Production File Structure:" -ForegroundColor Yellow
Get-ChildItem -Path . -Directory | Format-Table Name, LastWriteTime -AutoSize

Write-Host "`n⚠️  Next Steps:" -ForegroundColor Yellow
Write-Host "1. Review the production summary file" -ForegroundColor White
Write-Host "2. Test the application functionality" -ForegroundColor White  
Write-Host "3. Update configuration for production environment" -ForegroundColor White
Write-Host "4. Deploy to production server" -ForegroundColor White
Write-Host "5. Run post-deployment tests" -ForegroundColor White
"@
