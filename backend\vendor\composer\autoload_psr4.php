<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'phpseclib\\' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
    'kornrunner\\' => array($vendorDir . '/kornrunner/secp256k1/src', $vendorDir . '/kornrunner/keccak/src'),
    'Web3\\' => array($vendorDir . '/iexbase/web3.php/src'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Simbi\\Tls\\Tests\\' => array($baseDir . '/tests'),
    'Simbi\\Tls\\Frontend\\' => array($baseDir . '/../frontend/src'),
    'Simbi\\Tls\\CronJob\\' => array($baseDir . '/../cronJob/src'),
    'Simbi\\Tls\\' => array($baseDir . '/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Mdanter\\Ecc\\' => array($vendorDir . '/mdanter/ecc/src'),
    'IEXBase\\TronAPI\\' => array($vendorDir . '/iexbase/tron-api/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'FG\\' => array($vendorDir . '/fgrosse/phpasn1/lib'),
    'Elliptic\\' => array($vendorDir . '/simplito/elliptic-php/lib'),
    'Comely\\DataTypes\\' => array($vendorDir . '/comely-io/data-types/src'),
    'BN\\' => array($vendorDir . '/simplito/bn-php/lib'),
    'BI\\' => array($vendorDir . '/simplito/bigint-wrapper-php/lib'),
);
