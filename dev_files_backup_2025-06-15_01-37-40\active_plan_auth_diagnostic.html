<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Plan Authentication Debug</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f8f9fa;
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .btn { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            background: #007bff; 
            color: white;
        }
        .btn:disabled { 
            background: #ccc; 
            cursor: not-allowed; 
        }
        .result { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 5px; 
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .loading { background: #e2e3e5; color: #495057; border: 1px solid #ced4da; }
        .highlight { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 Active Plan Authentication Diagnostic</h1>
    <p>Complete diagnostic tool to identify and fix the active plan page authentication issue.</p>

    <div class="highlight">
        <strong>Issue:</strong> Active plan page redirects to login instead of showing content.<br>
        <strong>Expected:</strong> Page should display investment details and earnings when user is authenticated.
    </div>

    <div class="test-section">
        <h3>Step 1: Login & Session Establishment</h3>
        <div>
            <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
            <input type="password" id="loginPassword" placeholder="Password" value="password123">
            <button class="btn" onclick="performLogin()">🔐 Login</button>
        </div>
        <div id="loginResult"></div>
    </div>

    <div class="test-section">
        <h3>Step 2: Session Verification</h3>
        <button class="btn" onclick="testSessionAuth()">🔍 Check Session Status</button>
        <div id="sessionResult"></div>
    </div>

    <div class="test-section">
        <h3>Step 3: Active Plan Page Direct Access</h3>
        <div>
            <input type="text" id="investmentId" placeholder="Investment ID" value="1">
            <button class="btn" onclick="testActivePlanAccess()">📄 Test Page Access</button>
            <button class="btn" onclick="openActivePlanPage()">🔗 Open in New Tab</button>
        </div>
        <div id="pageAccessResult"></div>
    </div>

    <div class="test-section">
        <h3>Step 4: Active Plan APIs Test</h3>
        <button class="btn" onclick="testActivePlanAPIs()">🔌 Test Investment APIs</button>
        <div id="apiResult"></div>
    </div>

    <div class="test-section">
        <h3>Step 5: Navigation Simulation</h3>
        <button class="btn" onclick="simulateDashboardNavigation()">🧭 Simulate Dashboard → Active Plan</button>
        <div id="navigationResult"></div>
    </div>

    <div class="test-section">
        <h3>🎯 Diagnosis Results</h3>
        <div id="diagnosisResult"></div>
    </div>

    <script>
        let authState = {
            loggedIn: false,
            sessionValid: false,
            pageAccessible: false,
            apisWorking: false
        };

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function updateDiagnosis() {
            const { loggedIn, sessionValid, pageAccessible, apisWorking } = authState;
            
            let diagnosis = '🔍 DIAGNOSTIC ANALYSIS:\n\n';
            
            if (!loggedIn) {
                diagnosis += '❌ LOGIN FAILED\n';
                diagnosis += '→ Cannot proceed with authentication tests\n';
                diagnosis += '→ Check credentials and API connectivity\n';
            } else if (!sessionValid) {
                diagnosis += '❌ SESSION AUTHENTICATION FAILED\n';
                diagnosis += '→ Login works but session is not being maintained\n';
                diagnosis += '→ Check session configuration in FrontendConfig\n';
                diagnosis += '→ Verify $_SESSION variables are being set correctly\n';
            } else if (!pageAccessible) {
                diagnosis += '❌ ACTIVE PLAN PAGE ACCESS DENIED\n';
                diagnosis += '→ Session auth works but page redirects to login\n';
                diagnosis += '→ Issue is in active_plan.php authentication check\n';
                diagnosis += '→ Check FrontendConfig::isAuthenticated() method\n';
            } else if (!apisWorking) {
                diagnosis += '❌ ACTIVE PLAN APIs FAILING\n';
                diagnosis += '→ Page loads but APIs return authentication errors\n';
                diagnosis += '→ Check ajax.php authentication for investment endpoints\n';
                diagnosis += '→ Verify API token handling in APIWrapper\n';
            } else {
                diagnosis += '✅ ALL AUTHENTICATION WORKING\n';
                diagnosis += '→ Login, session, page access, and APIs all functional\n';
                diagnosis += '→ Issue may be in navigation or user experience flow\n';
            }
            
            diagnosis += '\n📋 CURRENT STATUS:\n';
            diagnosis += `Login: ${loggedIn ? '✅' : '❌'}\n`;
            diagnosis += `Session: ${sessionValid ? '✅' : '❌'}\n`;
            diagnosis += `Page Access: ${pageAccessible ? '✅' : '❌'}\n`;
            diagnosis += `APIs: ${apisWorking ? '✅' : '❌'}\n`;
            
            showResult('diagnosisResult', diagnosis, loggedIn && sessionValid && pageAccessible && apisWorking ? 'success' : 'warning');
        }

        async function performLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                showResult('loginResult', '🔄 Attempting login...', 'loading');
                
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    authState.loggedIn = true;
                    showResult('loginResult', 
                        `✅ LOGIN SUCCESSFUL
User ID: ${result.user?.id}
Email: ${result.user?.email}
Token: ${result.token ? 'Present' : 'Missing'}
Admin: ${result.user?.is_admin ? 'Yes' : 'No'}

Session should now be established with:
- user_id: ${result.user?.id}
- email: ${result.user?.email}
- token: [JWT Token]
- is_admin: ${result.user?.is_admin}`, 
                        'success'
                    );
                } else {
                    authState.loggedIn = false;
                    showResult('loginResult', `❌ LOGIN FAILED: ${result.error}`, 'error');
                }
            } catch (error) {
                authState.loggedIn = false;
                showResult('loginResult', `❌ LOGIN ERROR: ${error.message}`, 'error');
            }
            
            updateDiagnosis();
        }

        async function testSessionAuth() {
            try {
                showResult('sessionResult', '🔄 Testing session authentication...', 'loading');
                
                // Test with get_balance which uses session auth
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_balance'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    authState.sessionValid = true;
                    showResult('sessionResult', 
                        `✅ SESSION AUTHENTICATION WORKING
Balance: ${result.balance} USDT
Balance Formatted: ${result.balance_formatted}

This confirms:
✓ Session is properly established
✓ $_SESSION['token'] exists and is valid
✓ FrontendConfig authentication is working
✓ AJAX authentication checks are passing`, 
                        'success'
                    );
                } else {
                    authState.sessionValid = false;
                    showResult('sessionResult', 
                        `❌ SESSION AUTHENTICATION FAILED
Error: ${result.error}

This indicates:
✗ $_SESSION['token'] is missing or invalid
✗ Session was not properly established after login
✗ FrontendConfig::isAuthenticated() would return false`, 
                        'error'
                    );
                }
            } catch (error) {
                authState.sessionValid = false;
                showResult('sessionResult', `❌ SESSION TEST ERROR: ${error.message}`, 'error');
            }
            
            updateDiagnosis();
        }

        async function testActivePlanAccess() {
            const investmentId = document.getElementById('investmentId').value;
            
            try {
                showResult('pageAccessResult', '🔄 Testing active plan page access...', 'loading');
                
                const response = await fetch(`/user/active_plan.php?id=${investmentId}`, {
                    method: 'GET',
                    credentials: 'same-origin'
                });
                
                const text = await response.text();
                const finalUrl = response.url;
                
                // Check for redirect to login
                if (finalUrl.includes('index.php') || text.includes('<title>Login') || text.includes('login-form')) {
                    authState.pageAccessible = false;
                    showResult('pageAccessResult', 
                        `❌ ACTIVE PLAN PAGE ACCESS DENIED
Final URL: ${finalUrl}
Status: ${response.status}

The page redirected to login, which means:
✗ FrontendConfig::isAuthenticated() returned false
✗ Session authentication failed at the page level
✗ This is the exact issue users are experiencing!

DIAGNOSIS: The problem is in active_plan.php authentication check.`, 
                        'error'
                    );
                } else if (text.includes('Active Investment Plan') || text.includes('active-plan-container')) {
                    authState.pageAccessible = true;
                    showResult('pageAccessResult', 
                        `✅ ACTIVE PLAN PAGE ACCESS SUCCESSFUL
Final URL: ${finalUrl}
Status: ${response.status}
Content Length: ${text.length} characters

The page loaded correctly with investment plan content.
✓ Authentication check passed
✓ Investment ID parameter accepted
✓ Page rendered without redirect`, 
                        'success'
                    );
                } else {
                    authState.pageAccessible = false;
                    showResult('pageAccessResult', 
                        `⚠️ UNEXPECTED PAGE RESPONSE
Final URL: ${finalUrl}
Status: ${response.status}
Content Length: ${text.length} characters

Response Preview:
${text.substring(0, 500)}...

The page didn't redirect to login but content is unexpected.`, 
                        'warning'
                    );
                }
            } catch (error) {
                authState.pageAccessible = false;
                showResult('pageAccessResult', `❌ PAGE ACCESS ERROR: ${error.message}`, 'error');
            }
            
            updateDiagnosis();
        }

        async function testActivePlanAPIs() {
            const investmentId = document.getElementById('investmentId').value;
            
            try {
                showResult('apiResult', '🔄 Testing active plan APIs...', 'loading');
                
                // Test investment details API
                const detailsResponse = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_investment_details',
                        id: investmentId
                    })
                });

                const detailsResult = await detailsResponse.json();
                
                // Test investment earnings API
                const earningsResponse = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_investment_earnings',
                        id: investmentId
                    })
                });

                const earningsResult = await earningsResponse.json();
                
                if (detailsResult.success && earningsResult.success) {
                    authState.apisWorking = true;
                    showResult('apiResult', 
                        `✅ ACTIVE PLAN APIs WORKING
                        
Investment Details API:
Status: ✅ Success
Data: ${JSON.stringify(detailsResult.investment || {}, null, 2)}

Investment Earnings API:
Status: ✅ Success
Earnings Count: ${earningsResult.earnings?.length || 0}

This confirms:
✓ AJAX authentication is working
✓ Backend API integration is functional
✓ Investment data can be retrieved successfully`, 
                        'success'
                    );
                } else {
                    authState.apisWorking = false;
                    const authErrors = [];
                    if (detailsResult.error?.includes('authenticated')) authErrors.push('Details API');
                    if (earningsResult.error?.includes('authenticated')) authErrors.push('Earnings API');
                    
                    showResult('apiResult', 
                        `❌ ACTIVE PLAN APIs FAILED
                        
Investment Details API:
Status: ${detailsResult.success ? '✅' : '❌'}
Error: ${detailsResult.error || 'None'}

Investment Earnings API:
Status: ${earningsResult.success ? '✅' : '❌'}
Error: ${earningsResult.error || 'None'}

${authErrors.length > 0 ? 
    `AUTHENTICATION ERRORS in: ${authErrors.join(', ')}
This indicates AJAX session authentication is failing.` : 
    'Non-authentication related API errors.'}`, 
                        'error'
                    );
                }
                
            } catch (error) {
                authState.apisWorking = false;
                showResult('apiResult', `❌ API TEST ERROR: ${error.message}`, 'error');
            }
            
            updateDiagnosis();
        }

        function openActivePlanPage() {
            const investmentId = document.getElementById('investmentId').value;
            window.open(`/user/active_plan.php?id=${investmentId}`, '_blank');
        }

        async function simulateDashboardNavigation() {
            try {
                showResult('navigationResult', '🔄 Simulating dashboard navigation...', 'loading');
                
                // First load dashboard
                const dashboardResponse = await fetch('/user/dashboard.php');
                const dashboardSuccess = dashboardResponse.ok && !dashboardResponse.url.includes('index.php');
                
                if (!dashboardSuccess) {
                    showResult('navigationResult', 
                        `❌ DASHBOARD ACCESS FAILED
Dashboard URL: ${dashboardResponse.url}
Status: ${dashboardResponse.status}

Cannot simulate navigation - dashboard itself is not accessible.`, 
                        'error'
                    );
                    return;
                }
                
                // Then test active plan navigation
                const investmentId = document.getElementById('investmentId').value;
                const activePlanResponse = await fetch(`/user/active_plan.php?id=${investmentId}`);
                const activePlanSuccess = activePlanResponse.ok && !activePlanResponse.url.includes('index.php');
                
                showResult('navigationResult', 
                    `📊 NAVIGATION SIMULATION RESULTS
                    
Dashboard Access:
URL: ${dashboardResponse.url}
Status: ${dashboardResponse.status} ${dashboardSuccess ? '✅' : '❌'}

Active Plan Access:
URL: ${activePlanResponse.url} 
Status: ${activePlanResponse.status} ${activePlanSuccess ? '✅' : '❌'}

Navigation Flow: Dashboard → Active Plan ${activePlanSuccess ? '✅ WORKING' : '❌ BROKEN'}

${activePlanSuccess ? 
    'Navigation simulation successful! Users should be able to navigate from dashboard to active plan without issues.' : 
    'Navigation simulation failed! This confirms the reported issue - users get redirected to login when clicking active plan links.'}`, 
                    activePlanSuccess ? 'success' : 'error'
                );
                
            } catch (error) {
                showResult('navigationResult', `❌ NAVIGATION SIMULATION ERROR: ${error.message}`, 'error');
            }
        }

        // Initialize
        updateDiagnosis();
        console.log('Active Plan Authentication Diagnostic Tool loaded');
        console.log('Run tests in order: Login → Session → Page Access → APIs');
    </script>
</body>
</html>
