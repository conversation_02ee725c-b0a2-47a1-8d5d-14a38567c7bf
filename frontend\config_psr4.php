<?php

declare(strict_types=1);

/**
 * PSR-4 Compliant Frontend Configuration Entry Point
 * 
 * This file provides backward compatibility while using the new PSR-4 structure
 */

// Include composer autoloader
require_once __DIR__ . '/../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize frontend configuration
FrontendConfig::init();

/**
 * Backward compatible FrontendConfig class
 * Provides the same interface as the old FrontendConfig class
 */
class FrontendConfig_Legacy
{
    // API Configuration
    const API_BASE_URL = 'http://localhost:8000';
    const API_TIMEOUT = 30;
    
    // Session Configuration
    const SESSION_LIFETIME = 3600;
    const SESSION_NAME = 'tls_session';
    
    // Security Configuration
    const ENABLE_HTTPS_ONLY = false;
    const SECURE_COOKIES = false;
    
    // Application Configuration
    const APP_NAME = 'TLS Crypto Wallet';
    const APP_VERSION = '1.0.0';
    const DEFAULT_CURRENCY = 'TRX';
    
    // Pagination Settings
    const DEFAULT_PAGE_SIZE = 20;
    const MAX_PAGE_SIZE = 100;
    
    // File Upload Settings
    const MAX_UPLOAD_SIZE = 5242880;
    const ALLOWED_UPLOAD_TYPES = ['jpg', 'jpeg', 'png', 'gif'];
    
    // Debug Settings
    const DEBUG_MODE = true;
    const LOG_ERRORS = true;

    /**
     * Get API base URL
     */
    public static function getApiUrl(): string
    {
        return FrontendConfig::get('API_BASE_URL', self::API_BASE_URL);
    }
    
    /**
     * Get session configuration
     */
    public static function getSessionConfig(): array
    {
        return [
            'lifetime' => FrontendConfig::get('SESSION_LIFETIME', self::SESSION_LIFETIME),
            'name' => FrontendConfig::get('SESSION_NAME', self::SESSION_NAME),
            'secure' => FrontendConfig::get('SECURE_COOKIES', self::SECURE_COOKIES),
            'httponly' => true,
            'samesite' => 'Lax'
        ];
    }
    
    /**
     * Initialize session with security settings
     */
    public static function initSession(): void
    {
        SessionService::init();
    }
    
    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated(): bool
    {
        return SessionService::isAuthenticated();
    }
    
    /**
     * Check if user is admin
     */
    public static function isAdmin(): bool
    {
        return SessionService::isAdmin();
    }
    
    /**
     * Get current user data
     */
    public static function getCurrentUser(): ?array
    {
        return SessionService::getCurrentUser();
    }
    
    /**
     * Set error reporting based on debug mode
     */
    public static function setErrorReporting(): void
    {
        $debugMode = FrontendConfig::get('DEBUG_MODE', self::DEBUG_MODE);
        $logErrors = FrontendConfig::get('LOG_ERRORS', self::LOG_ERRORS);
        
        if ($debugMode) {
            error_reporting(E_ALL);
            ini_set('display_errors', '1');
            ini_set('log_errors', '1');
        } else {
            error_reporting(0);
            ini_set('display_errors', '0');
            ini_set('log_errors', $logErrors ? '1' : '0');
        }
    }
    
    /**
     * Get environment-specific configuration
     */
    public static function getEnvironmentConfig(): array
    {
        $env = FrontendConfig::get('ENVIRONMENT', 'development');
        
        switch ($env) {
            case 'production':
                return [
                    'api_url' => 'https://api.tlswallet.com',
                    'debug' => false,
                    'secure_cookies' => true,
                    'https_only' => true
                ];
            
            case 'staging':
                return [
                    'api_url' => 'https://staging-api.tlswallet.com',
                    'debug' => true,
                    'secure_cookies' => true,
                    'https_only' => true
                ];
            
            default: // development
                return [
                    'api_url' => 'http://localhost:8000',
                    'debug' => true,
                    'secure_cookies' => false,
                    'https_only' => false
                ];
        }
    }
}

// Initialize configuration for backward compatibility
if (!class_exists('FrontendConfig')) {
    class_alias('FrontendConfig_Legacy', 'FrontendConfig');
}

// Initialize configuration
FrontendConfig_Legacy::setErrorReporting();
FrontendConfig_Legacy::initSession();
