// Wallet Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initWalletPage();
});

let walletData = null;

// Helper function to safely toggle button visibility
function toggleButton(buttonId, show) {
    const button = document.getElementById(buttonId);
    if (button) {
        button.style.display = show ? 'inline-block' : 'none';
    }
}

function initWalletPage() {
    setupWalletForms();
    loadWalletData();
    initFooterMenu();
}

function setupWalletForms() {
    // Wallet actions
    const createWalletBtn = document.getElementById('createWalletBtn');
    const refreshBalanceBtn = document.getElementById('refreshBalanceBtn');
    const copyAddressBtn = document.getElementById('copyAddressBtn');
    const sweepFundsBtn = document.getElementById('sweepFundsBtn');

    if (createWalletBtn) {
        createWalletBtn.addEventListener('click', createWallet);
    }    if (refreshBalanceBtn) {
        refreshBalanceBtn.addEventListener('click', refreshLiveBalance);
    }

    if (copyAddressBtn) {
        copyAddressBtn.addEventListener('click', copyToClipboard);
    }

    if (sweepFundsBtn) {
        sweepFundsBtn.addEventListener('click', sweepFunds);
    }

    // Forms
    const withdrawForm = document.getElementById('withdrawForm');

    if (withdrawForm) {
        withdrawForm.addEventListener('submit', handleWithdraw);
    }
}

async function loadWalletData() {
    try {
        const response = await apiCall('get_wallet');
        if (response.success && response.wallet) {
            walletData = response.wallet;
            updateWalletDisplay();
        } else {
            // No wallet exists
            const addressElement = document.getElementById('walletAddress');
            const balanceElement = document.getElementById('walletBalance');
            
            if (addressElement) addressElement.value = 'No wallet created';
            if (balanceElement) balanceElement.value = '0.00 USDT';
            
            toggleButton('createWalletBtn', true);
            toggleButton('refreshBalanceBtn', false);
            toggleButton('copyAddressBtn', false);
            toggleButton('sweepFundsBtn', false);
        }
    } catch (error) {
        console.error('Error loading wallet data:', error);
        showMessage('Error loading wallet data', 'error');
    }
}

function updateWalletDisplay() {
    if (walletData) {
        const addressElement = document.getElementById('walletAddress');
        const balanceElement = document.getElementById('walletBalance');
        
        if (addressElement) addressElement.value = walletData.address;
        if (balanceElement) {
            // Show loading while fetching live balance
            balanceElement.value = 'Loading... USDT';
            
            // Fetch live balance
            fetchLiveBalance();
        }
        
        // Show/hide buttons based on wallet existence
        toggleButton('createWalletBtn', false);
        toggleButton('refreshBalanceBtn', true);
        toggleButton('copyAddressBtn', true);
        toggleButton('sweepFundsBtn', true);
    }
}

/**
 * Fetch and display live balance from blockchain
 */
async function fetchLiveBalance() {
    if (!walletData || !walletData.address) return;
    
    try {
        const response = await apiCall('get_live_wallet', {
            address: walletData.address
        });
        
        const balanceElement = document.getElementById('walletBalance');
        if (balanceElement) {
            if (response.success && typeof response.balance !== 'undefined') {
                // Convert balance to USDT format (2 decimal places)
                const balance = parseFloat(response.balance || 0).toFixed(2);
                balanceElement.value = `${balance} USDT`;
                console.log(`Live balance updated: ${balance} USDT`);
            } else {
                // Fallback to database balance if live balance fails
                const balance = parseFloat(walletData.balance || 0).toFixed(2);
                balanceElement.value = `${balance} USDT (cached)`;
                console.warn('Live balance failed, using cached balance:', response.error);
            }
        }
    } catch (error) {
        console.error('Error fetching live balance:', error);
        const balanceElement = document.getElementById('walletBalance');
        if (balanceElement) {
            // Fallback to database balance
            const balance = parseFloat(walletData.balance || 0).toFixed(2);
            balanceElement.value = `${balance} USDT (cached)`;
        }
    }
}

/**
 * Refresh live balance with loading indication
 */
async function refreshLiveBalance() {
    const btn = document.getElementById('refreshBalanceBtn');
    setButtonLoading(btn, true);
    
    try {
        await fetchLiveBalance();
        showMessage('Balance refreshed successfully!', 'success');
    } catch (error) {
        console.error('Error refreshing balance:', error);
        showMessage('Error refreshing balance. Please try again.', 'error');
    } finally {
        setButtonLoading(btn, false);
    }
}

async function createWallet() {
    const btn = document.getElementById('createWalletBtn');
    setButtonLoading(btn, true);
    
    try {
        const response = await apiCall('create_wallet');
        if (response.success) {
            showMessage('Wallet created successfully!', 'success');
            await loadWalletData();
        } else {
            showMessage(response.error || 'Failed to create wallet', 'error');
        }
    } catch (error) {
        console.error('Error creating wallet:', error);
        showMessage('Error creating wallet. Please try again.', 'error');
    } finally {
        setButtonLoading(btn, false);
    }
}

async function handleWithdraw(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const to = formData.get('to');
    const amount = parseFloat(formData.get('amount'));
    
    if (!to || !amount) {
        showMessage('Please fill in all fields', 'error');
        return;
    }
    
    if (amount <= 0) {
        showMessage('Amount must be greater than 0', 'error');
        return;
    }
    
    // Check if wallet has sufficient balance
    if (walletData && amount > parseFloat(walletData.balance)) {
        showMessage('Insufficient balance', 'error');
        return;
    }
    
    setButtonLoading(submitBtn, true);
    
    try {
        const response = await apiCall('withdraw', {
            to: to,
            amount: amount
        });
        
        if (response.success) {
            showMessage('Withdrawal initiated successfully!', 'success');
            form.reset();
            await loadWalletData(); // Refresh balance
        } else {
            showMessage(response.error || 'Failed to process withdrawal', 'error');
        }
    } catch (error) {
        console.error('Error processing withdrawal:', error);
        showMessage('Error processing withdrawal. Please try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

async function sweepFunds() {
    if (!confirm('Are you sure you want to sweep all available funds? This action cannot be undone.')) {
        return;
    }
    
    const btn = document.getElementById('sweepFundsBtn');
    setButtonLoading(btn, true);
    
    try {
        const response = await apiCall('sweep_funds');
        if (response.success) {
            showMessage('Funds swept successfully!', 'success');
            await loadWalletData();
        } else {
            showMessage(response.error || 'Failed to sweep funds', 'error');
        }
    } catch (error) {
        console.error('Error sweeping funds:', error);
        showMessage('Error sweeping funds. Please try again.', 'error');
    } finally {
        setButtonLoading(btn, false);
    }
}

function copyToClipboard() {
    if (walletData && walletData.address) {
        navigator.clipboard.writeText(walletData.address).then(() => {
            showMessage('Address copied to clipboard!', 'success');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = walletData.address;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showMessage('Address copied to clipboard!', 'success');
        });
    }
}

// Shared utility functions
async function apiCall(endpoint, data = null) {
    const url = `../ajax.php`;
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: endpoint,
            ...data
        })
    };
    
    const response = await fetch(url, options);
    return await response.json();
}

function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';
    
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 3000);
}

function setButtonLoading(button, loading) {
    if (loading) {
        button.disabled = true;
        button.dataset.originalText = button.textContent;
        button.textContent = 'Loading...';
    } else {
        button.disabled = false;
        button.textContent = button.dataset.originalText || button.textContent;
    }
}

function initFooterMenu() {
    // Add smooth entrance animation for footer menu
    const footerMenu = document.querySelector('.footer-menu');
    if (footerMenu) {
        // Always show footer menu
        footerMenu.classList.add('show');
        
        // Optional: Handle window resize for future mobile-specific behavior
        window.addEventListener('resize', function() {
            // Footer is always visible now, but this can be customized later
            footerMenu.classList.add('show');
        });
    }
}
