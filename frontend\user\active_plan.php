<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

// Get current user
$user = SessionService::getCurrentUser();

// Get investment ID from URL parameter
$investmentId = $_GET['id'] ?? null;
if (!$investmentId) {
    header('Location: dashboard.php');
    exit();
}

// Set variables for header
$pageTitle = 'TRON Wallet - Active Investment Plan';
$currentPage = 'active_plan';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>

<!-- Active Plan specific styles -->
<style>
    .active-plan-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

        .plan-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
            text-align: center;
        }

        .plan-header h1 {
            margin: 0 0 16px 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .plan-header .plan-status {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .plan-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .overview-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .overview-card h3 {
            margin: 0 0 16px 0;
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        .progress-section {
            margin-bottom: 24px;
        }

        .progress-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .progress-bar-container {
            position: relative;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            margin: 16px 0;
            overflow: hidden;
        }

        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-bar-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .earnings-table-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            margin-bottom: 32px;
        }

        .earnings-table-section h3 {
            margin: 0 0 24px 0;
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .earnings-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .earnings-table th,
        .earnings-table td {
            padding: 16px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .earnings-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .earnings-table tr:hover {
            background: #f8f9fa;
        }

        .earnings-amount {
            font-weight: 600;
            color: #28a745;
        }

        .earnings-date {
            color: #6c757d;
        }

        .timer-section {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            margin-bottom: 32px;
        }

        .timer-section h3 {
            margin: 0 0 24px 0;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .countdown-timer {
            display: flex;
            justify-content: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .timer-unit {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 16px;
            min-width: 80px;
            backdrop-filter: blur(10px);
        }

        .timer-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .timer-label {
            font-size: 0.8rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            text-align: center;
        }

        .empty-state {
            text-align: center;
            padding: 48px 24px;
            color: #6c757d;
        }

        .empty-state h4 {
            margin: 0 0 8px 0;
            color: #495057;
        }

        @media (max-width: 768px) {
            .active-plan-container {
                padding: 16px;
            }

            .plan-header {
                padding: 24px;
            }

            .plan-header h1 {
                font-size: 2rem;
            }

            .plan-overview {
                grid-template-columns: 1fr;
            }

            .overview-stats {
                grid-template-columns: 1fr;
            }

            .countdown-timer {
                gap: 12px;
            }

            .timer-unit {
                min-width: 70px;
                padding: 12px;
            }

            .timer-value {
                font-size: 1.5rem;
            }

            .action-buttons {
                flex-direction: column;
            }

            .earnings-table {
                font-size: 0.8rem;
            }

            .earnings-table th,
            .earnings-table td {
                padding: 12px 8px;
            }        }
    </style>

    <div class="active-plan-container">
        <!-- Plan Header -->
        <div class="plan-header">
            <h1 id="planName">Loading Plan...</h1>
            <div class="plan-status" id="planStatus">🔄 Active</div>
        </div>

        <!-- Plan Overview -->
        <div class="plan-overview">
            <!-- Investment Details -->
            <div class="overview-card">
                <h3>💰 Investment Details</h3>
                <div class="overview-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="investmentAmount">--</div>
                        <div class="stat-label">Investment Amount</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="dailyReturn">--</div>
                        <div class="stat-label">Daily Return</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="totalEarned">--</div>
                        <div class="stat-label">Total Earned</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="planDuration">--</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>

            <!-- Progress Tracking -->
            <div class="overview-card">
                <h3>📈 Progress Tracking</h3>
                <div class="progress-section">
                    <div class="progress-container">
                        <div class="progress-info">
                            <span id="currentDay">Day 0</span>
                            <span id="progressPercentage">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill" id="progressBar" style="width: 0%"></div>
                        </div>
                        <div class="progress-info">
                            <span id="daysRemaining">-- days remaining</span>
                            <span id="completionDate">--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Next Payout Timer -->
        <div class="timer-section">
            <h3>⏰ Next Payout In</h3>
            <div class="countdown-timer" id="countdownTimer">
                <div class="timer-unit">
                    <div class="timer-value" id="hours">00</div>
                    <div class="timer-label">Hours</div>
                </div>
                <div class="timer-unit">
                    <div class="timer-value" id="minutes">00</div>
                    <div class="timer-label">Minutes</div>
                </div>
                <div class="timer-unit">
                    <div class="timer-value" id="seconds">00</div>
                    <div class="timer-label">Seconds</div>
                </div>
            </div>
        </div>

        <!-- Earnings History Table -->
        <div class="earnings-table-section">
            <h3>📊 Earnings History</h3>
            <div id="earningsTableContainer">
                <div class="loading-spinner"></div>
                <p style="text-align: center; margin-top: 16px;">Loading earnings data...</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="dashboard.php" class="btn btn-outline">← Back to Dashboard</a>
            <a href="make_investment.php" class="btn btn-primary">Make New Investment</a>
        </div>
    </div>    <script>
        // Pass investment ID to JavaScript
        const INVESTMENT_ID = <?php echo json_encode($investmentId); ?>;
    </script>
    <script src="js/active_plan.js"></script>

<?php include '../includes/footer.php'; ?>
