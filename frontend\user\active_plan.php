<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

// Get current user
$user = SessionService::getCurrentUser();

// Get investment ID from URL parameter
$investmentId = $_GET['id'] ?? null;
if (!$investmentId) {
    header('Location: dashboard.php');
    exit();
}

// Set variables for header
$pageTitle = 'Active Investment Plan - TLS Wallet';
$currentPage = 'active_plan';
$basePath = '.';
$cssPath = '../css';

// Include header
include '../includes/header.php';
?>

<!-- Active Plan specific styles -->
<style>
    .active-plan-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 8px 4px;
    }

    .plan-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px;
        padding: 12px 8px;
        margin-bottom: 12px;
        text-align: center;
    }

    .plan-header h1 {
        margin: 0 0 8px 0;
        font-size: 1.5rem;
        font-weight: 700;
    }

    .plan-header .plan-status {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.85rem;
        font-weight: 600;
        backdrop-filter: blur(6px);
    }

    .plan-overview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 10px;
        margin-bottom: 12px;
    }

    .overview-card {
        background: white;
        border-radius: 8px;
        padding: 10px 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid #e9ecef;
    }

    .overview-card h3 {
        margin: 0 0 8px 0;
        color: #2c3e50;
        font-size: 1rem;
        font-weight: 600;
    }

    .overview-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }

    .stat-item {
        text-align: center;
    }

    .stat-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 2px;
    }

    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 500;
    }

    .investment-timeline {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 6px;
        margin: 8px 0;
        padding: 6px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 4px;
        border: 1px solid #dee2e6;
    }

    .timeline-item {
        text-align: center;
    }

    .timeline-label {
        font-size: 0.7rem;
        color: #6c757d;
        margin-bottom: 2px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }

    .timeline-value {
        font-size: 0.8rem;
        color: #2c3e50;
        font-weight: 600;
    }

    .earnings-milestone {
        margin-top: 8px;
        padding: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 6px;
        text-align: center;
        color: white;
    }

    .milestone-label {
        font-size: 0.8rem;
        margin-bottom: 2px;
        opacity: 0.9;
        font-weight: 500;
    }

    .milestone-value {
        font-size: 1.1rem;
        font-weight: 700;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    }

    .earnings-table-section {
        background: white;
        border-radius: 8px;
        padding: 10px 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid #e9ecef;
        margin-bottom: 12px;
    }

    .earnings-table-section h3 {
        margin: 0 0 8px 0;
        color: #2c3e50;
        font-size: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .earnings-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.8rem;
    }

    .earnings-table th,
    .earnings-table td {
        padding: 6px 4px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }

    .earnings-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .earnings-table tr:hover {
        background: #f8f9fa;
    }

    .earnings-amount {
        font-weight: 600;
        color: #28a745;
    }

    .earnings-date {
        color: #6c757d;
    }

    .timer-section {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 8px;
        padding: 12px 8px;
        text-align: center;
        margin-bottom: 12px;
    }

    .timer-section h3 {
        margin: 0 0 8px 0;
        font-size: 1rem;
        font-weight: 600;
    }

    .countdown-timer {
        display: flex;
        justify-content: center;
        gap: 6px;
        flex-wrap: wrap;
    }

    .timer-unit {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        padding: 8px;
        min-width: 48px;
        backdrop-filter: blur(6px);
    }

    .timer-value {
        font-size: 1.1rem;
        font-weight: 700;
        margin-bottom: 2px;
    }

    .timer-label {
        font-size: 0.7rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn {
        padding: 6px 14px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 600;
        text-align: center;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    .btn-outline {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
    }

    .btn-outline:hover {
        background: #667eea;
        color: white;
    }

    .loading-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 8px;
        border-radius: 6px;
        margin: 8px 0;
        text-align: center;
        font-size: 0.95rem;
    }

    .empty-state {
        text-align: center;
        padding: 16px 8px;
        color: #6c757d;
        font-size: 0.95rem;
    }

    .empty-state h4 {
        margin: 0 0 4px 0;
        color: #495057;
        font-size: 1rem;
    }

    @media (max-width: 768px) {
        .active-plan-container {
            padding: 4px;
        }

        .plan-header {
            padding: 8px;
        }

        .plan-header h1 {
            font-size: 1.1rem;
        }

        .plan-overview {
            grid-template-columns: 1fr;
        }

        .overview-stats {
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
        }

        .investment-timeline {
            grid-template-columns: 1fr;
            gap: 4px;
        }

        .countdown-timer {
            gap: 4px;
        }

        .timer-unit {
            min-width: 36px;
            padding: 4px;
        }

        .timer-value {
            font-size: 0.9rem;
        }

        .action-buttons {
            flex-direction: column;
        }

        .earnings-table {
            font-size: 0.7rem;
        }

        .earnings-table th,
        .earnings-table td {
            padding: 6px 2px;
        }
    }
    </style>

    <div class="active-plan-container">
        <!-- Plan Header -->
        <div class="plan-header">
            <h1 id="planName">Loading Plan...</h1>
            <div class="plan-status" id="planStatus">🔄 Active</div>
        </div>        <!-- Plan Overview -->
        <div class="plan-overview">
            <!-- Investment Details -->
            <div class="overview-card">
                <h3>💰 Investment Details</h3>
                <div class="overview-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="investmentAmount">--</div>
                        <div class="stat-label">Investment Amount</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="dailyReturn">--</div>
                        <div class="stat-label">Daily Return</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="totalEarned">--</div>
                        <div class="stat-label">Total Earned</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="planDuration">--</div>
                        <div class="stat-label">Duration</div>
                    </div>
                </div>
            </div>

            <!-- Quick Summary -->
            <div class="overview-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <h3 style="color: white;">⚡ Quick Summary</h3>
                <div class="overview-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="quickCurrentDay" style="color: white;">Day --</div>
                        <div class="stat-label" style="color: rgba(255,255,255,0.9);">Current Progress</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="quickEndDate" style="color: white;">--</div>
                        <div class="stat-label" style="color: rgba(255,255,255,0.9);">Completion Date</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="quickRemainingDays" style="color: white;">-- days</div>
                        <div class="stat-label" style="color: rgba(255,255,255,0.9);">Days Remaining</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="quickTodayEarning" style="color: white;">+-- USDT</div>
                        <div class="stat-label" style="color: rgba(255,255,255,0.9);">Today's Earning</div>
                    </div>
                </div>
            </div>        </div>

        <!-- Next Payout Timer -->
        <div class="timer-section">
            <h3>⏰ Next Payout In</h3>
            <div class="countdown-timer" id="countdownTimer">
                <div class="timer-unit">
                    <div class="timer-value" id="hours">00</div>
                    <div class="timer-label">Hours</div>
                </div>
                <div class="timer-unit">
                    <div class="timer-value" id="minutes">00</div>
                    <div class="timer-label">Minutes</div>
                </div>
                <div class="timer-unit">
                    <div class="timer-value" id="seconds">00</div>
                    <div class="timer-label">Seconds</div>
                </div>
            </div>
        </div>

        <!-- Earnings History Table -->
        <div class="earnings-table-section">
            <h3>📊 Earnings History</h3>
            <div id="earningsTableContainer">
                <div class="loading-spinner"></div>
                <p style="text-align: center; margin-top: 16px;">Loading earnings data...</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="dashboard.php" class="btn btn-outline">← Back to Dashboard</a>
            <a href="make_investment.php" class="btn btn-primary">Make New Investment</a>
        </div>
    </div>    <script>
        // Pass investment ID to JavaScript
        const INVESTMENT_ID = <?php echo json_encode($investmentId); ?>;
    </script>
    <script src="js/active_plan.js"></script>

<?php include '../includes/footer.php'; ?>
