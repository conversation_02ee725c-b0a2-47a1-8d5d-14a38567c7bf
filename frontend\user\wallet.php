<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'My Wallet - TLS Wallet';
$currentPage = 'wallet';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>

            <!-- Wallet Content -->
            <div class="wallet-page">                <div class="page-header">
                    <h2>USDT Wallet Management</h2>
                    <p>Manage your TRON USDT wallet and funds</p>
                </div><!-- Wallet Information -->
                <div class="card">
                    <h3>Wallet Information</h3>
                    <div class="wallet-info">
                        <div class="wallet-field">
                            <label for="walletAddress">Wallet Address</label>
                            <div class="address-display">
                                <input type="text" id="walletAddress" readonly 
                                       placeholder="No wallet created" class="wallet-address-input">
                                <button id="copyAddressBtn" class="btn btn-outline btn-sm" style="display: none;">Copy</button>
                            </div>
                        </div>
                        
                        <div class="wallet-field">
                            <label for="walletBalance">Balance</label>
                            <div class="balance-display">
                                <input type="text" id="walletBalance" readonly value="0.000000 USDT" class="wallet-balance-input">
                                <button id="refreshBalanceBtn" class="btn btn-outline btn-sm" style="display: none;">Refresh</button>
                            </div>
                        </div>
                    </div>
                </div>                <!-- Withdrawal Form -->
                <div class="card">
                    <h3>Withdraw Funds</h3>
                    <form id="withdrawForm" class="withdraw-form">
                        <div class="form-group">
                            <label for="withdrawAddress">Recipient Address</label>
                            <input type="text" id="withdrawAddress" name="address" required 
                                   placeholder="Enter TRON address (T...)">
                        </div>
                        
                        <div class="form-group">
                            <label for="withdrawAmount">Amount (USDT)</label>
                            <input type="number" id="withdrawAmount" name="amount" step="0.01" min="1" required 
                                   placeholder="Enter amount to withdraw">
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Withdraw</button>
                            <button type="reset" class="btn btn-outline">Reset</button>
                        </div>
                    </form>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>    <script src="js/qr-generator.js"></script>
    <script src="js/wallet.js"></script>
</body>
</html>
