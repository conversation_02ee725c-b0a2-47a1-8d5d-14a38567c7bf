<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Active Plan Authentication</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .btn { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            background: #007bff; 
            color: white;
        }
        .btn:disabled { 
            background: #ccc; 
            cursor: not-allowed; 
        }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        .loading { background: #e2e3e5; color: #495057; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Active Plan Page Authentication Test</h1>
        <p>This test verifies authentication flow and active plan page access.</p>

        <div class="test-section">
            <h3>Step 1: Login and Authentication</h3>
            <div>
                <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
                <input type="password" id="loginPassword" placeholder="Password" value="password123">
                <button class="btn" onclick="performLogin()">Login</button>
            </div>
            <div id="loginResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 2: Test Session Authentication</h3>
            <button class="btn" onclick="testSessionAuth()">Check Session Status</button>
            <div id="sessionResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 3: Test Active Plan Page Access</h3>
            <div>
                <input type="text" id="investmentId" placeholder="Investment ID" value="1">
                <button class="btn" onclick="testActivePlanPage()">Test Active Plan Page</button>
            </div>
            <div id="planPageResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 4: Test Active Plan APIs</h3>
            <button class="btn" onclick="testActivePlanAPIs()">Test Investment Details & Earnings</button>
            <div id="apiResult"></div>
        </div>

        <div class="test-section">
            <h3>Summary</h3>
            <div id="summaryResult"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function performLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                showResult('loginResult', 'Attempting login...', 'loading');
                
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('loginResult', 
                        `✅ Login successful!
User ID: ${result.user?.id}
Email: ${result.user?.email}
Token: ${result.token ? 'Present' : 'Missing'}`, 
                        'success'
                    );
                } else {
                    showResult('loginResult', `❌ Login failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testSessionAuth() {
            try {
                showResult('sessionResult', 'Checking session authentication...', 'loading');
                
                // Test with get_balance which uses same auth pattern
                const response = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_balance'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('sessionResult', 
                        `✅ Session authentication working!
Balance: ${result.balance} USDT
Authentication chain is functional.`, 
                        'success'
                    );
                } else {
                    showResult('sessionResult', 
                        `❌ Session authentication failed: ${result.error}
This indicates the core authentication issue.`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('sessionResult', `❌ Session test error: ${error.message}`, 'error');
            }
        }

        async function testActivePlanPage() {
            const investmentId = document.getElementById('investmentId').value;
            
            try {
                showResult('planPageResult', 'Testing active plan page access...', 'loading');
                
                // Try to fetch the active plan page
                const response = await fetch(`/user/active_plan.php?id=${investmentId}`);
                
                if (response.ok) {
                    const text = await response.text();
                    
                    // Check if we got redirected to login page
                    if (text.includes('login') || text.includes('Login') || response.url.includes('index.php')) {
                        showResult('planPageResult', 
                            `❌ Active Plan Page Access FAILED
The page redirected to login instead of showing content.
This is the exact issue users are experiencing!

Response URL: ${response.url}
Status: ${response.status}`, 
                            'error'
                        );
                    } else if (text.includes('Active Investment Plan') || text.includes('plan-header')) {
                        showResult('planPageResult', 
                            `✅ Active Plan Page Access SUCCESS
The page loaded correctly and shows investment plan content.
Status: ${response.status}`, 
                            'success'
                        );
                    } else {
                        showResult('planPageResult', 
                            `⚠️ Active Plan Page Access UNCLEAR
Got response but content is unexpected.
Status: ${response.status}
Content length: ${text.length} characters`, 
                            'warning'
                        );
                    }
                } else {
                    showResult('planPageResult', 
                        `❌ Active Plan Page Access FAILED
HTTP Status: ${response.status}
Status Text: ${response.statusText}`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('planPageResult', `❌ Page access error: ${error.message}`, 'error');
            }
        }

        async function testActivePlanAPIs() {
            const investmentId = document.getElementById('investmentId').value;
            
            try {
                showResult('apiResult', 'Testing Active Plan APIs...', 'loading');
                
                // Test investment details API
                const detailsResponse = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_investment_details',
                        investment_id: investmentId
                    })
                });

                const detailsResult = await detailsResponse.json();
                
                // Test investment earnings API
                const earningsResponse = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_investment_earnings',
                        investment_id: investmentId
                    })
                });

                const earningsResult = await earningsResponse.json();
                
                let apiStatus = '';
                if (detailsResult.success && earningsResult.success) {
                    apiStatus = '✅ All APIs working correctly';
                } else if (detailsResult.error?.includes('authenticated') || earningsResult.error?.includes('authenticated')) {
                    apiStatus = '❌ API authentication failed - this explains the page redirection';
                } else {
                    apiStatus = '⚠️ APIs have other issues (not authentication)';
                }
                
                showResult('apiResult', 
                    `API Test Results:
${apiStatus}

Investment Details API:
${JSON.stringify(detailsResult, null, 2)}

Investment Earnings API:
${JSON.stringify(earningsResult, null, 2)}`, 
                    detailsResult.success && earningsResult.success ? 'success' : 'error'
                );
                
            } catch (error) {
                showResult('apiResult', `❌ API test error: ${error.message}`, 'error');
            }
        }

        function updateSummary() {
            showResult('summaryResult', 
                `🔍 Diagnostic Summary:

1. If login succeeds but session auth fails → Session configuration issue
2. If session auth works but page redirects → Active plan page specific issue  
3. If page loads but APIs fail → Backend API integration issue
4. If everything works → No authentication issues (check navigation)

Next steps will depend on which test fails first.`, 
                'info'
            );
        }

        // Initialize
        updateSummary();
        console.log('Active Plan Authentication Test loaded');
    </script>
</body>
</html>
