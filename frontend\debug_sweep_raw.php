<?php
/**
 * Raw Sweep Funds Debug - Capture exact error
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

// Start session
session_start();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Raw Sweep Debug</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .error { background: #ffe6e6; }
        .success { background: #e6ffe6; }
        .info { background: #e6f3ff; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; white-space: pre-wrap; }
        .btn { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007cba; color: white; border: none; }
    </style>
</head>
<body>";

echo "<h1>🔍 Raw Sweep Funds Debug</h1>";

// Check session
echo "<div class='section info'>";
echo "<h2>Session Status</h2>";
if (isset($_SESSION['token']) && isset($_SESSION['user_id'])) {
    echo "✅ Session active - User ID: " . $_SESSION['user_id'] . "<br>";
    echo "✅ Token length: " . strlen($_SESSION['token']) . " characters<br>";
} else {
    echo "❌ No session found. Please login first.<br>";
    echo "</div></body></html>";
    exit;
}
echo "</div>";

// Test 1: Direct AJAX call simulation
echo "<div class='section info'>";
echo "<h2>Test 1: Simulate AJAX Call</h2>";
echo "<button class='btn' onclick='testAjaxCall()'>Test AJAX sweep_funds</button>";
echo "<div id='ajax-result'></div>";
echo "</div>";

// Test 2: Direct API call
echo "<div class='section info'>";
echo "<h2>Test 2: Direct API Call</h2>";

try {
    require_once 'api_psr4.php';
    
    $api = new APIWrapper();
    $api->setToken($_SESSION['token']);
    
    echo "<button class='btn' onclick='testDirectAPI()'>Test Direct API</button>";
    echo "<div id='direct-result'></div>";
    
} catch (Exception $e) {
    echo "<div class='error'>API Wrapper Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 3: Backend direct call
echo "<div class='section info'>";
echo "<h2>Test 3: Backend Direct Call</h2>";

try {
    require_once __DIR__ . '/../backend/vendor/autoload.php';
    
    use Simbi\Tls\Controllers\WalletController;
    use Simbi\Tls\Repositories\WalletRepository;
    use Simbi\Tls\Services\TronService;
    use Simbi\Tls\Config\Database;
    
    echo "<button class='btn' onclick='testBackendDirect()'>Test Backend Direct</button>";
    echo "<div id='backend-direct-result'></div>";
    
} catch (Exception $e) {
    echo "<div class='error'>Backend Error: " . $e->getMessage() . "</div>";
}

echo "</div>";

// JavaScript for testing
echo "<script>
async function testAjaxCall() {
    const resultDiv = document.getElementById('ajax-result');
    resultDiv.innerHTML = '⏳ Testing AJAX call...';
    
    try {
        const response = await fetch('ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'sweep_funds'
            })
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        const responseText = await response.text();
        console.log('Raw response text:', responseText);
        
        resultDiv.innerHTML = '<h3>Raw Response:</h3><pre>' + responseText + '</pre>';
        
        // Try to parse as JSON
        try {
            const jsonData = JSON.parse(responseText);
            resultDiv.innerHTML += '<h3>✅ Parsed JSON:</h3><pre>' + JSON.stringify(jsonData, null, 2) + '</pre>';
        } catch (jsonError) {
            resultDiv.innerHTML += '<h3>❌ JSON Parse Error:</h3><pre>' + jsonError.message + '</pre>';
            
            // Check for common issues
            if (responseText.includes('<?php')) {
                resultDiv.innerHTML += '<div class=\"error\">⚠️ Response contains PHP code - check for syntax errors</div>';
            }
            if (responseText.includes('Fatal error')) {
                resultDiv.innerHTML += '<div class=\"error\">⚠️ PHP Fatal Error detected</div>';
            }
            if (responseText.includes('Warning:') || responseText.includes('Notice:')) {
                resultDiv.innerHTML += '<div class=\"error\">⚠️ PHP Warning/Notice detected</div>';
            }
        }
        
    } catch (error) {
        resultDiv.innerHTML = '<div class=\"error\">Network Error: ' + error.message + '</div>';
    }
}

async function testDirectAPI() {
    const resultDiv = document.getElementById('direct-result');
    resultDiv.innerHTML = '⏳ Testing direct API...';
    
    try {
        // This will be handled by PHP
        const response = await fetch('debug_sweep_raw.php?test=direct_api', {
            method: 'POST'
        });
        
        const responseText = await response.text();
        resultDiv.innerHTML = '<h3>Direct API Result:</h3><pre>' + responseText + '</pre>';
        
    } catch (error) {
        resultDiv.innerHTML = '<div class=\"error\">Error: ' + error.message + '</div>';
    }
}

async function testBackendDirect() {
    const resultDiv = document.getElementById('backend-direct-result');
    resultDiv.innerHTML = '⏳ Testing backend direct...';
    
    try {
        const response = await fetch('debug_sweep_raw.php?test=backend_direct', {
            method: 'POST'
        });
        
        const responseText = await response.text();
        resultDiv.innerHTML = '<h3>Backend Direct Result:</h3><pre>' + responseText + '</pre>';
        
    } catch (error) {
        resultDiv.innerHTML = '<div class=\"error\">Error: ' + error.message + '</div>';
    }
}
</script>";

// Handle test requests
if (isset($_GET['test'])) {
    header('Content-Type: application/json');
    
    try {
        if ($_GET['test'] === 'direct_api') {
            require_once 'api_psr4.php';
            $api = new APIWrapper();
            $api->setToken($_SESSION['token']);
            $result = $api->sweepFunds();
            echo json_encode($result);
        } elseif ($_GET['test'] === 'backend_direct') {
            require_once __DIR__ . '/../backend/vendor/autoload.php';
            
            use Simbi\Tls\Controllers\WalletController;
            use Simbi\Tls\Repositories\WalletRepository;
            use Simbi\Tls\Services\TronService;
            use Simbi\Tls\Config\Database;
            
            $database = new Database();
            $pdo = $database->getConnection();
            $walletRepository = new WalletRepository($pdo);
            $tronService = new TronService();
            $walletController = new WalletController($walletRepository, $tronService);
            
            $user = ['id' => $_SESSION['user_id']];
            $result = $walletController->sweepFunds([], $user);
            echo json_encode($result);
        }
    } catch (Exception $e) {
        echo json_encode(['error' => 'Test error: ' . $e->getMessage()]);
    }
    exit;
}

echo "</body></html>";
?>
