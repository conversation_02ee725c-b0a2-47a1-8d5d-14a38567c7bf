<?php
// Test the make investment API endpoints
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing Make Investment API Endpoints</h2>";

// Test get_investment_plans endpoint
echo "<h3>1. Testing get_investment_plans endpoint:</h3>";

$postData = json_encode(['action' => 'get_investment_plans']);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $postData
    ]
]);

$response = file_get_contents('http://localhost:8080/frontend/ajax.php', false, $context);

if ($response) {
    $data = json_decode($response, true);
    echo "<pre>";
    echo "Response: " . print_r($data, true);
    echo "</pre>";
    
    if (isset($data['success']) && $data['success']) {
        echo "<p style='color: green;'>✅ API endpoint working correctly!</p>";
        echo "<p>Found " . count($data['plans']) . " investment plans</p>";
        
        foreach ($data['plans'] as $plan) {
            $status = $plan['is_active'] ? 'Active' : 'Disabled';
            $statusColor = $plan['is_active'] ? 'green' : 'red';
            echo "<p><strong>{$plan['plan_name']}</strong> - <span style='color: {$statusColor}'>{$status}</span></p>";
        }
    } else {
        echo "<p style='color: red;'>❌ API returned error</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Failed to connect to API</p>";
}

echo "<hr>";
echo "<h3>2. Testing Page Access:</h3>";
echo "<a href='frontend/user/make_investment.php' target='_blank'>Open Make Investment Page</a>";

echo "<hr>";
echo "<h3>3. CSS Test:</h3>";
echo "<div class='make-investment-page' style='padding: 20px; background: #f5f5f5; border-radius: 8px;'>";
echo "<div class='page-header' style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h2>Make Investment</h2>";
echo "<p>CSS styling test - if you see styled content, CSS is working</p>";
echo "</div>";
echo "</div>";
?>
