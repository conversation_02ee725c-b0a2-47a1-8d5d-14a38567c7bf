<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Balance API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Balance API Test</h1>
    <p>Testing the get_balance API endpoint to diagnose the undefined error.</p>
    
    <button onclick="testLogin()">1. Test Login</button>
    <button onclick="testBalance()" id="balanceBtn" disabled>2. Test Balance API</button>
    
    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        async function testLogin() {
            try {
                addResult('Testing login...', 'info');
                
                const response = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    addResult('✅ Login successful!', 'success');
                    addResult(`<pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                    document.getElementById('balanceBtn').disabled = false;
                } else {
                    addResult(`❌ Login failed: ${result.error || result.message}`, 'error');
                    addResult(`<pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                }
            } catch (error) {
                addResult(`❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testBalance() {
            try {
                addResult('Testing balance API...', 'info');
                
                const response = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_balance'
                    })
                });

                const result = await response.json();
                
                addResult('Raw API Response:', 'info');
                addResult(`<pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                
                if (result.success) {
                    addResult(`✅ Balance API successful!`, 'success');
                    addResult(`Balance: ${result.balance}`, 'success');
                    
                    // Test what happens in the frontend code
                    const userBalance = parseFloat(result.balance) || 0;
                    addResult(`Parsed balance: ${userBalance}`, 'success');
                    
                    if (userBalance === 30) {
                        addResult('🎯 Balance is correct! Should be 30 USDT (630 - 600)', 'success');
                    } else {
                        addResult(`⚠️ Balance might be incorrect. Expected: 30, Got: ${userBalance}`, 'error');
                    }
                } else {
                    addResult(`❌ Balance API failed: ${result.error || result.message}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Balance API error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
