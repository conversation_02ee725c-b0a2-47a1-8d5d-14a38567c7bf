<?php
/**
 * Payment Confirmation Service
 * Handles payment confirmation logic and database operations
 */

class PaymentConfirmationService {
    private $db;
    private $tronGridService;
    
    public function __construct() {
        $this->db = DatabaseConfig::getConnection();
        $this->tronGridService = new TronGridService();
    }
    
    /**
     * Confirm payment for a wallet
     */
    public function confirmPayment($walletId, $amount, $transactionHash = null) {
        try {
            // Start database transaction
            $this->db->beginTransaction();
            
            // 1. Get wallet information
            $wallet = $this->getWalletById($walletId);
            if (!$wallet) {
                $this->db->rollback();
                return [
                    'success' => false,
                    'code' => 404,
                    'message' => 'Wallet not found'
                ];
            }
            
            // 2. Check if transaction already confirmed (prevent double processing)
            if ($transactionHash && $this->isTransactionAlreadyProcessed($transactionHash)) {
                $this->db->rollback();
                return [
                    'success' => false,
                    'code' => 409,
                    'message' => 'Transaction already processed',
                    'transaction_hash' => $transactionHash
                ];
            }
            
            // 3. Verify transaction with TronGrid
            $verificationResult = $this->tronGridService->verifyTransaction(
                $wallet['address'], 
                $amount, 
                $transactionHash
            );
            
            if (!$verificationResult['success'] || !$verificationResult['verified']) {
                $this->db->rollback();
                return [
                    'success' => false,
                    'code' => 400,
                    'message' => $verificationResult['message'] ?? 'Transaction verification failed',
                    'verification_details' => $verificationResult
                ];
            }
            
            $transaction = $verificationResult['transaction'];
            
            // 4. Create transaction record
            $transactionId = $this->createTransactionRecord($wallet['user_id'], $transaction, $amount);
            
            // 5. Update user balance
            $this->updateUserBalance($wallet['user_id'], $amount);
            
            // 6. Update wallet statistics
            $this->updateWalletStats($walletId, $amount);
            
            // 7. Log the deposit
            $this->logDeposit($wallet['user_id'], $amount, $transaction['transaction_id'], 'confirmed');
            
            // Commit transaction
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'Payment confirmed successfully',
                'transaction_id' => $transactionId,
                'blockchain_tx' => $transaction['transaction_id'],
                'amount' => $amount,
                'wallet_address' => $wallet['address'],
                'confirmations' => $transaction['confirmations'] ?? 0,
                'verified_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Payment confirmation error: " . $e->getMessage());
            
            return [
                'success' => false,
                'code' => 500,
                'message' => 'Internal server error during payment confirmation',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get transaction status by hash
     */
    public function getTransactionStatus($transactionHash) {
        try {
            // Check database first
            $dbTransaction = $this->getTransactionByHash($transactionHash);
            
            if ($dbTransaction) {
                return [
                    'success' => true,
                    'found' => true,
                    'status' => $dbTransaction['status'],
                    'amount' => (float)$dbTransaction['amount'],
                    'user_id' => $dbTransaction['user_id'],
                    'created_at' => $dbTransaction['created_at'],
                    'source' => 'database'
                ];
            }
            
            // Check TronGrid if not in database
            $tronTransaction = $this->tronGridService->getTransactionByHash($transactionHash);
            
            if ($tronTransaction) {
                return [
                    'success' => true,
                    'found' => true,
                    'status' => 'pending',
                    'blockchain_status' => $tronTransaction['confirmed'] ? 'confirmed' : 'pending',
                    'amount' => isset($tronTransaction['value']) ? (float)$tronTransaction['value'] / 1000000 : 0,
                    'block_number' => $tronTransaction['block'] ?? null,
                    'confirmations' => $this->calculateConfirmations($tronTransaction),
                    'source' => 'blockchain'
                ];
            }
            
            return [
                'success' => true,
                'found' => false,
                'message' => 'Transaction not found in database or blockchain'
            ];
            
        } catch (Exception $e) {
            error_log("Transaction status check error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error checking transaction status: ' . $e->getMessage()
            ];
        }
    }
      /**
     * Get wallet by ID
     */
    private function getWalletById($walletId) {
        $stmt = $this->db->prepare("
            SELECT w.*, u.id as user_id 
            FROM wallets w 
            JOIN users u ON w.user_id = u.id 
            WHERE w.id = ?
        ");
        $stmt->execute([$walletId]);
        return $stmt->fetch();
    }
    
    /**
     * Check if transaction is already processed
     */
    private function isTransactionAlreadyProcessed($transactionHash) {
        $stmt = $this->db->prepare("
            SELECT id FROM transactions 
            WHERE transaction_hash = ? AND status IN ('confirmed', 'completed')
        ");
        $stmt->execute([$transactionHash]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Create transaction record in database
     */
    private function createTransactionRecord($userId, $transaction, $amount) {
        $stmt = $this->db->prepare("
            INSERT INTO transactions (
                user_id, 
                transaction_hash, 
                amount, 
                type, 
                status, 
                blockchain_data,
                created_at
            ) VALUES (
                ?, ?, ?, 'deposit', 'confirmed', ?, NOW()
            )
        ");
        
        $blockchainData = json_encode([
            'from_address' => $transaction['from_address'],
            'to_address' => $transaction['to_address'],
            'block_number' => $transaction['block_number'],
            'block_timestamp' => $transaction['block_timestamp'],
            'confirmations' => $transaction['confirmations'],
            'contract_address' => $transaction['contract_address']
        ]);
        
        $stmt->execute([
            $userId,
            $transaction['transaction_id'],
            $amount,
            $blockchainData
        ]);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * Update user balance
     */
    private function updateUserBalance($userId, $amount) {
        // Get current balance
        $stmt = $this->db->prepare("SELECT balance FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user) {
            throw new Exception("User not found for balance update");
        }
        
        $newBalance = (float)$user['balance'] + $amount;
          // Update balance
        $stmt = $this->db->prepare("UPDATE users SET balance = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$newBalance, $userId]);
        
        // Create balance history record (if table exists)
        try {
            $stmt = $this->db->prepare("
                INSERT INTO balance_history (
                    user_id, 
                    previous_balance, 
                    new_balance, 
                    amount, 
                    type, 
                    description,
                    created_at
                ) VALUES (?, ?, ?, ?, 'deposit', 'USDT Deposit Confirmed', NOW())
            ");
            
            $stmt->execute([
                $userId,
                $user['balance'],
                $newBalance,
                $amount
            ]);
        } catch (Exception $e) {
            // Balance history table may not exist, log but don't fail
            error_log("Balance history insert failed (table may not exist): " . $e->getMessage());
        }
    }
      /**
     * Update wallet statistics
     */
    private function updateWalletStats($walletId, $amount) {
        try {
            $stmt = $this->db->prepare("
                UPDATE wallets SET 
                    balance = balance + ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$amount, $walletId]);
        } catch (Exception $e) {
            error_log("Wallet stats update failed: " . $e->getMessage());
            // Don't fail the transaction for wallet stats
        }
    }
      /**
     * Log deposit activity
     */
    private function logDeposit($userId, $amount, $transactionHash, $status) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO deposit_logs (
                    user_id, 
                    amount, 
                    transaction_hash, 
                    status, 
                    payment_method,
                    notes,
                    created_at
                ) VALUES (?, ?, ?, ?, 'USDT', 'Auto-confirmed via TronGrid API', NOW())
            ");
            
            $stmt->execute([$userId, $amount, $transactionHash, $status]);
        } catch (Exception $e) {
            // Deposit logs table may not exist, log but don't fail
            error_log("Deposit log insert failed (table may not exist): " . $e->getMessage());
        }
    }
    
    /**
     * Get transaction by hash from database
     */
    private function getTransactionByHash($transactionHash) {
        $stmt = $this->db->prepare("
            SELECT * FROM transactions 
            WHERE transaction_hash = ? 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$transactionHash]);
        return $stmt->fetch();
    }
    
    /**
     * Calculate confirmations from blockchain transaction
     */
    private function calculateConfirmations($tx) {
        if (!isset($tx['block_timestamp']) || !isset($tx['confirmed']) || !$tx['confirmed']) {
            return 0;
        }
        
        // TRON blocks are approximately 3 seconds
        $blockTime = 3;
        $timeDiff = time() - ($tx['block_timestamp'] / 1000);
        
        return max(0, (int)($timeDiff / $blockTime));
    }
    
    /**
     * Batch confirm multiple payments (useful for cron jobs)
     */
    public function batchConfirmPayments($payments) {
        $results = [];
        
        foreach ($payments as $payment) {
            $walletId = $payment['wallet_id'] ?? null;
            $amount = $payment['amount'] ?? null;
            $transactionHash = $payment['transaction_hash'] ?? null;
            
            if (!$walletId || !$amount) {
                $results[] = [
                    'wallet_id' => $walletId,
                    'success' => false,
                    'message' => 'Missing required fields'
                ];
                continue;
            }
            
            $result = $this->confirmPayment($walletId, $amount, $transactionHash);
            $result['wallet_id'] = $walletId;
            $results[] = $result;
        }
        
        return [
            'success' => true,
            'processed' => count($results),
            'results' => $results
        ];
    }
    
    /**
     * Get pending deposits for a user
     */
    public function getPendingDeposits($userId, $limit = 10) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM transactions 
                WHERE user_id = ? 
                AND type = 'deposit' 
                AND status = 'pending'
                ORDER BY created_at DESC 
                LIMIT ?
            ");
            $stmt->execute([$userId, $limit]);
            
            return [
                'success' => true,
                'deposits' => $stmt->fetchAll()
            ];
            
        } catch (Exception $e) {
            error_log("Get pending deposits error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error retrieving pending deposits'
            ];
        }
    }
    
    /**
     * Auto-scan and confirm recent deposits for all active wallets
     * This method can be called by cron job to automatically process deposits
     */
    public function autoConfirmRecentDeposits($hoursBack = 1) {        try {
            // Get all active wallets
            $stmt = $this->db->prepare("
                SELECT w.*, u.id as user_id 
                FROM wallets w 
                JOIN users u ON w.user_id = u.id
            ");
            $stmt->execute();
            $wallets = $stmt->fetchAll();
            
            $confirmed = 0;
            $errors = [];
            
            foreach ($wallets as $wallet) {
                try {
                    // Get recent transactions from TronGrid for this wallet
                    $transactions = $this->tronGridService->getWalletTransactions($wallet['address'], 20);
                    
                    if (empty($transactions)) {
                        continue;
                    }
                    
                    // Process each transaction
                    foreach ($transactions as $tx) {
                        // Skip if already processed
                        if ($this->isTransactionAlreadyProcessed($tx['transaction_id'])) {
                            continue;
                        }
                        
                        // Skip if too old
                        $txTime = $tx['block_timestamp'] / 1000;
                        $cutoffTime = time() - ($hoursBack * 3600);
                        if ($txTime < $cutoffTime) {
                            continue;
                        }
                        
                        // Skip if not confirmed or not USDT
                        if (!$tx['confirmed'] || $tx['token_info']['symbol'] !== 'USDT') {
                            continue;
                        }
                        
                        $amount = (float)$tx['value'] / 1000000;
                        
                        // Confirm the payment
                        $result = $this->confirmPayment($wallet['id'], $amount, $tx['transaction_id']);
                        
                        if ($result['success']) {
                            $confirmed++;
                        } else {
                            $errors[] = [
                                'wallet_id' => $wallet['id'],
                                'transaction_hash' => $tx['transaction_id'],
                                'error' => $result['message']
                            ];
                        }
                    }
                    
                } catch (Exception $e) {
                    $errors[] = [
                        'wallet_id' => $wallet['id'],
                        'error' => $e->getMessage()
                    ];
                }
            }
            
            return [
                'success' => true,
                'confirmed_count' => $confirmed,
                'processed_wallets' => count($wallets),
                'errors' => $errors,
                'scan_period_hours' => $hoursBack
            ];
            
        } catch (Exception $e) {
            error_log("Auto-confirm deposits error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error during auto-confirmation: ' . $e->getMessage()
            ];
        }
    }
}
