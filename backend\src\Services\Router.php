<?php

namespace Simbi\Tls\Services;

class Router
{
    private array $routes = [];

    public function addRoute(string $method, string $path, callable $handler): void
    {
        $this->routes[] = [
            'method' => strtoupper($method),
            'path' => $path,
            'handler' => $handler
        ];
    }    
    
    public function handleRequest(): void
    {
        $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $requestMethod = $_SERVER['REQUEST_METHOD'];

        // Remove base path if it exists
        $requestUri = preg_replace('#^/tls#', '', $requestUri);

        // Handle query parameter routing as fallback
        if (isset($_GET['route'])) {
            $requestUri = '/api/' . $_GET['route'];
        }

        // Find matching route
        foreach ($this->routes as $route) {
            if ($route['method'] === $requestMethod) {
                $params = $this->matchRoute($route['path'], $requestUri);
                if ($params !== false) {
                    try {
                        $result = call_user_func($route['handler'], $params);
                        $this->sendResponse($result);
                        return;
                    } catch (\Exception $e) {
                        $this->sendResponse(['error' => 'Internal server error', 'code' => 500]);
                        return;
                    }
                }
            }
        }

        // No route found
        $this->sendResponse([
            'error' => 'Endpoint not found',
            'available_endpoints' => $this->getAvailableEndpoints(),
            'code' => 404
        ]);
    }

    private function sendResponse(array $data): void
    {
        $code = $data['code'] ?? 200;
        unset($data['code']); // Remove code from response body
        
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_PRETTY_PRINT);
        exit;
    }

    private function getAvailableEndpoints(): array
    {
        $endpoints = [];
        foreach ($this->routes as $route) {
            $endpoints[] = $route['method'] . ' ' . $route['path'];
        }
        return $endpoints;
    }

    public static function getRequestData(): array
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        if (strpos($contentType, 'application/json') !== false) {
            $json = file_get_contents('php://input');
            return json_decode($json, true) ?? [];
        }
        
        return $_POST;
    }

    private function matchRoute(string $routePath, string $requestUri): array|false
    {
        // Convert route pattern to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '(?P<$1>[^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        if (preg_match($pattern, $requestUri, $matches)) {
            // Extract named parameters
            $params = [];
            foreach ($matches as $key => $value) {
                if (!is_numeric($key)) {
                    $params[$key] = $value;
                }
            }
            return $params;
        }
        
        return false;
    }
}
