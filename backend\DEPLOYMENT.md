# TRON Wallet API - Production Deployment Guide

## 🚀 Production Deployment Checklist

### 1. Environment Setup

1. **Copy Environment Configuration**:
   ```powershell
   Copy-Item .env.example .env
   ```

2. **Update .env file with production values**:
   ```bash
   # Database - Use strong credentials
   DB_HOST=your-production-db-host
   DB_NAME=tlssc_production
   DB_USER=your-db-user
   DB_PASS=your-strong-db-password
   
   # JWT - Generate a strong secret (256+ bits)
   JWT_SECRET=your-super-secure-jwt-secret-key-at-least-256-bits-long
   
   # API Configuration
   API_ENV=production
   API_DEBUG=false
   
   # TRON Network - Use mainnet for production
   TRON_NETWORK=mainnet
   MASTER_ADDRESS=your-master-wallet-address
   MASTER_PRIVATE_KEY=your-master-wallet-private-key
   USDT_CONTRACT=your-mainnet-usdt-contract
   
   # Security
   CORS_ORIGINS=https://yourdomain.com
   RATE_LIMIT_ENABLED=true
   RATE_LIMIT_REQUESTS=60
   RATE_LIMIT_WINDOW=3600
   ```

### 2. Database Setup

1. **Create Production Database**:
   ```sql
   CREATE DATABASE tlssc_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'tlsapi'@'localhost' IDENTIFIED BY 'strong_password_here';
   GRANT SELECT, INSERT, UPDATE, DELETE ON tlssc_production.* TO 'tlsapi'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Initialize Tables**:
   ```powershell
   php setup_db.php
   ```

### 3. Web Server Configuration

#### Apache Configuration
Create `.htaccess` in document root:
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ src/index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
Header always set Content-Security-Policy "default-src 'self'"
```

#### Nginx Configuration
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /var/www/tron-wallet-api;
    index index.php;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    location /api/ {
        try_files $uri $uri/ /src/index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Block access to sensitive files
    location ~ /\. {
        deny all;
    }
    location ~ /(vendor|tests|database\.sql|\.env) {
        deny all;
    }
}
```

### 4. Security Hardening

#### File Permissions
```powershell
# Set appropriate permissions (Unix-like systems)
chmod 644 *.php
chmod 644 src/*.php
chmod 644 src/**/*.php
chmod 600 .env
chmod 755 vendor/
```

#### Additional Security Measures

1. **Rate Limiting**: Implement rate limiting in production
2. **Input Validation**: All inputs are already validated
3. **SQL Injection Protection**: Using PDO prepared statements
4. **XSS Protection**: JSON responses prevent XSS
5. **CSRF Protection**: Stateless JWT tokens prevent CSRF

### 5. Monitoring and Logging

#### Error Logging
Update `src/index.php` for production:
```php
// Production error handling
if (Config::get('API_ENV') === 'production') {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', '/var/log/tron-wallet-api/error.log');
}
```

#### Performance Monitoring
Consider implementing:
- Database query monitoring
- API response time tracking
- Failed authentication attempt logging
- Wallet operation auditing

### 6. SSL Certificate

#### Let's Encrypt (Free)
```bash
certbot --apache -d yourdomain.com
```

#### Or using Nginx
```bash
certbot --nginx -d yourdomain.com
```

### 7. Backup Strategy

#### Database Backup Script
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u tlsapi -p tlssc_production > /backups/tlssc_backup_$DATE.sql
find /backups -name "tlssc_backup_*.sql" -mtime +7 -delete
```

#### File Backup
```bash
tar -czf /backups/tron_wallet_api_$DATE.tar.gz /var/www/tron-wallet-api --exclude=vendor
```

### 8. Testing Production Setup

> **IMPORTANT:** Before deploying, verify that all test and development endpoints (such as `/api/test`) have been removed from the codebase.

1. **Run Health Check**:
   ```bash
   curl -X GET https://yourdomain.com/api/admin/health
   ```

2. **Test Authentication Flow**:
   ```bash
   # Register
   curl -X POST https://yourdomain.com/api/register \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"testpass123"}'
   
   # Login
   curl -X POST https://yourdomain.com/api/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"testpass123"}'
   ```

### 9. Performance Optimization

#### PHP-FPM Configuration
```ini
# /etc/php/8.2/fpm/pool.d/www.conf
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

#### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_wallets_created_at ON wallets(created_at);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_users_created_at ON users(created_at);
```

### 10. Maintenance Tasks

#### Daily Tasks
- Monitor error logs
- Check API response times
- Verify database connections

#### Weekly Tasks
- Database backup verification
- Security log review
- Performance metrics analysis

#### Monthly Tasks
- Update dependencies
- Security audit
- Capacity planning review

## 🔒 Security Best Practices

1. **Never commit .env files**
2. **Use strong JWT secrets (256+ bits)**
3. **Implement rate limiting**
4. **Monitor failed authentication attempts**
5. **Use HTTPS in production**
6. **Regularly update dependencies**
7. **Implement proper logging**
8. **Use database user with minimal privileges**

## 📊 API Monitoring

### Health Check Endpoint
Monitor `/api/admin/health` for:
- Response time < 200ms
- HTTP 200 status code
- Valid JSON response

### Key Metrics to Track
- Authentication success/failure rates
- Wallet creation rates
- Transaction success rates
- API response times
- Error rates by endpoint

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials in `.env`
   - Verify database server is running
   - Check network connectivity

2. **JWT Token Invalid**
   - Verify JWT_SECRET in `.env`
   - Check token expiration
   - Validate token format

3. **TRON API Errors**
   - Check TRON network status
   - Verify API key validity
   - Check network configuration

### Debug Mode
Enable debug mode in `.env`:
```bash
API_DEBUG=true
```

## 📞 Support

For production support issues:
1. Check error logs first
2. Verify environment configuration
3. Test with known good credentials
4. Check database connectivity
5. Validate TRON network status

---

**⚠️ Important**: Always test thoroughly in a staging environment before deploying to production!
