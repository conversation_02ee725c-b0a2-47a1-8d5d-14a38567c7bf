<?php
require_once 'vendor/autoload.php';
use Simbi\Tls\Config\Database;

try {
    $pdo = Database::getConnection();
      echo "=== INVESTMENT PLANS TABLE STRUCTURE ===\n";
    $stmt = $pdo->query('DESCRIBE investment_plans');
    while ($row = $stmt->fetch()) {
        echo "Column: {$row['Field']} ({$row['Type']})\n";
    }
    
    echo "\n=== INVESTMENT PLANS DATA ===\n";
    $stmt = $pdo->query('SELECT * FROM investment_plans ORDER BY min_amount ASC');
    
    while ($row = $stmt->fetch()) {
        echo "Raw data: " . json_encode($row) . "\n";
        echo "---\n";
    }
      echo "\n=== ALL TABLES IN DATABASE ===\n";
    $stmt = $pdo->query('SHOW TABLES');
    while ($row = $stmt->fetch()) {
        echo "Table: " . array_values($row)[0] . "\n";
    }
    $stmt = $pdo->query('DESCRIBE users');
    echo "Users table structure:\n";
    while ($row = $stmt->fetch()) {
        echo "Column: {$row['Field']} ({$row['Type']})\n";
    }
    
    echo "\nUser data:\n";
    $stmt = $pdo->prepare('SELECT * FROM users WHERE email = ?');
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "Raw user data: " . json_encode($user) . "\n";
    } else {
        echo "Test user not found\n";
    }
    
    echo "\n=== WALLETS TABLE ===\n";
    $stmt = $pdo->query('DESCRIBE wallets');
    echo "Wallets table structure:\n";
    while ($row = $stmt->fetch()) {
        echo "Column: {$row['Field']} ({$row['Type']})\n";
    }
    
    echo "\nTest user wallets:\n";
    $stmt = $pdo->prepare('SELECT * FROM wallets WHERE user_id = ?');
    $stmt->execute([2]); // Test user ID
    $wallets = $stmt->fetchAll();
    
    if ($wallets) {
        foreach ($wallets as $wallet) {
            echo "Wallet: " . json_encode($wallet) . "\n";
        }
    } else {
        echo "No wallets found for test user\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
