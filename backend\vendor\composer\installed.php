<?php return array(
    'root' => array(
        'name' => 'simbi/tls',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'c20e89fe7ab210d54d3b7c2103e4118eb7d3d19a',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'comely-io/data-types' => array(
            'pretty_version' => '1.0.34',
            'version' => '1.0.34.0',
            'reference' => '833ecf364a99aa5cd161e0ebd1191860921834c6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../comely-io/data-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fgrosse/phpasn1' => array(
            'pretty_version' => 'v2.5.0',
            'version' => '2.5.0.0',
            'reference' => '42060ed45344789fb9f21f9f1864fc47b9e3507b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fgrosse/phpasn1',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'iexbase/tron-api' => array(
            'pretty_version' => 'v5.0',
            'version' => '5.0.0.0',
            'reference' => '45b01d8a562a2445c307df3b2f233473d970755b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../iexbase/tron-api',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'iexbase/web3.php' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'f25ed954a7586ead86046dd7e02a333a8098511b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../iexbase/web3.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kornrunner/keccak' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '433749d28e117fb97baf9f2631b92b5d9ab3c890',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kornrunner/keccak',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kornrunner/secp256k1' => array(
            'pretty_version' => '0.2.0',
            'version' => '0.2.0.0',
            'reference' => 'c3990dba47c7a8b0c9fd858fb29c61a5794fbb39',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kornrunner/secp256k1',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mdanter/ecc' => array(
            'pretty_version' => 'v1.0.0',
            'version' => '1.0.0.0',
            'reference' => '34e2eec096bf3dcda814e8f66dd91ae87a2db7cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mdanter/ecc',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '2.0.48',
            'version' => '2.0.48.0',
            'reference' => 'eaa7be704b8b93a6913b69eb7f645a59d7731b61',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'simbi/tls' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'c20e89fe7ab210d54d3b7c2103e4118eb7d3d19a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'simplito/bigint-wrapper-php' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'cf21ec76d33f103add487b3eadbd9f5033a25930',
            'type' => 'library',
            'install_path' => __DIR__ . '/../simplito/bigint-wrapper-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'simplito/bn-php' => array(
            'pretty_version' => '1.1.4',
            'version' => '*******',
            'reference' => '83446756a81720eacc2ffb87ff97958431451fd6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../simplito/bn-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'simplito/elliptic-php' => array(
            'pretty_version' => '1.0.12',
            'version' => '********',
            'reference' => 'be321666781be2be2c89c79c43ffcac834bc8868',
            'type' => 'library',
            'install_path' => __DIR__ . '/../simplito/elliptic-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '********',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
