# Production Cleanup Script for TLS Wallet
# Removes development/testing files and prepares for production deployment
# Date: June 15, 2025

Write-Host "Starting Production Cleanup for TLS Wallet..." -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Yellow

# Define files to remove
$filesToRemove = @(
    "test_*.php", "test_*.html", "debug_*.php", "debug_*.html",
    "validate_*.php", "verify_*.php", "verify_*.html",
    "*_test.html", "*_test.php", "*_debug.html", "*_diagnostic.html",
    "*_COMPLETE.md", "*_RESOLVED.md", "*_FIX_COMPLETE.md",
    "*_IMPLEMENTATION_COMPLETE.md", "*_ENHANCEMENT_COMPLETE.md",
    "*_STYLING_COMPLETE.md", "*_CHECKLIST.md", "*_STATUS.md",
    "complete_test_summary.html", "dashboard_element_check.html",
    "enhanced_*.html", "final_*.html", "quick_*.html",
    "modal_test.html", "balance_deduction_diagnosis.html",
    "cleanup-production.ps1", "production-cleanup.ps1"
)

# Create backup directory
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$backupDir = "dev_files_backup_$timestamp"

Write-Host "Creating backup directory: $backupDir" -ForegroundColor Cyan
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

$removedCount = 0

# Remove files
Write-Host "Removing development and test files..." -ForegroundColor Yellow
foreach ($pattern in $filesToRemove) {
    $files = Get-ChildItem -Path . -Name $pattern -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        if (Test-Path $file) {
            Write-Host "Removing: $file" -ForegroundColor Red
            
            # Backup file before deletion
            Copy-Item $file $backupDir -ErrorAction SilentlyContinue
            Remove-Item $file -Force -ErrorAction SilentlyContinue
            $removedCount++
        }
    }
}

# Clean up temporary files
Write-Host "Cleaning temporary files..." -ForegroundColor Yellow
$tempFiles = @("*.tmp", "*.temp", "*~", ".DS_Store", "Thumbs.db")
foreach ($pattern in $tempFiles) {
    $files = Get-ChildItem -Path . -Name $pattern -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        if (Test-Path $file) {
            Write-Host "Removing temp file: $file" -ForegroundColor Red
            Remove-Item $file -Force -ErrorAction SilentlyContinue
            $removedCount++
        }
    }
}

# Clean frontend backup files
if (Test-Path "frontend") {
    Write-Host "Cleaning frontend backup files..." -ForegroundColor Yellow
    
    if (Test-Path "frontend\css") {
        Get-ChildItem -Path "frontend\css" -Name "*.bak" -ErrorAction SilentlyContinue | ForEach-Object {
            Write-Host "Removing CSS backup: $_" -ForegroundColor Red
            Remove-Item "frontend\css\$_" -Force -ErrorAction SilentlyContinue
            $removedCount++
        }
    }
    
    if (Test-Path "frontend\js") {
        Get-ChildItem -Path "frontend\js" -Name "*.bak" -ErrorAction SilentlyContinue | ForEach-Object {
            Write-Host "Removing JS backup: $_" -ForegroundColor Red
            Remove-Item "frontend\js\$_" -Force -ErrorAction SilentlyContinue
            $removedCount++
        }
    }
}

# Clean backend test files
if (Test-Path "backend") {
    Write-Host "Cleaning backend test files..." -ForegroundColor Yellow
    Get-ChildItem -Path "backend" -Name "test_*" -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "Removing backend test: $_" -ForegroundColor Red
        Remove-Item "backend\$_" -Force -ErrorAction SilentlyContinue
        $removedCount++
    }
}

# Create production summary
Write-Host "Creating production summary..." -ForegroundColor Yellow

$summaryContent = @"
# TLS Wallet - Production Ready
Date: $timestamp

## Cleanup Summary:
- Files removed: $removedCount
- Backup location: $backupDir

## Production Structure:
- frontend/     - User interface
- backend/      - API and business logic  
- cronJob/      - Scheduled tasks

## Deployment Checklist:
1. Update database connection strings
2. Set production environment variables
3. Configure web server settings
4. Set up SSL certificates
5. Test all functionality

Generated by Production Cleanup Script
"@

$summaryContent | Out-File -FilePath "PRODUCTION_READY_$timestamp.md" -Encoding UTF8

# Final summary
Write-Host ""
Write-Host "Production Cleanup Complete!" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Yellow
Write-Host "Files removed: $removedCount" -ForegroundColor Green
Write-Host "Backup created: $backupDir" -ForegroundColor Cyan
Write-Host "Summary file: PRODUCTION_READY_$timestamp.md" -ForegroundColor Cyan

Write-Host ""
Write-Host "Production File Structure:" -ForegroundColor Yellow
Get-ChildItem -Path . -Directory | Select-Object Name, LastWriteTime | Format-Table -AutoSize

Write-Host "Ready for production deployment!" -ForegroundColor Green
