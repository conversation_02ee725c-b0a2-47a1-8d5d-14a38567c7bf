<?php

namespace Simbi\Tls\Controllers;

use Simbi\Tls\Services\TransactionService;

class TransactionController
{
    private TransactionService $transactionService;

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    public function getHistory(array $user, array $filters = []): array
    {
        $userId = $user['id'];
        return $this->transactionService->getUserTransactionHistory($userId, $filters);
    }

    public function getTransactionDetails(int $transactionId, array $user): array
    {
        $userId = $user['id'];
        return $this->transactionService->getTransactionDetails($transactionId, $userId);
    }

    public function recordDeposit(array $data, array $user): array
    {
        $userId = $user['id'];
        $walletAddress = $data['wallet_address'] ?? null;
        $txHash = $data['transaction_hash'] ?? null;
        $amount = $data['amount'] ?? null;
        $fromAddress = $data['from_address'] ?? null;

        if (!$walletAddress || !$txHash || !$amount) {
            return ['error' => 'Wallet address, transaction hash, and amount are required', 'code' => 400];
        }

        if (!is_numeric($amount) || $amount <= 0) {
            return ['error' => 'Amount must be a positive number', 'code' => 400];
        }

        return $this->transactionService->recordDeposit(
            $userId,
            $walletAddress,
            $txHash,
            (float)$amount,
            $fromAddress
        );
    }

    public function getWalletTransactions(int $walletId, array $user, array $params = []): array
    {
        $userId = $user['id'];
        $limit = min(max($params['limit'] ?? 50, 1), 100);
        $offset = max($params['offset'] ?? 0, 0);

        return $this->transactionService->getTransactionsByWallet($walletId, $userId, $limit, $offset);
    }

    public function getStatistics(array $user): array
    {
        $userId = $user['id'];
        $stats = $this->transactionService->getUserTransactionHistory($userId, ['limit' => 1]);
        
        return [
            'success' => true,
            'statistics' => $stats['statistics'] ?? []
        ];
    }
}
