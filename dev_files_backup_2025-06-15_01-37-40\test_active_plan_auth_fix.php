<?php
// Test active plan authentication fix
require_once 'frontend/config.php';

header('Content-Type: text/plain');

echo "Active Plan Authentication Fix Test\n";
echo "==================================\n\n";

// Test 1: Check FrontendConfig authentication methods
echo "Test 1: FrontendConfig Authentication Methods\n";
echo "---------------------------------------------\n";

FrontendConfig::initSession();

echo "Session initialized: " . (session_status() === PHP_SESSION_ACTIVE ? "✅ YES" : "❌ NO") . "\n";
echo "Session ID: " . session_id() . "\n";

// Test isAuthenticated method without login
echo "isAuthenticated() before login: " . (FrontendConfig::isAuthenticated() ? "✅ YES" : "❌ NO") . "\n";

// Test 2: Simulate login and session setup
echo "\nTest 2: Simulate Login Session Setup\n";
echo "------------------------------------\n";

// Manually set session variables like ajax.php does after successful login
$_SESSION['user_id'] = 2;
$_SESSION['email'] = '<EMAIL>';
$_SESSION['token'] = 'test_token_123';
$_SESSION['is_admin'] = false;

echo "Session variables set:\n";
echo "- user_id: " . ($_SESSION['user_id'] ?? 'NOT SET') . "\n";
echo "- email: " . ($_SESSION['email'] ?? 'NOT SET') . "\n";
echo "- token: " . (isset($_SESSION['token']) ? 'SET' : 'NOT SET') . "\n";
echo "- is_admin: " . ($_SESSION['is_admin'] ?? 'NOT SET') . "\n";

// Test authentication after session setup
echo "\nAuthentication check after session setup:\n";
echo "isAuthenticated(): " . (FrontendConfig::isAuthenticated() ? "✅ YES" : "❌ NO") . "\n";
echo "isAdmin(): " . (FrontendConfig::isAdmin() ? "✅ YES" : "❌ NO") . "\n";

$currentUser = FrontendConfig::getCurrentUser();
if ($currentUser) {
    echo "getCurrentUser(): ✅ SUCCESS\n";
    echo "- ID: " . $currentUser['id'] . "\n";
    echo "- Email: " . $currentUser['email'] . "\n";
} else {
    echo "getCurrentUser(): ❌ FAILED\n";
}

// Test 3: Simulate active_plan.php authentication flow
echo "\nTest 3: Active Plan Page Authentication Flow\n";
echo "-------------------------------------------\n";

// Test URL parameter
$_GET['id'] = '1';
$investmentId = $_GET['id'] ?? null;

echo "Investment ID from URL: " . ($investmentId ?: 'NOT PROVIDED') . "\n";

// Test authentication check like active_plan.php does
if (!FrontendConfig::isAuthenticated()) {
    echo "Authentication result: ❌ WOULD REDIRECT TO LOGIN\n";
    echo "Reason: FrontendConfig::isAuthenticated() returned false\n";
} else {
    echo "Authentication result: ✅ WOULD ALLOW ACCESS\n";
    
    if (!$investmentId) {
        echo "Investment ID check: ❌ WOULD REDIRECT TO DASHBOARD\n";
        echo "Reason: No investment ID provided\n";
    } else {
        echo "Investment ID check: ✅ WOULD SHOW ACTIVE PLAN PAGE\n";
        echo "Page would display investment details for ID: $investmentId\n";
    }
}

// Test 4: Test AJAX authentication pattern
echo "\nTest 4: AJAX Authentication Check\n";
echo "---------------------------------\n";

if (!isset($_SESSION['token'])) {
    echo "AJAX auth check: ❌ WOULD RETURN 'Not authenticated'\n";
    echo "Reason: \$_SESSION['token'] not set\n";
} else {
    echo "AJAX auth check: ✅ WOULD ALLOW API CALLS\n";
    echo "Token present: " . $_SESSION['token'] . "\n";
}

// Test 5: Summary
echo "\nTest 5: Summary and Diagnosis\n";
echo "-----------------------------\n";

$sessionOk = session_status() === PHP_SESSION_ACTIVE;
$authMethodOk = FrontendConfig::isAuthenticated();
$sessionVarsOk = isset($_SESSION['user_id']) && isset($_SESSION['token']);
$pageAccessOk = FrontendConfig::isAuthenticated() && !empty($investmentId);
$ajaxOk = isset($_SESSION['token']);

echo "Session Status: " . ($sessionOk ? "✅" : "❌") . "\n";
echo "Auth Method: " . ($authMethodOk ? "✅" : "❌") . "\n"; 
echo "Session Variables: " . ($sessionVarsOk ? "✅" : "❌") . "\n";
echo "Page Access: " . ($pageAccessOk ? "✅" : "❌") . "\n";
echo "AJAX Ready: " . ($ajaxOk ? "✅" : "❌") . "\n";

echo "\n";

if ($sessionOk && $authMethodOk && $sessionVarsOk && $pageAccessOk && $ajaxOk) {
    echo "🎉 RESULT: AUTHENTICATION FIX SUCCESSFUL!\n";
    echo "The active plan page should now work correctly when users:\n";
    echo "1. Login through the frontend\n";
    echo "2. Navigate to active_plan.php?id=X\n";
    echo "3. Use the JavaScript APIs to load investment data\n";
} else {
    echo "⚠️  RESULT: ISSUES DETECTED\n";
    echo "Problems found:\n";
    if (!$sessionOk) echo "- Session not properly initialized\n";
    if (!$authMethodOk) echo "- FrontendConfig::isAuthenticated() failing\n";
    if (!$sessionVarsOk) echo "- Required session variables missing\n";
    if (!$pageAccessOk) echo "- Page access would be denied\n";
    if (!$ajaxOk) echo "- AJAX calls would fail authentication\n";
}

echo "\n📝 Next Steps:\n";
echo "1. Test the actual page: http://localhost:8080/user/active_plan.php?id=1\n";
echo "2. Test the diagnostic tool: http://localhost:8080/active_plan_auth_diagnostic.html\n";
echo "3. Test navigation from dashboard investment items\n";
?>
