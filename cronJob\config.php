<?php
/**
 * Configuration for Payment Confirmation API
 * Uses existing database configuration from backend
 */

// Load environment variables from backend
$envFile = __DIR__ . '/../backend/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

/**
 * Database Configuration
 */
class DatabaseConfig {
    private static $connection = null;
    
    public static function getConnection() {
        if (self::$connection === null) {
            try {
                $host = $_ENV['DB_HOST'] ?? 'localhost';
                $dbname = $_ENV['DB_NAME'] ?? 'tlssc';
                $username = $_ENV['DB_USER'] ?? 'root';
                $password = $_ENV['DB_PASS'] ?? '';
                
                $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ];
                
                self::$connection = new PDO($dsn, $username, $password, $options);
            } catch (PDOException $e) {
                error_log("Database connection failed: " . $e->getMessage());
                throw new Exception("Database connection failed");
            }
        }
        
        return self::$connection;
    }
}

/**
 * Application Configuration
 */
define('TRON_NETWORK', $_ENV['TRON_NETWORK'] ?? 'nile');

// Network-specific contract selection
$network = $_ENV['TRON_NETWORK'] ?? 'nile';
$networkContract = match($network) {
    'mainnet' => $_ENV['USDT_CONTRACT_MAINNET'] ?? 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
    'nile' => $_ENV['USDT_CONTRACT_NILE'] ?? 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf',
    'shasta' => $_ENV['USDT_CONTRACT_SHASTA'] ?? 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs',
    default => $_ENV['USDT_CONTRACT_NILE'] ?? 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'
};

define('USDT_CONTRACT', $_ENV['USDT_CONTRACT'] ?? $networkContract);
define('TRON_GRID_API_KEY', $_ENV['TRON_GRID_API_KEY'] ?? '');

// TronGrid API configuration - network-specific URLs
$baseUrl = match($network) {
    'mainnet' => $_ENV['TRON_MAINNET_URL'] ?? 'https://api.trongrid.io',
    'nile' => $_ENV['TRON_NILE_URL'] ?? 'https://nile.trongrid.io',
    'shasta' => $_ENV['TRON_SHASTA_URL'] ?? 'https://api.shasta.trongrid.io',
    default => $_ENV['TRON_BASE_URL'] ?? 'https://nile.trongrid.io'
};

define('TRONGRID_BASE_URL', $baseUrl);
define('TRONGRID_TIMEOUT', 30);

// Payment confirmation settings
define('TRANSACTION_TIMEOUT_HOURS', 24); // Look for transactions within last 24 hours
define('AMOUNT_TOLERANCE', 0.01); // Allow 0.01 USDT tolerance for amount matching
define('MIN_CONFIRMATIONS', 19); // Minimum confirmations required
