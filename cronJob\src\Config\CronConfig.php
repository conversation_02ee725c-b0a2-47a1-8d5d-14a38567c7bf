<?php

declare(strict_types=1);

namespace Simbi\Tls\CronJob\Config;

use PDO;
use PDOException;

/**
 * CronJob Configuration Class
 * Handles configuration settings for cron jobs and scheduled tasks
 */
class CronConfig
{
    private static ?PDO $connection = null;
    private static array $config = [];
    private static bool $loaded = false;
    
    /**
     * Load configuration from environment or config files
     */
    public static function load(): void
    {
        if (self::$loaded) {
            return;
        }
        
        // Load from .env file if it exists
        $envFile = __DIR__ . '/../../.env';
        if (file_exists($envFile)) {
            self::loadFromEnvFile($envFile);
        }
        
        // Set default configuration values
        self::setDefaults();
        
        self::$loaded = true;
    }
    
    /**
     * Get configuration value
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        self::load();
        return self::$config[$key] ?? $default;
    }
    
    /**
     * Set configuration value
     */
    public static function set(string $key, mixed $value): void
    {
        self::$config[$key] = $value;
    }
    
    /**
     * Get database connection for cron jobs
     */
    public static function getDatabaseConnection(): PDO
    {
        if (self::$connection === null) {
            self::load();
            
            $host = self::get('DB_HOST', 'localhost');
            $dbname = self::get('DB_NAME', 'tls_wallet');
            $username = self::get('DB_USER', 'root');
            $password = self::get('DB_PASS', '');
            $charset = 'utf8mb4';
            
            $dsn = "mysql:host={$host};dbname={$dbname};charset={$charset}";
            
            try {
                self::$connection = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$charset}"
                ]);
            } catch (PDOException $e) {
                throw new \Exception('Database connection failed: ' . $e->getMessage());
            }
        }
        
        return self::$connection;
    }
    
    /**
     * Load configuration from .env file
     */
    private static function loadFromEnvFile(string $filePath): void
    {
        $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue; // Skip comments
            }
            
            if (strpos($line, '=') !== false) {
                [$key, $value] = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if (($value[0] === '"' && $value[-1] === '"') || 
                    ($value[0] === "'" && $value[-1] === "'")) {
                    $value = substr($value, 1, -1);
                }
                
                self::$config[$key] = $value;
            }
        }
    }
    
    /**
     * Set default configuration values
     */
    private static function setDefaults(): void
    {
        $defaults = [
            // Database Configuration
            'DB_HOST' => 'localhost',
            'DB_NAME' => 'tls_wallet',
            'DB_USER' => 'root',
            'DB_PASS' => '',
            
            // TRON Configuration
            'TRONGRID_BASE_URL' => 'https://api.trongrid.io',
            'TRON_GRID_API_KEY' => '',
            'USDT_CONTRACT' => 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
            'TRON_NETWORK' => 'mainnet',
            
            // Cron Job Configuration
            'CRON_LOG_LEVEL' => 'info',
            'CRON_MAX_EXECUTION_TIME' => 300, // 5 minutes
            'CRON_MEMORY_LIMIT' => '256M',
            'CRON_TIMEZONE' => 'UTC',
            
            // Payment Confirmation Settings
            'PAYMENT_CONFIRMATION_INTERVAL' => 300, // 5 minutes
            'PAYMENT_MAX_AGE_DAYS' => 7,
            'PAYMENT_BATCH_SIZE' => 100,
            'PAYMENT_API_DELAY_MS' => 100,
            
            // Logging Configuration
            'LOG_PATH' => __DIR__ . '/../../logs/',
            'LOG_MAX_FILES' => 30,
            'LOG_MAX_SIZE' => '10M',
            
            // Security Configuration
            'API_TIMEOUT' => 30,
            'MAX_RETRY_ATTEMPTS' => 3,
            'RETRY_DELAY_SECONDS' => 5,
            
            // Investment Processing
            'INVESTMENT_PROCESSING_ENABLED' => true,
            'INVESTMENT_PROCESSING_INTERVAL' => 3600, // 1 hour
            'INVESTMENT_DAILY_PROCESSING_HOUR' => 9, // 9 AM UTC
        ];
        
        foreach ($defaults as $key => $value) {
            if (!isset(self::$config[$key])) {
                self::$config[$key] = $value;
            }
        }
    }
    
    /**
     * Get all configuration values
     */
    public static function all(): array
    {
        self::load();
        return self::$config;
    }
    
    /**
     * Check if configuration is loaded
     */
    public static function isLoaded(): bool
    {
        return self::$loaded;
    }
    
    /**
     * Reset configuration (useful for testing)
     */
    public static function reset(): void
    {
        self::$config = [];
        self::$loaded = false;
        self::$connection = null;
    }
    
    /**
     * Get log file path for cron jobs
     */
    public static function getLogPath(string $logType = 'general'): string
    {
        $logDir = self::get('LOG_PATH');
        
        // Ensure log directory exists
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $date = date('Y-m-d');
        return $logDir . "cron_{$logType}_{$date}.log";
    }
    
    /**
     * Log message to cron log file
     */
    public static function log(string $message, string $level = 'info', string $logType = 'general'): void
    {
        $logFile = self::getLogPath($logType);
        $timestamp = date('Y-m-d H:i:s');
        $logLevel = strtoupper($level);
        
        $logMessage = "[{$timestamp}] [{$logLevel}] {$message}" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Get environment type
     */
    public static function getEnvironment(): string
    {
        return self::get('ENVIRONMENT', 'development');
    }
    
    /**
     * Check if running in production
     */
    public static function isProduction(): bool
    {
        return self::getEnvironment() === 'production';
    }
    
    /**
     * Check if debugging is enabled
     */
    public static function isDebugEnabled(): bool
    {
        return self::get('DEBUG', !self::isProduction());
    }
}
