<?php
/**
 * Test Backend API Directly
 * This bypasses the frontend and tests the backend API directly
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔧 Testing Backend API Directly\n";
echo "===============================\n\n";

// Test configuration
$backendUrl = 'http://localhost/tls/backend/src/index.php';

// You need to get a valid token first
// Replace this with a real token from your session
$token = '';

if (empty($token)) {
    echo "❌ No token provided. Please:\n";
    echo "1. Login to the frontend\n";
    echo "2. Check your browser's developer tools\n";
    echo "3. Find the token in localStorage or sessionStorage\n";
    echo "4. Update this script with the token\n\n";
    
    // Try to get token from session if available
    session_start();
    if (isset($_SESSION['token'])) {
        $token = $_SESSION['token'];
        echo "✅ Found token in session: " . substr($token, 0, 20) . "...\n\n";
    } else {
        echo "❌ No session token found either.\n";
        exit(1);
    }
}

// Test 1: Test authentication endpoint
echo "1. Testing Authentication...\n";
echo "----------------------------\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $backendUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "HTTP Code: {$httpCode}\n";
if ($curlError) {
    echo "cURL Error: {$curlError}\n";
}
echo "Raw Response:\n";
echo $response . "\n\n";

// Try to parse JSON
$jsonData = json_decode($response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Valid JSON Response\n";
    echo "Parsed Data:\n";
    print_r($jsonData);
} else {
    echo "❌ Invalid JSON Response\n";
    echo "JSON Error: " . json_last_error_msg() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n\n";

// Test 2: Test sweep-funds endpoint specifically
echo "2. Testing Sweep Funds Endpoint...\n";
echo "-----------------------------------\n";

$sweepUrl = $backendUrl . '/api/sweep-funds';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $sweepUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "Sweep URL: {$sweepUrl}\n";
echo "HTTP Code: {$httpCode}\n";
if ($curlError) {
    echo "cURL Error: {$curlError}\n";
}
echo "Raw Response:\n";
echo $response . "\n\n";

// Try to parse JSON
$jsonData = json_decode($response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Valid JSON Response\n";
    echo "Parsed Data:\n";
    print_r($jsonData);
} else {
    echo "❌ Invalid JSON Response\n";
    echo "JSON Error: " . json_last_error_msg() . "\n";
    
    // Check for common issues
    if (strpos($response, 'Fatal error') !== false) {
        echo "⚠️ PHP Fatal Error detected\n";
    }
    if (strpos($response, 'Warning:') !== false) {
        echo "⚠️ PHP Warning detected\n";
    }
    if (strpos($response, 'Notice:') !== false) {
        echo "⚠️ PHP Notice detected\n";
    }
    if (strpos($response, '<?php') !== false) {
        echo "⚠️ PHP code in response\n";
    }
    if (empty(trim($response))) {
        echo "⚠️ Empty response\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n\n";

// Test 3: Check if backend is accessible at all
echo "3. Testing Backend Accessibility...\n";
echo "-----------------------------------\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $backendUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "Backend URL: {$backendUrl}\n";
echo "HTTP Code: {$httpCode}\n";
if ($curlError) {
    echo "cURL Error: {$curlError}\n";
} else {
    echo "✅ Backend is accessible\n";
}

echo "\n🏁 Test complete!\n";

// Instructions
echo "\n📋 Next Steps:\n";
echo "1. If you see 'Invalid JSON Response', check the raw response for PHP errors\n";
echo "2. If HTTP code is 404, check if the backend URL is correct\n";
echo "3. If HTTP code is 500, check PHP error logs\n";
echo "4. If authentication fails, get a fresh token from the frontend\n";
?>
