<!DOCTYPE html>
<html>
<head>
    <title>Investment Plans Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Investment Plans Styling Debug</h1>
    
    <div class="debug-section">
        <h2>1. Test Investment Plans API</h2>
        <button onclick="testPlansAPI()">Test API Call</button>
        <div id="apiResult"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. Test Plan Rendering</h2>
        <button onclick="testPlanRendering()">Render Test Plans</button>
        <div id="planRenderResult"></div>
    </div>
    
    <div class="debug-section">
        <h2>3. CSS Test</h2>
        <link rel="stylesheet" href="frontend/css/dashboard.css">
        <div class="investment-plans-section">
            <h3>Choose Investment Plan</h3>
            <div class="investment-plans">
                <div class="plan-card" data-plan="basic">
                    <div class="plan-header">
                        <div class="plan-name">Basic Plan</div>
                        <div class="plan-badge">Most Popular</div>
                        <div class="plan-rate">1.7%</div>
                        <div class="plan-period">Daily</div>
                        <div class="plan-duration">30 Days</div>
                        <div class="plan-minimum-header">Min: 600 USDT</div>
                    </div>
                    <div class="plan-details">
                        <div class="plan-features">
                            <li>✅ 1.67% daily simple interest</li>
                            <li>✅ Non-compounding returns</li>
                            <li>✅ 30-day investment duration</li>
                            <li>✅ Low risk profile</li>
                            <li>✅ Perfect for beginners</li>
                        </div>
                    </div>
                </div>
                
                <div class="plan-card disabled" data-plan="premium">
                    <div class="plan-header">
                        <div class="plan-name">Premium Plan</div>
                        <div class="plan-badge coming-soon">Coming Soon</div>
                        <div class="plan-rate">2.5%</div>
                        <div class="plan-period">Daily</div>
                        <div class="plan-duration">25 Days</div>
                        <div class="plan-minimum-header">Min: 2000 USDT</div>
                    </div>
                    <div class="plan-details">
                        <div class="plan-features">
                            <li>✅ 2.5% daily returns</li>
                            <li>✅ Shorter duration</li>
                            <li>✅ Higher minimum investment</li>
                        </div>
                    </div>
                    <div class="plan-overlay"><span>Coming Soon</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testPlansAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p>Testing API...</p>';
            
            try {
                const response = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_investment_plans'
                    })
                });
                
                const data = await response.json();
                resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                resultDiv.innerHTML = '<p class="error">Error: ' + error.message + '</p>';
            }
        }
        
        async function testPlanRendering() {
            const resultDiv = document.getElementById('planRenderResult');
            resultDiv.innerHTML = '<p>Rendering test plans...</p>';
            
            // Simulate the rendering function
            const mockPlans = [
                {
                    plan_code: 'basic',
                    plan_name: 'Basic Plan',
                    daily_rate: 0.0167,
                    duration: 30,
                    min_amount: 600,
                    features: ['1.67% daily simple interest', 'Non-compounding returns', '30-day investment duration'],
                    is_active: true,
                    is_featured: true
                },
                {
                    plan_code: 'premium',
                    plan_name: 'Premium Plan',
                    daily_rate: 0.025,
                    duration: 25,
                    min_amount: 2000,
                    features: ['2.5% daily returns', 'Shorter duration', 'Higher minimum investment'],
                    is_active: false,
                    is_featured: false
                }
            ];
            
            let html = '<div class="investment-plans">';
            
            mockPlans.forEach(plan => {
                const isBasic = plan.plan_code === 'basic';
                const isDisabled = !plan.is_active || !isBasic;
                
                html += `
                    <div class="plan-card ${isDisabled ? 'disabled' : ''}" data-plan="${plan.plan_code}">
                        <div class="plan-header">
                            <div class="plan-name">${plan.plan_name}</div>
                            ${plan.is_featured ? '<div class="plan-badge">Most Popular</div>' : ''}
                            ${isDisabled && !isBasic ? '<div class="plan-badge coming-soon">Coming Soon</div>' : ''}
                            <div class="plan-rate">${(plan.daily_rate * 100).toFixed(1)}%</div>
                            <div class="plan-period">Daily</div>
                            <div class="plan-duration">${plan.duration} Days</div>
                            <div class="plan-minimum-header">Min: ${plan.min_amount} USDT</div>
                        </div>
                        <div class="plan-details">
                            <div class="plan-features">
                                ${plan.features.map(feature => `<li>✅ ${feature}</li>`).join('')}
                            </div>
                        </div>
                        ${isDisabled && !isBasic ? '<div class="plan-overlay"><span>Coming Soon</span></div>' : ''}
                    </div>
                `;
            });
            
            html += '</div>';
            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
