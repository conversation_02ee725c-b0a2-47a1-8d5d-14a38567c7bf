<?php

namespace Simbi\Tls\Controllers;

use Simbi\Tls\Repositories\AdminRepository;
use Simbi\Tls\Services\TransactionService;

class AdminController
{
    private AdminRepository $adminRepository;
    private TransactionService $transactionService;

    public function __construct(
        AdminRepository $adminRepository,
        TransactionService $transactionService = null
    ) {
        $this->adminRepository = $adminRepository;
        $this->transactionService = $transactionService;
    }

    public function getSystemStatistics(array $admin): array
    {
        try {
            if (!$admin['is_admin']) {
                return ['error' => 'Admin access required', 'code' => 403];
            }

            // Log admin activity
            $this->logAdminActivity($admin['id'], 'view_system_statistics', 'system');

            $stats = $this->adminRepository->getSystemStatistics();

            return [
                'success' => true,
                'statistics' => $stats
            ];
        } catch (\Exception $e) {
            error_log("Admin system stats error: " . $e->getMessage());
            return ['error' => 'Failed to fetch system statistics', 'code' => 500];
        }
    }

    public function getUserList(array $admin, array $filters = []): array
    {
        try {
            if (!$admin['is_admin']) {
                return ['error' => 'Admin access required', 'code' => 403];
            }

            $limit = min(max($filters['limit'] ?? 50, 1), 100);
            $offset = max($filters['offset'] ?? 0, 0);

            // Log admin activity
            $this->logAdminActivity($admin['id'], 'view_user_list', 'user', null, $filters);

            $users = $this->adminRepository->getUserList($limit, $offset, $filters);

            return [
                'success' => true,
                'users' => $users,
                'pagination' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'has_more' => count($users) === $limit
                ]
            ];
        } catch (\Exception $e) {
            error_log("Admin user list error: " . $e->getMessage());
            return ['error' => 'Failed to fetch user list', 'code' => 500];
        }
    }

    public function updateUserStatus(int $userId, array $data, array $admin): array
    {
        try {
            if (!$admin['is_admin']) {
                return ['error' => 'Admin access required', 'code' => 403];
            }

            $isActive = $data['is_active'] ?? null;
            if ($isActive === null) {
                return ['error' => 'is_active status is required', 'code' => 400];
            }

            $success = $this->adminRepository->updateUserStatus($userId, (bool)$isActive, $admin['id']);

            if (!$success) {
                return ['error' => 'Failed to update user status', 'code' => 500];
            }

            return [
                'success' => true,
                'message' => 'User status updated successfully'
            ];
        } catch (\Exception $e) {
            error_log("Admin user status update error: " . $e->getMessage());
            return ['error' => 'Failed to update user status', 'code' => 500];
        }
    }

    public function promoteToAdmin(int $userId, array $admin): array
    {
        try {
            if (!$admin['is_admin']) {
                return ['error' => 'Admin access required', 'code' => 403];
            }

            // Prevent self-promotion confusion
            if ($userId === $admin['id']) {
                return ['error' => 'Cannot modify your own admin status', 'code' => 400];
            }

            $success = $this->adminRepository->promoteToAdmin($userId, $admin['id']);

            if (!$success) {
                return ['error' => 'Failed to promote user to admin', 'code' => 500];
            }

            return [
                'success' => true,
                'message' => 'User promoted to admin successfully'
            ];
        } catch (\Exception $e) {
            error_log("Admin promotion error: " . $e->getMessage());
            return ['error' => 'Failed to promote user', 'code' => 500];
        }
    }

    public function getActivityLogs(array $admin, array $filters = []): array
    {
        try {
            if (!$admin['is_admin']) {
                return ['error' => 'Admin access required', 'code' => 403];
            }

            $limit = min(max($filters['limit'] ?? 100, 1), 500);
            $offset = max($filters['offset'] ?? 0, 0);

            // Log admin activity
            $this->logAdminActivity($admin['id'], 'view_activity_logs', 'system', null, $filters);

            $logs = $this->adminRepository->getActivityLogs($limit, $offset, $filters);

            return [
                'success' => true,
                'logs' => $logs,
                'pagination' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'has_more' => count($logs) === $limit
                ]
            ];
        } catch (\Exception $e) {
            error_log("Admin activity logs error: " . $e->getMessage());
            return ['error' => 'Failed to fetch activity logs', 'code' => 500];
        }
    }

    public function getTransactionStatistics(array $admin): array
    {
        try {
            if (!$admin['is_admin']) {
                return ['error' => 'Admin access required', 'code' => 403];
            }

            // Log admin activity
            $this->logAdminActivity($admin['id'], 'view_transaction_statistics', 'transaction');

            if (!$this->transactionService) {
                return ['error' => 'Transaction service not available', 'code' => 503];
            }

            return $this->transactionService->getSystemTransactionStats();
        } catch (\Exception $e) {
            error_log("Admin transaction stats error: " . $e->getMessage());        return ['error' => 'Failed to fetch transaction statistics', 'code' => 500];
        }
    }

    public function getSystemHealth(array $admin): array
    {
        try {
            if (!$admin['is_admin']) {
                return ['error' => 'Admin access required', 'code' => 403];
            }

            // Log admin activity
            $this->logAdminActivity($admin['id'], 'view_system_health', 'system');

            $health = [
                'timestamp' => time(),
                'php' => [
                    'version' => phpversion(),
                    'memory_usage' => memory_get_usage(true),
                    'peak_memory' => memory_get_peak_usage(true),
                    'memory_limit' => ini_get('memory_limit')
                ],
                'database' => [
                    'status' => 'connected', // Would check actual DB connection
                    'version' => 'MySQL 8.0' // Would get actual version
                ],
                'disk' => [
                    'free_space' => disk_free_space('.'),
                    'total_space' => disk_total_space('.')
                ],
                'server' => [
                    'uptime' => time() - $_SERVER['REQUEST_TIME'],
                    'load_average' => sys_getloadavg() ?? [0, 0, 0]
                ]
            ];

            return [
                'success' => true,
                'health' => $health
            ];
        } catch (\Exception $e) {
            error_log("Admin system health error: " . $e->getMessage());
            return ['error' => 'Failed to fetch system health', 'code' => 500];
        }
    }

    private function logAdminActivity(int $adminUserId, string $action, string $targetType, 
                                     string $targetId = null, array $details = null): void
    {
        try {
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;

            $this->adminRepository->logActivity(
                $adminUserId,
                $action,
                $targetType,
                $targetId,
                $details,
                $ipAddress,
                $userAgent
            );
        } catch (\Exception $e) {
            error_log("Admin activity logging error: " . $e->getMessage());
        }
    }
}
