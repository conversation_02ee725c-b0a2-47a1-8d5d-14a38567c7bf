<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Investment Authentication Flow</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .log { white-space: pre-wrap; font-family: monospace; background-color: #f8f9fa; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Investment Authentication Flow</h1>
    
    <div class="section">
        <h2>Step 1: Login Test</h2>
        <button onclick="testLogin()">Test <NAME_EMAIL></button>
        <div id="loginResult" class="log"></div>
    </div>
    
    <div class="section">
        <h2>Step 2: Investment Creation Test</h2>
        <button onclick="testInvestment()" disabled id="investBtn">Test Create Investment</button>
        <div id="investmentResult" class="log"></div>
    </div>
    
    <div class="section">
        <h2>Step 3: Get Active Investments Test</h2>
        <button onclick="testGetInvestments()" disabled id="getInvestBtn">Test Get Active Investments</button>
        <div id="getInvestmentsResult" class="log"></div>
    </div>
    
    <div class="section info">
        <h3>Test Progress</h3>
        <div id="progress">Ready to start testing...</div>
    </div>

    <script>
        let authToken = null;
        
        function updateProgress(message) {
            document.getElementById('progress').textContent = message;
        }
        
        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
            if (isError) {
                element.classList.add('error');
            }
        }
        
        async function apiCall(action, data = {}) {
            try {
                const response = await fetch('/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: action,
                        ...data
                    })
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                return { error: error.message };
            }
        }
        
        async function testLogin() {
            updateProgress('Testing login...');
            log('loginResult', 'Attempting to <NAME_EMAIL>...');
            
            const result = await apiCall('login', {
                email: '<EMAIL>',
                password: 'password123'
            });
            
            if (result.success) {
                log('loginResult', 'Login successful!');
                log('loginResult', `Token received: ${result.token ? 'Yes' : 'No'}`);
                log('loginResult', `User ID: ${result.user?.id || 'Not set'}`);
                
                authToken = result.token;
                
                // Enable other test buttons
                document.getElementById('investBtn').disabled = false;
                document.getElementById('getInvestBtn').disabled = false;
                
                updateProgress('Login successful - ready for investment tests');
            } else {
                log('loginResult', `Login failed: ${result.error || 'Unknown error'}`, true);
                updateProgress('Login failed - cannot proceed with tests');
            }
        }
        
        async function testInvestment() {
            updateProgress('Testing investment creation...');
            log('investmentResult', 'Attempting to create investment...');
            log('investmentResult', 'Plan: basic, Amount: 50 USDT');
            
            const result = await apiCall('create_investment', {
                plan: 'basic',
                amount: 50
            });
            
            if (result.success) {
                log('investmentResult', 'Investment created successfully!');
                log('investmentResult', `Investment ID: ${result.investment_id || 'Not provided'}`);
                updateProgress('Investment creation successful!');
            } else {
                log('investmentResult', `Investment failed: ${result.error || 'Unknown error'}`, true);
                updateProgress('Investment creation failed');
                
                // Log additional debug info
                if (result.debug) {
                    log('investmentResult', `Debug info: ${JSON.stringify(result.debug)}`);
                }
            }
        }
        
        async function testGetInvestments() {
            updateProgress('Testing get active investments...');
            log('getInvestmentsResult', 'Attempting to get active investments...');
            
            const result = await apiCall('get_active_investments');
            
            if (result.success || Array.isArray(result)) {
                log('getInvestmentsResult', 'Active investments retrieved successfully!');
                const investments = result.investments || result;
                log('getInvestmentsResult', `Found ${investments.length} active investments`);
                
                if (investments.length > 0) {
                    log('getInvestmentsResult', `Latest investment: ${JSON.stringify(investments[0])}`);
                }
                
                updateProgress('Get investments successful!');
            } else {
                log('getInvestmentsResult', `Get investments failed: ${result.error || 'Unknown error'}`, true);
                updateProgress('Get investments failed');
            }
        }
        
        // Auto-start the test flow
        window.addEventListener('load', function() {
            updateProgress('Page loaded - click "Test Login" to start');
        });
    </script>
</body>
</html>
