<?php
// Simple session test to verify authentication persistence
require_once 'frontend/config.php';

// Initialize session
FrontendConfig::initSession();

echo "Session Test for Active Plan Navigation\n";
echo "=====================================\n\n";

// Check current authentication state
$isAuth = FrontendConfig::isAuthenticated();
$user = FrontendConfig::getCurrentUser();

echo "Authentication Status: " . ($isAuth ? 'AUTHENTICATED' : 'NOT AUTHENTICATED') . "\n";

if ($isAuth && $user) {
    echo "User ID: " . $user['id'] . "\n";
    echo "Email: " . $user['email'] . "\n";
    echo "Token Present: " . (isset($user['token']) ? 'YES' : 'NO') . "\n";
    
    echo "\nSimulating Active Plan Page Access:\n";
    echo "----------------------------------\n";
    
    // Simulate the exact same check as active_plan.php
    if (!FrontendConfig::isAuthenticated()) {
        echo "❌ WOULD REDIRECT TO LOGIN\n";
        echo "Authentication check failed\n";
    } else {
        echo "✅ WOULD ALLOW ACCESS\n";
        echo "Authentication check passed\n";
        
        // Check investment ID
        $testId = $_GET['id'] ?? '1';  // Use default test ID
        if (!$testId) {
            echo "❌ WOULD REDIRECT TO DASHBOARD\n";
            echo "No investment ID provided\n";
        } else {
            echo "✅ WOULD SHOW ACTIVE PLAN PAGE\n";
            echo "Investment ID: $testId\n";
        }
    }
} else {
    echo "❌ No user session found\n";
    echo "\nDiagnosing issue:\n";
    echo "-----------------\n";
    
    if (!isset($_SESSION['user_id'])) {
        echo "Missing: user_id\n";
    }
    if (!isset($_SESSION['token'])) {
        echo "Missing: token\n";
    }
    if (!isset($_SESSION['email'])) {
        echo "Missing: email\n";
    }
    
    echo "\nTo fix: User needs to log in first\n";
}

echo "\nSession Variables:\n";
echo "-----------------\n";
foreach ($_SESSION as $key => $value) {
    if ($key === 'token') {
        echo "$key: " . (empty($value) ? 'EMPTY' : 'SET') . "\n";
    } else {
        echo "$key: $value\n";
    }
}
?>
