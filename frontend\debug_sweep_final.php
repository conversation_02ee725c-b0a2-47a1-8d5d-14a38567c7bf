<?php
/**
 * Final Sweep Debug - Capture exact backend response
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Handle AJAX request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    if (!isset($_SESSION['token']) || !isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'error' => 'Not authenticated']);
        exit;
    }
    
    $action = $_GET['action'];
    
    if ($action === 'test_backend_direct') {
        // Test backend directly with cURL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $backendUrl = "{$protocol}://{$host}/tls/backend/src/index.php/api/sweep-funds";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $backendUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $_SESSION['token']
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_VERBOSE, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        echo json_encode([
            'success' => true,
            'backend_url' => $backendUrl,
            'http_code' => $httpCode,
            'curl_error' => $curlError,
            'raw_response' => $response,
            'response_length' => strlen($response),
            'is_json' => json_decode($response, true) !== null
        ]);
        exit;
    }
    
    if ($action === 'test_ajax_original') {
        // Test the original AJAX call
        try {
            $input = json_decode(file_get_contents('php://input'), true) ?: [];
            
            // Simulate the original ajax.php logic
            require_once 'api_psr4.php';
            $api = new APIWrapper();
            $api->setToken($_SESSION['token']);
            
            $result = $api->sweepFunds();
            
            // Ensure we always return valid JSON
            if (!is_array($result)) {
                $result = ['success' => false, 'error' => 'Invalid response from API'];
            }
            
            // Ensure success field is set
            if (!isset($result['success'])) {
                $result['success'] = isset($result['error']) ? false : true;
            }
            
            echo json_encode($result);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => 'Internal error: ' . $e->getMessage()
            ]);
        }
        exit;
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Final Sweep Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffe6e6; border: 1px solid #ff9999; }
        .success { background: #e6ffe6; border: 1px solid #99ff99; }
        .info { background: #e6f3ff; border: 1px solid #99ccff; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; font-size: 12px; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; border: none; border-radius: 5px; font-size: 16px; }
        .btn-primary { background: #007cba; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        h1, h2, h3 { color: #333; }
        .highlight { background: yellow; padding: 2px 4px; }
        .step { margin: 15px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #007cba; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Final Sweep Debug Tool</h1>
        
        <?php if (isset($_SESSION['token'])): ?>
            <div class="status success">
                ✅ <strong>Session Active</strong><br>
                User ID: <?= $_SESSION['user_id'] ?><br>
                Token: <?= substr($_SESSION['token'], 0, 20) ?>...
            </div>
            
            <div class="section">
                <h2>🎯 Systematic Debug Process</h2>
                <p>Let's test each component step by step to find the exact issue.</p>
                
                <div class="step">
                    <h3>Step 1: Test Backend Direct</h3>
                    <p>Test the backend API directly with cURL to see the raw response.</p>
                    <button class="btn-primary" onclick="testBackendDirect()">🔧 Test Backend Direct</button>
                    <div id="backend-results"></div>
                </div>
                
                <div class="step">
                    <h3>Step 2: Test AJAX Original</h3>
                    <p>Test the exact same logic as the original AJAX call.</p>
                    <button class="btn-success" onclick="testAjaxOriginal()">🧪 Test AJAX Original</button>
                    <div id="ajax-results"></div>
                </div>
                
                <div class="step">
                    <h3>Step 3: Check PHP Error Logs</h3>
                    <p>Check your PHP error logs for any errors that occurred during testing.</p>
                    <div class="status warning">
                        <strong>Check these log files:</strong><br>
                        • XAMPP: <code>xampp/apache/logs/error.log</code><br>
                        • WAMP: <code>wamp/logs/apache_error.log</code><br>
                        • Linux: <code>/var/log/apache2/error.log</code><br>
                        • Windows IIS: <code>C:\inetpub\logs\LogFiles</code>
                    </div>
                </div>
            </div>
            
        <?php else: ?>
            <div class="status error">
                ❌ <strong>No Session Found</strong><br>
                Please <a href="index.php">login first</a> to debug the sweep functionality.
            </div>
        <?php endif; ?>
    </div>

    <script>
    async function testBackendDirect() {
        const resultsDiv = document.getElementById('backend-results');
        resultsDiv.innerHTML = '<div class="status info">⏳ Testing backend directly...</div>';
        
        try {
            const response = await fetch('debug_sweep_final.php?action=test_backend_direct', {
                method: 'POST'
            });
            
            const responseText = await response.text();
            console.log('Backend test response:', responseText);
            
            let html = '<h4>Backend Direct Test Results:</h4>';
            
            try {
                const jsonData = JSON.parse(responseText);
                
                html += '<div class="status ' + (jsonData.success ? 'success' : 'error') + '">';
                html += '<strong>Backend URL:</strong> ' + jsonData.backend_url + '<br>';
                html += '<strong>HTTP Code:</strong> ' + jsonData.http_code + '<br>';
                html += '<strong>Response Length:</strong> ' + jsonData.response_length + ' bytes<br>';
                html += '<strong>Is JSON:</strong> ' + (jsonData.is_json ? 'Yes' : 'No') + '<br>';
                
                if (jsonData.curl_error) {
                    html += '<strong>cURL Error:</strong> ' + jsonData.curl_error + '<br>';
                }
                html += '</div>';
                
                html += '<h5>Raw Backend Response:</h5>';
                html += '<pre>' + escapeHtml(jsonData.raw_response) + '</pre>';
                
                // Analyze the response
                if (jsonData.http_code !== 200) {
                    html += '<div class="status error">❌ HTTP Error ' + jsonData.http_code + ' - Backend not responding correctly</div>';
                } else if (!jsonData.is_json) {
                    html += '<div class="status error">❌ Backend returned non-JSON response</div>';
                    
                    if (jsonData.raw_response.includes('Fatal error')) {
                        html += '<div class="status error">⚠️ PHP Fatal Error detected in backend</div>';
                    }
                    if (jsonData.raw_response.includes('Warning:')) {
                        html += '<div class="status warning">⚠️ PHP Warning detected in backend</div>';
                    }
                    if (jsonData.raw_response.includes('404')) {
                        html += '<div class="status error">⚠️ Backend endpoint not found (404)</div>';
                    }
                } else {
                    html += '<div class="status success">✅ Backend returned valid JSON</div>';
                }
                
            } catch (jsonError) {
                html += '<div class="status error">❌ Failed to parse test response: ' + jsonError.message + '</div>';
                html += '<pre>' + escapeHtml(responseText) + '</pre>';
            }
            
            resultsDiv.innerHTML = html;
            
        } catch (error) {
            resultsDiv.innerHTML = '<div class="status error">❌ Network Error: ' + error.message + '</div>';
        }
    }
    
    async function testAjaxOriginal() {
        const resultsDiv = document.getElementById('ajax-results');
        resultsDiv.innerHTML = '<div class="status info">⏳ Testing original AJAX logic...</div>';
        
        try {
            const response = await fetch('debug_sweep_final.php?action=test_ajax_original', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });
            
            const responseText = await response.text();
            console.log('AJAX test response:', responseText);
            
            let html = '<h4>AJAX Original Test Results:</h4>';
            html += '<h5>Raw Response:</h5>';
            html += '<pre>' + escapeHtml(responseText) + '</pre>';
            
            try {
                const jsonData = JSON.parse(responseText);
                
                if (jsonData.success) {
                    html += '<div class="status success">✅ AJAX call successful!</div>';
                } else {
                    html += '<div class="status error">❌ AJAX call failed: ' + jsonData.error + '</div>';
                    
                    if (jsonData.error.includes('Invalid JSON response')) {
                        html += '<div class="status warning">⚠️ This is the exact error you\'re seeing. Check the backend test above for the root cause.</div>';
                    }
                }
                
            } catch (jsonError) {
                html += '<div class="status error">❌ Invalid JSON from AJAX test: ' + jsonError.message + '</div>';
            }
            
            resultsDiv.innerHTML = html;
            
        } catch (error) {
            resultsDiv.innerHTML = '<div class="status error">❌ Network Error: ' + error.message + '</div>';
        }
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    </script>
</body>
</html>
