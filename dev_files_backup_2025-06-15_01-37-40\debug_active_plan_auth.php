<?php
// Debug active plan authentication issue
require_once 'frontend/config.php';

header('Content-Type: text/plain');

echo "Active Plan Authentication Debug\n";
echo "===============================\n\n";

// Initialize session
FrontendConfig::initSession();

echo "Session ID: " . session_id() . "\n";
echo "Session Name: " . session_name() . "\n\n";

echo "Session Data:\n";
echo "-------------\n";
foreach ($_SESSION as $key => $value) {
    if (is_array($value) || is_object($value)) {
        echo "$key: " . print_r($value, true) . "\n";
    } else {
        echo "$key: $value\n";
    }
}

echo "\nAuthentication Check:\n";
echo "--------------------\n";
echo "isAuthenticated(): " . (FrontendConfig::isAuthenticated() ? 'YES' : 'NO') . "\n";
echo "isAdmin(): " . (FrontendConfig::isAdmin() ? 'YES' : 'NO') . "\n";

$currentUser = FrontendConfig::getCurrentUser();
if ($currentUser) {
    echo "\nCurrent User:\n";
    echo "-------------\n";
    foreach ($currentUser as $key => $value) {
        echo "$key: $value\n";
    }
} else {
    echo "\nCurrent User: NULL\n";
}

echo "\nKey Session Variables Check:\n";
echo "---------------------------\n";
echo "user_id: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'NOT SET') . "\n";
echo "token: " . (isset($_SESSION['token']) ? 'SET' : 'NOT SET') . "\n";
echo "email: " . (isset($_SESSION['email']) ? $_SESSION['email'] : 'NOT SET') . "\n";
echo "is_admin: " . (isset($_SESSION['is_admin']) ? ($_SESSION['is_admin'] ? 'true' : 'false') : 'NOT SET') . "\n";

echo "\nTest Investment ID (from URL):\n";
echo "-----------------------------\n";
$testId = $_GET['id'] ?? 'NOT PROVIDED';
echo "ID: $testId\n";

if (FrontendConfig::isAuthenticated() && $testId !== 'NOT PROVIDED') {
    echo "\n✅ SHOULD ALLOW ACCESS TO ACTIVE PLAN PAGE\n";
} else if (!FrontendConfig::isAuthenticated()) {
    echo "\n❌ WOULD REDIRECT TO LOGIN - AUTHENTICATION FAILED\n";
    echo "Missing session variables:\n";
    if (!isset($_SESSION['user_id'])) echo "- user_id\n";
    if (!isset($_SESSION['token'])) echo "- token\n";
} else {
    echo "\n❌ WOULD REDIRECT TO DASHBOARD - NO INVESTMENT ID\n";
}

echo "\nPHP Session Configuration:\n";
echo "-------------------------\n";
echo "session.save_path: " . session_save_path() . "\n";
echo "session.cookie_lifetime: " . ini_get('session.cookie_lifetime') . "\n";
echo "session.cookie_secure: " . ini_get('session.cookie_secure') . "\n";
echo "session.cookie_httponly: " . ini_get('session.cookie_httponly') . "\n";
echo "session.use_strict_mode: " . ini_get('session.use_strict_mode') . "\n";
?>
