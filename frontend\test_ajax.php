<?php
// Test ajax.php initialization
require_once __DIR__ . '/../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;
use Simbi\Tls\Frontend\Services\ApiService;
use Simbi\Tls\Frontend\Utils\ValidationUtils;

echo "Starting test...\n";

try {
    echo "1. Testing FrontendConfig::init()...\n";
    FrontendConfig::init();
    echo "   OK\n";
    
    echo "2. Testing SessionService::init()...\n";
    SessionService::init();
    echo "   OK\n";
    
    echo "3. Testing ApiService creation...\n";
    $api = new ApiService();
    echo "   OK\n";
    
    echo "4. Testing ValidationUtils...\n";
    $test = ValidationUtils::sanitizeInput(['test' => 'value']);
    echo "   OK\n";
    
    echo "All tests passed!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "Fatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}
?>
