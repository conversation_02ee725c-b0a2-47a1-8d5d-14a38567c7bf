RewriteEngine On

# Handle API routes
RewriteCond %{REQUEST_METHOD} ^(GET|POST|PUT|DELETE|OPTIONS)$
RewriteRule ^.*$ index.php [QSA,L]

# Set headers
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# Handle preflight requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
