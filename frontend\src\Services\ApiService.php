<?php

declare(strict_types=1);

namespace Simbi\Tls\Frontend\Services;

use Simbi\Tls\Frontend\Config\FrontendConfig;

/**
 * PSR-4 compliant API service for frontend
 * Handles all API communication with the backend
 */
class ApiService
{
    private string $baseUrl;
    private ?string $token;
    private int $timeout;

    public function __construct(?string $baseUrl = null, int $timeout = 30)
    {
        $this->baseUrl = rtrim($baseUrl ?: FrontendConfig::get('API_BASE_URL', 'http://localhost:8000'), '/');
        $this->token = $_SESSION['token'] ?? null;
        $this->timeout = $timeout;
    }

    public function setToken(?string $token): void
    {
        $this->token = $token;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    /**
     * Make HTTP request to API
     */
    private function makeRequest(string $endpoint, string $method = 'GET', ?array $data = null, bool $useAuth = true): array
    {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);

        // Set headers
        $headers = ['Content-Type: application/json'];
        if ($useAuth && $this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // Set method and data
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_error($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return ['error' => 'Connection error: ' . $error];
        }
        
        curl_close($ch);

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['error' => 'Invalid JSON response'];
        }

        return $decodedResponse;
    }

    // Authentication methods
    public function register(string $email, string $password): array
    {
        return $this->makeRequest('/api/register', 'POST', [
            'email' => $email,
            'password' => $password
        ], false);
    }

    public function login(string $email, string $password): array
    {
        return $this->makeRequest('/api/login', 'POST', [
            'email' => $email,
            'password' => $password
        ], false);
    }

    public function passwordResetRequest(string $email): array
    {
        return $this->makeRequest('/api/password-reset-request', 'POST', [
            'email' => $email
        ], false);
    }

    public function passwordReset(string $token, string $newPassword): array
    {
        return $this->makeRequest('/api/password-reset', 'POST', [
            'token' => $token,
            'new_password' => $newPassword
        ], false);
    }

    // User methods
    public function getUserProfile(): array
    {
        return $this->makeRequest('/api/me', 'GET');
    }

    public function changePassword(string $currentPassword, string $newPassword): array
    {
        return $this->makeRequest('/api/change-password', 'POST', [
            'current_password' => $currentPassword,
            'new_password' => $newPassword
        ]);
    }

    // Wallet methods
    public function createWallet(): array
    {
        return $this->makeRequest('/api/create-wallet', 'POST');
    }    public function getBalance(): array
    {
        return $this->makeRequest('/api/balance', 'POST');
    }

    public function getWallet(): array
    {
        return $this->makeRequest('/api/wallet', 'GET');
    }

    public function getLiveWalletBalance(string $address): array
    {
        return $this->makeRequest('/api/wallet/live-balance', 'POST', [
            'address' => $address
        ]);
    }

    public function withdraw(string $to, float $amount): array
    {
        return $this->makeRequest('/api/withdraw', 'POST', [
            'to' => $to,
            'amount' => $amount
        ]);
    }

    public function sweepFunds(): array
    {
        // Try the direct route first, then fallback to query parameter
        $result = $this->makeRequest('/api/sweep-funds', 'POST');

        // If we get a 404 or endpoint not found, try query parameter method
        if (isset($result['error']) && (
            strpos($result['error'], 'Endpoint not found') !== false ||
            strpos($result['error'], '404') !== false ||
            isset($result['http_code']) && $result['http_code'] === 404
        )) {
            error_log("Sweep funds: Direct route failed, trying query parameter method");
            return $this->makeRequest('?route=sweep-funds', 'POST');
        }

        return $result;
    }    // Transaction methods
    public function getTransactions($param1 = null, $param2 = 10, $param3 = 0, $param4 = null): array
    {
        // Handle different parameter patterns for backward compatibility
        if (is_int($param1)) {
            // Called as getTransactions(limit, offset, type, status)
            $limit = $param1;
            $offset = $param2;
            $type = $param3;
            $status = $param4;
            
            $params = ['limit' => $limit, 'offset' => $offset];
            if ($type) $params['type'] = $type;
            if ($status) $params['status'] = $status;
        } else {
            // Called as getTransactions(type, limit, page)
            $type = $param1;
            $limit = $param2;
            $page = $param3;
            
            $params = ['limit' => $limit, 'page' => $page];
            if ($type) $params['type'] = $type;
        }

        return $this->makeRequest('/api/transactions?' . http_build_query($params), 'GET');
    }

    public function getTransactionStatistics(): array
    {
        return $this->makeRequest('/api/transactions/statistics', 'GET');
    }

    // Investment methods
    public function getInvestmentPlans(): array
    {
        return $this->makeRequest('/api/investment-plans', 'GET', null, false);
    }

    public function createInvestment(float $amount, string $plan): array
    {
        return $this->makeRequest('/api/invest', 'POST', [
            'amount' => $amount,
            'plan' => $plan
        ]);
    }    public function getInvestments(): array
    {
        return $this->makeRequest('/api/investments/active', 'GET');
    }public function getActiveInvestments(): array
    {
        return $this->makeRequest('/api/investments/active', 'GET');    }

    // Payment methods
    public function confirmPayment(int $walletId, float $amount): array
    {
        return $this->makeRequest('/api/payment/confirm', 'POST', [
            'wallet_id' => $walletId,
            'amount' => $amount
        ]);
    }

    public function getPaymentStatus(string $transactionHash): array
    {
        return $this->makeRequest('/api/payment/status', 'POST', [
            'transaction_hash' => $transactionHash
        ]);
    }

    // Admin methods
    public function getSystemStatistics(): array
    {
        return $this->makeRequest('/api/admin/statistics', 'GET');
    }

    public function getUserList(int $limit = 20, int $offset = 0, ?string $status = null): array
    {
        $params = ['limit' => $limit, 'offset' => $offset];
        if ($status) {
            $params['status'] = $status;
        }
        
        $query = http_build_query($params);
        return $this->makeRequest('/api/admin/users?' . $query, 'GET');
    }

    public function getAllTransactions(int $limit = 100, int $offset = 0): array
    {
        $query = http_build_query(['limit' => $limit, 'offset' => $offset]);
        return $this->makeRequest('/api/admin/transactions?' . $query, 'GET');
    }    public function updateUserStatus(int $userId, $status): array
    {
        // Handle both string status and boolean isActive
        if (is_bool($status)) {
            $data = ['is_active' => $status];
        } else {
            $data = ['status' => $status];
        }
        
        return $this->makeRequest("/api/admin/users/{$userId}/status", 'PUT', $data);
    }

    public function promoteUser(int $userId): array
    {
        return $this->makeRequest("/api/admin/users/{$userId}/promote", 'POST');
    }

    public function demoteUser(int $userId): array
    {
        return $this->makeRequest("/api/admin/users/{$userId}/demote", 'POST');
    }

    public function getActivityLogs(int $limit = 50, int $offset = 0): array
    {
        $query = http_build_query(['limit' => $limit, 'offset' => $offset]);
        return $this->makeRequest('/api/admin/logs?' . $query, 'GET');
    }

    public function getAdminTransactionStatistics(): array
    {
        return $this->makeRequest('/api/admin/transactions/statistics', 'GET');
    }

    public function getSystemHealth(): array
    {
        return $this->makeRequest('/api/admin/health', 'GET');
    }

    // Additional methods for compatibility with ajax.php
    public function getInvestmentHistory(int $limit = 20, int $offset = 0): array
    {
        $query = http_build_query(['limit' => $limit, 'offset' => $offset]);
        return $this->makeRequest('/api/investments/history?' . $query, 'GET');
    }

    public function getInvestmentStatistics(): array
    {
        return $this->makeRequest('/api/investments/statistics', 'GET');
    }

    public function getInvestmentDetails(int $investmentId): array
    {
        return $this->makeRequest("/api/investments/{$investmentId}", 'GET');
    }

    public function getInvestmentEarnings(int $investmentId): array
    {
        return $this->makeRequest("/api/investments/{$investmentId}/earnings", 'GET');
    }    public function recordDeposit(string $walletAddress, string $transactionHash, float $amount, string $fromAddress): array
    {
        return $this->makeRequest('/api/transactions/deposit', 'POST', [
            'wallet_address' => $walletAddress,
            'transaction_hash' => $transactionHash,
            'amount' => $amount,
            'from_address' => $fromAddress
        ]);
    }
}
