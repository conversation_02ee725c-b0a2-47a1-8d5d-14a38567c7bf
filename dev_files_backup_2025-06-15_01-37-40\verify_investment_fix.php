<?php
// Test the investment flow fix directly
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/backend/src/Config/Config.php';
require_once __DIR__ . '/backend/src/Config/Database.php';
require_once __DIR__ . '/backend/src/Services/InvestmentService.php';
require_once __DIR__ . '/backend/src/Services/WalletService.php';

use Simbi\Tls\Services\InvestmentService;
use Simbi\Tls\Services\WalletService;

echo "=== Investment Deduction Fix Verification ===\n";
echo "Testing the fixed investment flow logic\n\n";

try {
    $investmentService = new InvestmentService();
    $walletService = new WalletService();
    $userId = 1; // Test user
    
    echo "STEP 1: Check current balance\n";
    echo "============================\n";
    $currentBalance = $walletService->getBalance($userId);
    echo "Current available balance: $currentBalance USDT\n";
    
    echo "\nSTEP 2: Test investment amounts\n";
    echo "===============================\n";
    
    $testAmounts = [25, 30, 35]; // Test below, at, and above balance
    
    foreach ($testAmounts as $amount) {
        echo "\nTesting investment of $amount USDT:\n";
        echo "- Available balance: $currentBalance USDT\n";
        echo "- Investment amount: $amount USDT\n";
        
        if ($amount > $currentBalance) {
            echo "- Expected result: Should fail with 'Insufficient balance'\n";
        } else {
            echo "- Expected result: Should succeed\n";
        }
        
        // Get plan details (basic plan)
        $planDetails = [
            'name' => 'Basic Plan',
            'dailyRate' => 0.0167, // 1.67% daily
            'duration' => 30
        ];
        
        $result = $investmentService->createInvestment($userId, $amount, 'basic', $planDetails);
        
        echo "- Actual result: ";
        if ($result['success']) {
            echo "SUCCESS - Investment ID: " . $result['investment_id'] . "\n";
            // Update balance for next test
            $currentBalance = $walletService->getBalance($userId);
            echo "- New available balance: $currentBalance USDT\n";
        } else {
            echo "FAILED - " . $result['message'] . "\n";
            
            // Check if it's the old "Failed to deduct balance" error
            if (strpos($result['message'], 'Failed to deduct balance') !== false) {
                echo "❌ CRITICAL: Still getting 'Failed to deduct balance' error!\n";
            } elseif (strpos($result['message'], 'Insufficient balance') !== false) {
                echo "✅ GOOD: Getting proper 'Insufficient balance' error\n";
            }
        }
        
        echo str_repeat("-", 50) . "\n";
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "If the fix worked correctly, you should see:\n";
    echo "1. ✅ Investments ≤ available balance succeed\n";
    echo "2. ✅ Investments > available balance fail with 'Insufficient balance'\n";
    echo "3. ❌ NO 'Failed to deduct balance' errors\n";
    
} catch (Exception $e) {
    echo "❌ Error during test: " . $e->getMessage() . "\n";
}
?>
