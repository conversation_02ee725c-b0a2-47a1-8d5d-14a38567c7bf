<?php
require_once __DIR__ . '/backend/src/Services/WalletService.php';
require_once __DIR__ . '/backend/src/Config/Database.php';

use Simbi\Tls\Services\WalletService;

echo "=== Balance Calculation Test ===\n";
echo "Testing the fix for user_id 1 balance calculation\n\n";

try {
    $walletService = new WalletService();
    $userId = 1;
    
    // Test the fixed balance calculation
    $availableBalance = $walletService->getBalance($userId);
    
    echo "User ID: $userId\n";
    echo "Available Balance: $availableBalance USDT\n";
    echo "Expected Balance: 30 USDT (630 wallet - 600 investments)\n\n";
    
    if (abs($availableBalance - 30) < 0.01) {
        echo "✅ SUCCESS: Balance calculation is correct!\n";
    } else {
        echo "❌ ERROR: Balance calculation is still incorrect.\n";
        echo "   Expected: 30 USDT\n";
        echo "   Actual: $availableBalance USDT\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== End Test ===\n";
?>
