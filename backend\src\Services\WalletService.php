<?php

namespace Simbi\Tls\Services;

use Simbi\Tls\Config\Database;
use Exception;

class WalletService
{
    private $db;

    public function __construct()
    {
        $database = new Database();
        $this->db = $database->getConnection();
    }      public function getBalance($userId)
    {
        try {
            // Get wallet balance
            $stmt = $this->db->prepare("SELECT balance FROM wallets WHERE user_id = ?");
            $stmt->execute([$userId]);
            $walletResult = $stmt->fetch(\PDO::FETCH_ASSOC);
            $walletBalance = $walletResult ? floatval($walletResult['balance']) : 0;

            // Get sum of all active investments (status = 'active')
            $stmt = $this->db->prepare("SELECT COALESCE(SUM(amount), 0) as total_invested FROM investments WHERE user_id = ? AND status = 'active'");
            $stmt->execute([$userId]);
            $investmentResult = $stmt->fetch(\PDO::FETCH_ASSOC);
            $totalInvested = $investmentResult ? floatval($investmentResult['total_invested']) : 0;

            // Available balance = wallet balance - total active investments
            $availableBalance = $walletBalance - $totalInvested;
            return $availableBalance;

        } catch (Exception $e) {
            error_log("Get balance error: " . $e->getMessage());
            return 0;
        }
    }

    public function getWallet($userId)
    {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    w.id,
                    w.address,
                    w.created_at,
                    w.user_id
                FROM wallets w 
                WHERE w.user_id = ? 
                LIMIT 1
            ");
            $stmt->execute([$userId]);
            $wallet = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if ($wallet) {
                // Add balance to wallet data
                $wallet['balance'] = $this->getBalance($userId);
                return $wallet;
            }
            
            return null;

        } catch (Exception $e) {
            error_log("Get wallet error: " . $e->getMessage());
            return null;
        }
    }

    public function addBalance($userId, $amount, $type = 'deposit')
    {
        try {
            // Get user's wallet ID
            $stmt = $this->db->prepare("SELECT id FROM wallets WHERE user_id = ? LIMIT 1");
            $stmt->execute([$userId]);
            $wallet = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$wallet) {
                error_log("No wallet found for user $userId");
                return false;
            }

            // Create a transaction record to add balance
            $stmt = $this->db->prepare("
                INSERT INTO transactions (user_id, wallet_id, type, amount, status, transaction_hash, created_at) 
                VALUES (?, ?, ?, ?, 'confirmed', ?, NOW())
            ");
            return $stmt->execute([$userId, $wallet['id'], $type, $amount, $type . '_' . time() . '_' . $userId]);

        } catch (Exception $e) {
            error_log("Add balance error: " . $e->getMessage());
            return false;
        }
    }    public function deductBalance($userId, $amount, $type = 'investment', $skipBalanceCheck = false)
    {
        try {
            // Skip balance check if already verified by caller to avoid circular dependency
            if (!$skipBalanceCheck) {
                $currentBalance = $this->getBalance($userId);
                if ($currentBalance < $amount) {
                    return false;
                }
            }
            
            $stmt = $this->db->prepare("SELECT id FROM wallets WHERE user_id = ? LIMIT 1");
            $stmt->execute([$userId]);
            $wallet = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$wallet) {
                error_log("No wallet found for user $userId");
                return false;
            }

            // Create a transaction record to deduct balance
            $stmt = $this->db->prepare("
                INSERT INTO transactions (user_id, wallet_id, type, amount, status, transaction_hash, created_at) 
                VALUES (?, ?, ?, ?, 'confirmed', ?, NOW())
            ");
            return $stmt->execute([$userId, $wallet['id'], $type, $amount, $type . '_' . time() . '_' . $userId]);

        } catch (Exception $e) {
            error_log("Deduct balance error: " . $e->getMessage());
            return false;
        }
    }

    public function createWallet($userId)
    {
        try {
            // Generate a new wallet address (simplified - in production use proper TRON wallet generation)
            $address = $this->generateTronAddress();
            
            $stmt = $this->db->prepare("
                INSERT INTO wallets (user_id, address, private_key, balance) 
                VALUES (?, ?, ?, 0)
                ON DUPLICATE KEY UPDATE address = VALUES(address)
            ");

            $privateKey = $this->generatePrivateKey();
            $result = $stmt->execute([$userId, $address, $privateKey]);

            if ($result) {
                return [
                    'address' => $address,
                    'balance' => 0
                ];
            }

            return false;

        } catch (Exception $e) {
            error_log("Create wallet error: " . $e->getMessage());
            return false;        }
    }

    private function generateTronAddress()
    {
        // Simplified TRON address generation for demo
        // In production, use proper TRON SDK
        return 'T' . bin2hex(random_bytes(20));
    }

    private function generatePrivateKey()
    {
        // Simplified private key generation for demo
        // In production, use proper cryptographic methods
        return bin2hex(random_bytes(32));
    }
}
