// Admin Dashboard JavaScript
class AdminDashboard {
    constructor() {
        this.currentTab = 'overview';
        this.users = [];
        this.transactions = [];
        this.stats = {};
        this.logs = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
        this.setupRefreshIntervals();
    }

    bindEvents() {
        // Tab navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                this.switchTab(tab);
            });
        });

        // User management
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-promote')) {
                this.promoteUser(e.target.dataset.userId);
            } else if (e.target.classList.contains('btn-demote')) {
                this.demoteUser(e.target.dataset.userId);
            } else if (e.target.classList.contains('btn-activate')) {
                this.activateUser(e.target.dataset.userId);
            } else if (e.target.classList.contains('btn-deactivate')) {
                this.deactivateUser(e.target.dataset.userId);
            }
        });

        // System actions
        document.getElementById('clearCacheBtn')?.addEventListener('click', () => this.clearCache());
        document.getElementById('backupDbBtn')?.addEventListener('click', () => this.backupDatabase());
        document.getElementById('refreshStatsBtn')?.addEventListener('click', () => this.refreshStats());

        // Filters and search
        document.getElementById('userSearch')?.addEventListener('input', (e) => this.filterUsers(e.target.value));
        document.getElementById('transactionFilter')?.addEventListener('change', (e) => this.filterTransactions(e.target.value));
        document.getElementById('logFilter')?.addEventListener('change', (e) => this.filterLogs(e.target.value));
    }

    switchTab(tabName) {
        // Update active tab
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Show/hide content
        document.querySelectorAll('.admin-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');

        this.currentTab = tabName;
        this.loadTabData(tabName);
    }

    async loadInitialData() {
        this.showLoader();
        try {
            await this.loadOverviewData();
        } catch (error) {
            this.showMessage('Error loading initial data', 'error');
            console.error('Initial load error:', error);
        } finally {
            this.hideLoader();
        }
    }

    async loadTabData(tab) {
        this.showLoader();
        try {
            switch (tab) {
                case 'overview':
                    await this.loadOverviewData();
                    break;
                case 'users':
                    await this.loadUsersData();
                    break;
                case 'transactions':
                    await this.loadTransactionsData();
                    break;
                case 'system':
                    await this.loadSystemData();
                    break;
                case 'logs':
                    await this.loadLogsData();
                    break;
            }
        } catch (error) {
            this.showMessage(`Error loading ${tab} data`, 'error');
            console.error(`${tab} load error:`, error);
        } finally {
            this.hideLoader();
        }
    }

    async loadOverviewData() {
        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'admin_stats' })
            });

            const data = await response.json();
            if (data.success) {
                this.stats = data.data;
                this.renderOverview();
            } else {
                throw new Error(data.message || 'Failed to load overview data');
            }
        } catch (error) {
            console.error('Overview load error:', error);
            throw error;
        }
    }

    async loadUsersData() {
        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'admin_users' })
            });

            const data = await response.json();
            if (data.success) {
                this.users = data.data;
                this.renderUsers();
            } else {
                throw new Error(data.message || 'Failed to load users data');
            }
        } catch (error) {
            console.error('Users load error:', error);
            throw error;
        }
    }

    async loadTransactionsData() {
        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'admin_transactions' })
            });

            const data = await response.json();
            if (data.success) {
                this.transactions = data.data;
                this.renderTransactions();
            } else {
                throw new Error(data.message || 'Failed to load transactions data');
            }
        } catch (error) {
            console.error('Transactions load error:', error);
            throw error;
        }
    }

    async loadSystemData() {
        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'admin_system' })
            });

            const data = await response.json();
            if (data.success) {
                this.renderSystemInfo(data.data);
            } else {
                throw new Error(data.message || 'Failed to load system data');
            }
        } catch (error) {
            console.error('System load error:', error);
            throw error;
        }
    }

    async loadLogsData() {
        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'admin_logs' })
            });

            const data = await response.json();
            if (data.success) {
                this.logs = data.data;
                this.renderLogs();
            } else {
                throw new Error(data.message || 'Failed to load logs data');
            }
        } catch (error) {
            console.error('Logs load error:', error);
            throw error;
        }
    }

    renderOverview() {
        const overviewContent = document.querySelector('#overviewTab .stats-grid');
        if (!overviewContent) return;

        overviewContent.innerHTML = `
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <div class="stat-number">${this.stats.total_users || 0}</div>
                    <div class="stat-label">Total Users</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <div class="stat-number">${this.formatNumber(this.stats.total_balance || 0)} TRX</div>
                    <div class="stat-label">Total Balance</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                    <div class="stat-number">${this.stats.total_transactions || 0}</div>
                    <div class="stat-label">Total Transactions</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🔄</div>
                <div class="stat-content">
                    <div class="stat-number">${this.stats.pending_transactions || 0}</div>
                    <div class="stat-label">Pending Transactions</div>
                </div>
            </div>
        `;

        // Update recent activity
        const recentActivity = document.querySelector('#overviewTab .recent-activity ul');
        if (recentActivity && this.stats.recent_activities) {
            recentActivity.innerHTML = this.stats.recent_activities.map(activity => `
                <li>
                    <div class="activity-content">
                        <div class="activity-title">${activity.action}</div>
                        <div class="activity-details">${activity.details}</div>
                        <div class="activity-time">${this.formatDate(activity.created_at)}</div>
                    </div>
                </li>
            `).join('');
        }
    }

    renderUsers() {
        const usersTable = document.querySelector('#usersTab .data-table tbody');
        if (!usersTable) return;

        usersTable.innerHTML = this.users.map(user => `
            <tr>
                <td>${user.id}</td>
                <td>${user.email}</td>
                <td>
                    <span class="status ${user.status}">${user.status}</span>
                </td>
                <td>
                    <span class="role ${user.is_admin ? 'admin' : 'user'}">${user.is_admin ? 'Admin' : 'User'}</span>
                </td>
                <td>${this.formatNumber(user.balance || 0)} TRX</td>
                <td>${this.formatDate(user.created_at)}</td>
                <td>
                    <div class="table-actions">
                        ${user.is_admin ? 
                            `<button class="btn btn-sm btn-secondary btn-demote" data-user-id="${user.id}">Demote</button>` :
                            `<button class="btn btn-sm btn-primary btn-promote" data-user-id="${user.id}">Promote</button>`
                        }
                        ${user.status === 'active' ?
                            `<button class="btn btn-sm btn-danger btn-deactivate" data-user-id="${user.id}">Deactivate</button>` :
                            `<button class="btn btn-sm btn-success btn-activate" data-user-id="${user.id}">Activate</button>`
                        }
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderTransactions() {
        const transactionsTable = document.querySelector('#transactionsTab .data-table tbody');
        if (!transactionsTable) return;

        transactionsTable.innerHTML = this.transactions.map(tx => `
            <tr>
                <td>${tx.id}</td>
                <td>${tx.user_email}</td>
                <td>
                    <span class="transaction-type ${tx.type}">${tx.type}</span>
                </td>
                <td>${this.formatNumber(tx.amount)} TRX</td>
                <td>
                    <span class="status ${tx.status}">${tx.status}</span>
                </td>
                <td class="tx-hash" title="${tx.hash}">${tx.hash ? tx.hash.substring(0, 16) + '...' : 'N/A'}</td>
                <td>${this.formatDate(tx.created_at)}</td>
            </tr>
        `).join('');
    }

    renderSystemInfo(systemData) {
        const systemContent = document.querySelector('#systemTab .system-info');
        if (!systemContent) return;

        systemContent.innerHTML = `
            <div class="info-section">
                <h3>Server Information</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">PHP Version:</span>
                        <span class="info-value">${systemData.php_version || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Server Load:</span>
                        <span class="info-value">${systemData.server_load || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Memory Usage:</span>
                        <span class="info-value">${systemData.memory_usage || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Database Size:</span>
                        <span class="info-value">${systemData.db_size || 'N/A'}</span>
                    </div>
                </div>
            </div>
            <div class="info-section">
                <h3>TRON Network Status</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Network:</span>
                        <span class="info-value">${systemData.tron_network || 'Mainnet'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Block Height:</span>
                        <span class="info-value">${systemData.block_height || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">API Status:</span>
                        <span class="info-value status ${systemData.api_status || 'offline'}">${systemData.api_status || 'Unknown'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    renderLogs() {
        const logsTable = document.querySelector('#logsTab .data-table tbody');
        if (!logsTable) return;

        logsTable.innerHTML = this.logs.map(log => `
            <tr>
                <td>${log.id}</td>
                <td>
                    <span class="log-level ${log.level}">${log.level}</span>
                </td>
                <td>${log.action}</td>
                <td>${log.user_email || 'System'}</td>
                <td class="log-details">${log.details}</td>
                <td>${this.formatDate(log.created_at)}</td>
            </tr>
        `).join('');
    }

    async promoteUser(userId) {
        if (!confirm('Are you sure you want to promote this user to admin?')) return;

        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    action: 'admin_promote_user',
                    user_id: userId
                })
            });

            const data = await response.json();
            if (data.success) {
                this.showMessage('User promoted successfully', 'success');
                await this.loadUsersData();
            } else {
                this.showMessage(data.message || 'Failed to promote user', 'error');
            }
        } catch (error) {
            this.showMessage('Error promoting user', 'error');
            console.error('Promote user error:', error);
        }
    }

    async demoteUser(userId) {
        if (!confirm('Are you sure you want to demote this admin to user?')) return;

        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    action: 'admin_demote_user',
                    user_id: userId
                })
            });

            const data = await response.json();
            if (data.success) {
                this.showMessage('User demoted successfully', 'success');
                await this.loadUsersData();
            } else {
                this.showMessage(data.message || 'Failed to demote user', 'error');
            }
        } catch (error) {
            this.showMessage('Error demoting user', 'error');
            console.error('Demote user error:', error);
        }
    }

    async activateUser(userId) {
        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    action: 'admin_activate_user',
                    user_id: userId
                })
            });

            const data = await response.json();
            if (data.success) {
                this.showMessage('User activated successfully', 'success');
                await this.loadUsersData();
            } else {
                this.showMessage(data.message || 'Failed to activate user', 'error');
            }
        } catch (error) {
            this.showMessage('Error activating user', 'error');
            console.error('Activate user error:', error);
        }
    }

    async deactivateUser(userId) {
        if (!confirm('Are you sure you want to deactivate this user?')) return;

        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    action: 'admin_deactivate_user',
                    user_id: userId
                })
            });

            const data = await response.json();
            if (data.success) {
                this.showMessage('User deactivated successfully', 'success');
                await this.loadUsersData();
            } else {
                this.showMessage(data.message || 'Failed to deactivate user', 'error');
            }
        } catch (error) {
            this.showMessage('Error deactivating user', 'error');
            console.error('Deactivate user error:', error);
        }
    }

    async clearCache() {
        if (!confirm('Are you sure you want to clear the system cache?')) return;

        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'admin_clear_cache' })
            });

            const data = await response.json();
            if (data.success) {
                this.showMessage('Cache cleared successfully', 'success');
            } else {
                this.showMessage(data.message || 'Failed to clear cache', 'error');
            }
        } catch (error) {
            this.showMessage('Error clearing cache', 'error');
            console.error('Clear cache error:', error);
        }
    }

    async backupDatabase() {
        if (!confirm('Are you sure you want to create a database backup?')) return;

        try {
            const response = await fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'admin_backup_db' })
            });

            const data = await response.json();
            if (data.success) {
                this.showMessage('Database backup created successfully', 'success');
            } else {
                this.showMessage(data.message || 'Failed to create backup', 'error');
            }
        } catch (error) {
            this.showMessage('Error creating backup', 'error');
            console.error('Backup error:', error);
        }
    }

    async refreshStats() {
        await this.loadOverviewData();
        this.showMessage('Statistics refreshed', 'success');
    }

    filterUsers(searchTerm) {
        const filteredUsers = this.users.filter(user => 
            user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.id.toString().includes(searchTerm)
        );
        
        const usersTable = document.querySelector('#usersTab .data-table tbody');
        if (!usersTable) return;

        usersTable.innerHTML = filteredUsers.map(user => `
            <tr>
                <td>${user.id}</td>
                <td>${user.email}</td>
                <td>
                    <span class="status ${user.status}">${user.status}</span>
                </td>
                <td>
                    <span class="role ${user.is_admin ? 'admin' : 'user'}">${user.is_admin ? 'Admin' : 'User'}</span>
                </td>
                <td>${this.formatNumber(user.balance || 0)} TRX</td>
                <td>${this.formatDate(user.created_at)}</td>
                <td>
                    <div class="table-actions">
                        ${user.is_admin ? 
                            `<button class="btn btn-sm btn-secondary btn-demote" data-user-id="${user.id}">Demote</button>` :
                            `<button class="btn btn-sm btn-primary btn-promote" data-user-id="${user.id}">Promote</button>`
                        }
                        ${user.status === 'active' ?
                            `<button class="btn btn-sm btn-danger btn-deactivate" data-user-id="${user.id}">Deactivate</button>` :
                            `<button class="btn btn-sm btn-success btn-activate" data-user-id="${user.id}">Activate</button>`
                        }
                    </div>
                </td>
            </tr>
        `).join('');
    }

    filterTransactions(filterType) {
        let filteredTransactions = this.transactions;
        
        if (filterType && filterType !== 'all') {
            filteredTransactions = this.transactions.filter(tx => 
                tx.type === filterType || tx.status === filterType
            );
        }

        const transactionsTable = document.querySelector('#transactionsTab .data-table tbody');
        if (!transactionsTable) return;

        transactionsTable.innerHTML = filteredTransactions.map(tx => `
            <tr>
                <td>${tx.id}</td>
                <td>${tx.user_email}</td>
                <td>
                    <span class="transaction-type ${tx.type}">${tx.type}</span>
                </td>
                <td>${this.formatNumber(tx.amount)} TRX</td>
                <td>
                    <span class="status ${tx.status}">${tx.status}</span>
                </td>
                <td class="tx-hash" title="${tx.hash}">${tx.hash ? tx.hash.substring(0, 16) + '...' : 'N/A'}</td>
                <td>${this.formatDate(tx.created_at)}</td>
            </tr>
        `).join('');
    }

    filterLogs(filterLevel) {
        let filteredLogs = this.logs;
        
        if (filterLevel && filterLevel !== 'all') {
            filteredLogs = this.logs.filter(log => log.level === filterLevel);
        }

        const logsTable = document.querySelector('#logsTab .data-table tbody');
        if (!logsTable) return;

        logsTable.innerHTML = filteredLogs.map(log => `
            <tr>
                <td>${log.id}</td>
                <td>
                    <span class="log-level ${log.level}">${log.level}</span>
                </td>
                <td>${log.action}</td>
                <td>${log.user_email || 'System'}</td>
                <td class="log-details">${log.details}</td>
                <td>${this.formatDate(log.created_at)}</td>
            </tr>
        `).join('');
    }

    setupRefreshIntervals() {
        // Refresh overview stats every 30 seconds
        setInterval(() => {
            if (this.currentTab === 'overview') {
                this.loadOverviewData();
            }
        }, 30000);

        // Refresh system data every 60 seconds
        setInterval(() => {
            if (this.currentTab === 'system') {
                this.loadSystemData();
            }
        }, 60000);
    }

    formatNumber(number) {
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 6
        }).format(number);
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    showLoader() {
        const loader = document.querySelector('.loader');
        if (loader) loader.style.display = 'block';
    }

    hideLoader() {
        const loader = document.querySelector('.loader');
        if (loader) loader.style.display = 'none';
    }

    showMessage(message, type = 'info') {
        const messageDiv = document.getElementById('adminMessage');
        if (!messageDiv) return;

        messageDiv.textContent = message;
        messageDiv.className = `message ${type}`;
        messageDiv.style.display = 'block';

        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
}

// Initialize admin dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdminDashboard();
});
