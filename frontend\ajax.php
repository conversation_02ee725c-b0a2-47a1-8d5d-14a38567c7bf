<?php
// Include PSR-4 autoloader and initialize
require_once __DIR__ . '/../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;
use Simbi\Tls\Frontend\Services\ApiService;
use Simbi\Tls\Frontend\Utils\ValidationUtils;

// Initialize frontend configuration and session
FrontendConfig::init();
SessionService::init();

header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$input = ValidationUtils::sanitizeInput($input ?? []);
$action = $input['action'] ?? '';

$api = new ApiService();

switch ($action) {
    case 'register':
        $email = $input['email'] ?? '';
        $password = $input['password'] ?? '';
        
        if (empty($email) || empty($password)) {
            echo json_encode(['error' => 'Email and password are required']);
            break;
        }
        
        $result = $api->register($email, $password);
        
        if (isset($result['success']) && $result['success']) {
            // Store session data
            $_SESSION['user_id'] = $result['user']['id'];
            $_SESSION['email'] = $result['user']['email'];
            $_SESSION['token'] = $result['token'];
            $_SESSION['is_admin'] = $result['user']['is_admin'] ?? false;
        }
        
        echo json_encode($result);
        break;

    case 'login':
        $email = $input['email'] ?? '';
        $password = $input['password'] ?? '';
        
        if (empty($email) || empty($password)) {
            echo json_encode(['error' => 'Email and password are required']);
            break;
        }
        
        $result = $api->login($email, $password);
        
        if (isset($result['success']) && $result['success']) {
            // Store session data
            $_SESSION['user_id'] = $result['user']['id'];
            $_SESSION['email'] = $result['user']['email'];
            $_SESSION['token'] = $result['token'];
            $_SESSION['is_admin'] = $result['user']['is_admin'] ?? false;
        }
        
        echo json_encode($result);
        break;

    case 'forgot_password':
        $email = $input['email'] ?? '';
        
        if (empty($email)) {
            echo json_encode(['error' => 'Email is required']);
            break;
        }
        
        $result = $api->passwordResetRequest($email);
        echo json_encode($result);
        break;

    case 'get_profile':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->getUserProfile();
        echo json_encode($result);
        break;

    case 'change_password':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $currentPassword = $input['current_password'] ?? '';
        $newPassword = $input['new_password'] ?? '';
        
        if (empty($currentPassword) || empty($newPassword)) {
            echo json_encode(['error' => 'Current and new passwords are required']);
            break;
        }
        
        $result = $api->changePassword($currentPassword, $newPassword);
        echo json_encode($result);
        break;

    case 'create_wallet':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->createWallet();
        echo json_encode($result);
        break;

    case 'get_balance':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->getBalance();
        echo json_encode($result);
        break;

    case 'withdraw':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $to = $input['to'] ?? '';
        $amount = $input['amount'] ?? 0;
        
        if (empty($to) || $amount <= 0) {
            echo json_encode(['error' => 'Valid recipient address and amount are required']);
            break;
        }
        
        $result = $api->withdraw($to, $amount);
        echo json_encode($result);
        break;

    case 'sweep_funds':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->sweepFunds();
        echo json_encode($result);
        break;

    case 'get_wallet':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->getWallet();
        echo json_encode($result);
        break;

    case 'get_live_wallet':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $address = $input['address'] ?? '';
        if (empty($address)) {
            echo json_encode(['error' => 'Wallet address is required']);
            break;
        }
        
        $result = $api->getLiveWalletBalance($address);
        echo json_encode($result);
        break;

    case 'get_transactions':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $limit = $input['limit'] ?? 10;
        $offset = $input['offset'] ?? 0;
        $type = $input['type'] ?? null;
        $status = $input['status'] ?? null;
        
        $result = $api->getTransactions($limit, $offset, $type, $status);
        echo json_encode($result);
        break;

    case 'record_deposit':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $walletAddress = $input['wallet_address'] ?? '';
        $transactionHash = $input['transaction_hash'] ?? '';
        $amount = $input['amount'] ?? 0;
        $fromAddress = $input['from_address'] ?? '';
        
        if (empty($walletAddress) || empty($transactionHash) || $amount <= 0 || empty($fromAddress)) {
            echo json_encode(['error' => 'All fields are required']);
            break;
        }
        
        $result = $api->recordDeposit($walletAddress, $transactionHash, $amount, $fromAddress);
        echo json_encode($result);
        break;

    case 'get_transaction_statistics':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->getTransactionStatistics();
        echo json_encode($result);
        break;

    // Admin actions
    case 'get_system_statistics':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $result = $api->getSystemStatistics();
        echo json_encode($result);
        break;

    case 'get_user_list':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $limit = $input['limit'] ?? 20;
        $offset = $input['offset'] ?? 0;
        $status = $input['status'] ?? null;
        
        $result = $api->getUserList($limit, $offset, $status);
        echo json_encode($result);
        break;

    case 'update_user_status':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $userId = $input['user_id'] ?? 0;
        $isActive = $input['is_active'] ?? true;
        
        if (!$userId) {
            echo json_encode(['error' => 'User ID is required']);
            break;
        }
        
        $result = $api->updateUserStatus($userId, $isActive);
        echo json_encode($result);
        break;

    case 'promote_user':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $userId = $input['user_id'] ?? 0;
        
        if (!$userId) {
            echo json_encode(['error' => 'User ID is required']);
            break;
        }
        
        $result = $api->promoteUser($userId);
        echo json_encode($result);
        break;

    case 'get_activity_logs':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $limit = $input['limit'] ?? 50;
        $offset = $input['offset'] ?? 0;
        
        $result = $api->getActivityLogs($limit, $offset);
        echo json_encode($result);
        break;

    case 'get_admin_transaction_statistics':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $result = $api->getAdminTransactionStatistics();
        echo json_encode($result);
        break;    case 'get_system_health':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $result = $api->getSystemHealth();
        echo json_encode($result);
        break;

    // New admin actions for the admin dashboard
    case 'admin_stats':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $result = $api->getSystemStatistics();
        echo json_encode($result);
        break;

    case 'admin_users':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $result = $api->getUserList(100, 0);
        echo json_encode($result);
        break;

    case 'admin_transactions':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $result = $api->getAllTransactions(100, 0);
        echo json_encode($result);
        break;

    case 'admin_system':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $result = $api->getSystemHealth();
        echo json_encode($result);
        break;

    case 'admin_logs':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $result = $api->getActivityLogs(100, 0);
        echo json_encode($result);
        break;

    case 'admin_promote_user':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $userId = $input['user_id'] ?? 0;
        if (!$userId) {
            echo json_encode(['error' => 'User ID is required']);
            break;
        }
        
        $result = $api->promoteUser($userId);
        echo json_encode($result);
        break;

    case 'admin_demote_user':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $userId = $input['user_id'] ?? 0;
        if (!$userId) {
            echo json_encode(['error' => 'User ID is required']);
            break;
        }
        
        // For demo purposes, we'll use the same endpoint but with different logic
        $result = $api->updateUserStatus($userId, false); // Demote by deactivating admin privileges
        echo json_encode($result);
        break;

    case 'admin_activate_user':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $userId = $input['user_id'] ?? 0;
        if (!$userId) {
            echo json_encode(['error' => 'User ID is required']);
            break;
        }
        
        $result = $api->updateUserStatus($userId, true);
        echo json_encode($result);
        break;

    case 'admin_deactivate_user':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        $userId = $input['user_id'] ?? 0;
        if (!$userId) {
            echo json_encode(['error' => 'User ID is required']);
            break;
        }
        
        $result = $api->updateUserStatus($userId, false);
        echo json_encode($result);
        break;

    case 'admin_clear_cache':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        // Simulate cache clearing
        echo json_encode(['success' => true, 'message' => 'Cache cleared successfully']);
        break;    case 'admin_backup_db':
        if (!isset($_SESSION['token']) || !$_SESSION['is_admin']) {
            echo json_encode(['error' => 'Admin access required']);
            break;
        }
        
        // Simulate database backup
        echo json_encode(['success' => true, 'message' => 'Database backup created successfully']);
        break;    case 'create_investment':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $amount = $input['amount'] ?? 0;
        $plan = $input['plan'] ?? '';
        
        if (empty($amount) || empty($plan)) {
            echo json_encode(['error' => 'Amount and plan are required']);
            break;
        }
        
        $result = $api->createInvestment($amount, $plan);
        echo json_encode($result);
        break;

    case 'get_active_investments':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->getActiveInvestments();
        echo json_encode($result);
        break;    case 'get_investment_history':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->getInvestmentHistory();
        echo json_encode($result);
        break;    case 'get_investment_statistics':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->getInvestmentStatistics();
        echo json_encode($result);
        break;

    case 'get_investment_details':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $investmentId = $input['id'] ?? null;
        if (!$investmentId) {
            echo json_encode(['error' => 'Investment ID is required']);
            break;
        }
        
        $result = $api->getInvestmentDetails($investmentId);
        echo json_encode($result);
        break;

    case 'get_investment_earnings':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $investmentId = $input['id'] ?? null;
        if (!$investmentId) {
            echo json_encode(['error' => 'Investment ID is required']);
            break;
        }
        
        $result = $api->getInvestmentEarnings($investmentId);
        echo json_encode($result);
        break;case 'get_balance':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->getBalance();
        echo json_encode($result);
        break;

    case 'get_wallet':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $result = $api->getWallet();
        echo json_encode($result);
        break;

    case 'get_transactions':
        if (!isset($_SESSION['token'])) {
            echo json_encode(['error' => 'Not authenticated']);
            break;
        }
        
        $type = $input['type'] ?? null;
        $limit = $input['limit'] ?? 10;
        $page = $input['page'] ?? 0;
          $result = $api->getTransactions($type, $limit, $page);
        echo json_encode($result);
        break;    case 'get_investment_plans':
        // Load investment plans from backend API
        try {
            require_once 'api_psr4.php';
            $backendApi = new \Simbi\Tls\Frontend\Api\ApiClientPSR4();
            $result = $backendApi->getInvestmentPlans();
            echo json_encode($result);
        } catch (Exception $e) {
            error_log("Error loading investment plans: " . $e->getMessage());
            
            // Fallback to default basic plan
            $fallbackPlans = [
                [
                    'id' => 1,
                    'plan_code' => 'basic',
                    'plan_name' => 'Basic Plan',
                    'daily_rate' => 0.0167,
                    'duration' => 30,
                    'min_amount' => 600,
                    'max_amount' => 10000,
                    'description' => 'Our beginner-friendly investment plan with consistent daily returns',
                    'features' => [
                        '1.67% daily simple interest',
                        'Non-compounding returns',
                        '30-day investment duration',
                        'Low risk profile',
                        'Perfect for beginners'
                    ],
                    'is_active' => true,
                    'is_featured' => true
                ]
            ];
            
            echo json_encode([
                'success' => true,
                'plans' => $fallbackPlans,
                'message' => 'Fallback plans loaded (API error)'
            ]);
        }
        break;

    case 'get_system_configuration':
        // System configuration endpoint (public, no auth required)
        $config = [
            'deposit' => [
                'minimum_amount' => 1.0,
                'maximum_amount' => 1000000.0,
                'currency' => 'USDT',
                'network' => 'TRC20'
            ],
            'investment' => [
                'minimum_amounts' => [
                    'basic' => 600.0,
                    'premium' => 2000.0,
                    'vip' => 5000.0
                ]
            ]
        ];
        
        echo json_encode([
            'success' => true,
            'data' => $config
        ]);
        break;

    default:
        echo json_encode(['error' => 'Invalid action']);
        break;
}
?>
