<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Width Fix Verification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-section h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 5px;
            font-size: 14px;
            border-left: 4px solid #ddd;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-left-color: #28a745; 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545; 
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border-left-color: #17a2b8; 
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-top: 10px;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CSS Width Fix Verification Test</h1>
        <p>Testing the fixed CSS width issues on both investment and make_investment pages.</p>

        <!-- Test Summary -->
        <div class="test-section">
            <h3>🛠️ Fix Summary</h3>
            <div class="result info">
                <strong>Issue:</strong> Both invest.php and make_investment.php had width-related layout problems affecting components like warning cards, forms, and containers.
            </div>
            <div class="result success">
                <strong>Solution Applied:</strong>
                <ul style="margin: 8px 0 0 20px;">
                    <li>Added dedicated CSS for `.invest-page` layout and width constraints</li>
                    <li>Fixed `.warning-card` and container width issues</li>
                    <li>Added responsive design for mobile/tablet/desktop</li>
                    <li>Implemented proper box-sizing and overflow prevention</li>
                    <li>Enhanced both pages with proper container max-widths</li>
                </ul>
            </div>
        </div>

        <!-- CSS Changes Applied -->
        <div class="test-section">
            <h3>📝 CSS Changes Applied</h3>
            <div class="result info">
                <strong>Files Modified:</strong> <code>frontend/user/css/dashboard.css</code>
            </div>
            <div class="grid">
                <div>
                    <h4>✅ Invest Page Fixes:</h4>
                    <ul>
                        <li>Added `.invest-page` layout styles</li>
                        <li>Fixed page header and balance overview</li>
                        <li>Proper card width constraints</li>
                        <li>Investment form container fixes</li>
                        <li>Warning card width issues resolved</li>
                        <li>Responsive design for all screen sizes</li>
                    </ul>
                </div>
                <div>
                    <h4>✅ Make Investment Page Fixes:</h4>
                    <ul>
                        <li>Enhanced existing styles with width constraints</li>
                        <li>Fixed modal and form container widths</li>
                        <li>Improved plan cards and grid layouts</li>
                        <li>Better mobile responsiveness</li>
                        <li>Low funds modal width fixes</li>
                        <li>Investment grid overflow prevention</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Live Page Tests -->
        <div class="test-section">
            <h3>🔬 Live Page Tests</h3>
            <div class="grid">
                <div>
                    <h4>Test Invest Page:</h4>
                    <button class="btn" onclick="loadInvestPage()">Load Invest Page</button>
                    <div id="investResults"></div>
                    <div id="investFrame" class="iframe-container" style="display: none;"></div>
                </div>
                <div>
                    <h4>Test Make Investment Page:</h4>
                    <button class="btn" onclick="loadMakeInvestmentPage()">Load Make Investment Page</button>
                    <div id="makeInvestResults"></div>
                    <div id="makeInvestFrame" class="iframe-container" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="test-section">
            <h3>🔧 Technical Implementation</h3>
            <div class="result info">
                <strong>Key CSS Classes Added/Fixed:</strong>
                <div style="margin-top: 10px;">
                    <code>.invest-page</code> - Main page layout<br>
                    <code>.invest-page .card</code> - Card width constraints<br>
                    <code>.invest-page .warning-card</code> - Warning card fixes<br>
                    <code>.make-investment-page .warning-card</code> - Enhanced width controls<br>
                    <code>Global width fixes</code> - Box-sizing and overflow prevention<br>
                    <code>Responsive breakpoints</code> - Mobile, tablet, desktop optimizations
                </div>
            </div>
        </div>

        <!-- Expected Results -->
        <div class="test-section">
            <h3>🎯 Expected Results</h3>
            <div class="result success">
                <strong>Both pages should now display:</strong>
                <ul style="margin: 8px 0 0 20px;">
                    <li>✅ Properly constrained container widths</li>
                    <li>✅ No horizontal overflow or scrollbars</li>
                    <li>✅ Responsive layout on all screen sizes</li>
                    <li>✅ Warning cards with appropriate sizing</li>
                    <li>✅ Forms and inputs that fit within containers</li>
                    <li>✅ Investment cards and grids that align properly</li>
                    <li>✅ Modals that don't exceed viewport width</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.innerHTML = message;
            container.appendChild(result);
            return result;
        }

        function loadInvestPage() {
            const container = document.getElementById('investResults');
            const frameContainer = document.getElementById('investFrame');
            container.innerHTML = '';
            frameContainer.innerHTML = '';
            frameContainer.style.display = 'block';

            showResult('investResults', '🔄 Loading Invest Page...', 'info');

            const iframe = document.createElement('iframe');
            iframe.src = '/user/invest.php';
            
            iframe.onload = function() {
                showResult('investResults', '✅ Invest Page: Loaded successfully', 'success');
                showResult('investResults', 'ℹ️ Check for proper layout, no horizontal overflow, and responsive design', 'info');
                
                // Try to analyze the page
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        
                        if (iframeDoc.querySelector('.invest-page')) {
                            showResult('investResults', '✅ Page: .invest-page container found', 'success');
                        }
                        
                        if (iframeDoc.querySelector('.warning-card')) {
                            showResult('investResults', '✅ Warning: .warning-card element found', 'success');
                        }
                        
                        const cards = iframeDoc.querySelectorAll('.card');
                        if (cards.length > 0) {
                            showResult('investResults', `✅ Layout: ${cards.length} card elements rendered`, 'success');
                        }

                    } catch (error) {
                        showResult('investResults', '⚠️ Cross-origin restrictions prevent detailed analysis', 'warning');
                    }
                }, 2000);
            };

            iframe.onerror = function() {
                showResult('investResults', '❌ Failed to load Invest Page', 'error');
            };

            frameContainer.appendChild(iframe);
        }

        function loadMakeInvestmentPage() {
            const container = document.getElementById('makeInvestResults');
            const frameContainer = document.getElementById('makeInvestFrame');
            container.innerHTML = '';
            frameContainer.innerHTML = '';
            frameContainer.style.display = 'block';

            showResult('makeInvestResults', '🔄 Loading Make Investment Page...', 'info');

            const iframe = document.createElement('iframe');
            iframe.src = '/user/make_investment.php';
            
            iframe.onload = function() {
                showResult('makeInvestResults', '✅ Make Investment Page: Loaded successfully', 'success');
                showResult('makeInvestResults', 'ℹ️ Check for proper modal widths, form layouts, and responsive behavior', 'info');
                
                // Try to analyze the page
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        
                        if (iframeDoc.querySelector('.make-investment-page')) {
                            showResult('makeInvestResults', '✅ Page: .make-investment-page container found', 'success');
                        }
                        
                        if (iframeDoc.querySelector('#investmentPlansContainer')) {
                            showResult('makeInvestResults', '✅ Plans: Investment plans container found', 'success');
                        }
                        
                        if (iframeDoc.querySelector('#investmentFormCard')) {
                            showResult('makeInvestResults', '✅ Form: Investment form card found', 'success');
                        }

                        const planCards = iframeDoc.querySelectorAll('.plan-card');
                        if (planCards.length > 0) {
                            showResult('makeInvestResults', `✅ Layout: ${planCards.length} plan cards rendered`, 'success');
                        }

                    } catch (error) {
                        showResult('makeInvestResults', '⚠️ Cross-origin restrictions prevent detailed analysis', 'warning');
                    }
                }, 2000);
            };

            iframe.onerror = function() {
                showResult('makeInvestResults', '❌ Failed to load Make Investment Page', 'error');
            };

            frameContainer.appendChild(iframe);
        }

        // Auto-show initial status
        document.addEventListener('DOMContentLoaded', function() {
            console.log('CSS Width Fix Verification Test loaded');
        });
    </script>
</body>
</html>
