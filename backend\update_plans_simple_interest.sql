-- Update Investment Plans to Simple Interest 1.67% Daily
-- This script updates all investment plans to use non-compounding simple interest of 1.67% daily

-- Check current plan rates before update
SELECT plan_code, plan_name, daily_rate, (daily_rate * 100) as percentage_rate 
FROM investment_plans 
ORDER BY display_order;

-- Update all plans to 1.67% daily rate (0.0167 decimal)
-- Non-compounding simple interest calculation
UPDATE investment_plans 
SET 
    daily_rate = 0.0167,
    description = CASE 
        WHEN plan_code = 'basic' THEN 'Perfect for beginners with steady 1.67% daily simple interest returns'
        WHEN plan_code = 'premium' THEN 'Enhanced returns with 1.67% daily simple interest for experienced investors'
        WHEN plan_code = 'vip' THEN 'Maximum returns with 1.67% daily simple interest for high-value investors'
        ELSE description
    END,
    features = CASE 
        WHEN plan_code = 'basic' THEN JSON_ARRAY('1.67% daily simple interest', '30-day duration', 'Non-compounding', 'Low risk', 'Beginner friendly')
        WHEN plan_code = 'premium' THEN JSON_ARRAY('1.67% daily simple interest', '30-day duration', 'Non-compounding', 'Medium risk', 'Advanced features')
        WHEN plan_code = 'vip' THEN JSON_ARRAY('1.67% daily simple interest', '30-day duration', 'Non-compounding', 'Premium service', 'Maximum profits')
        ELSE features
    END,
    updated_at = CURRENT_TIMESTAMP
WHERE plan_code IN ('basic', 'premium', 'vip');

-- Verify the updates
SELECT 
    plan_code, 
    plan_name, 
    daily_rate, 
    (daily_rate * 100) as percentage_rate,
    (daily_rate * duration * 100) as total_return_percentage,
    description,
    features,
    is_active
FROM investment_plans 
ORDER BY display_order;

-- Calculate example returns for different investment amounts
-- Simple Interest Formula: Final Amount = Principal + (Principal × Rate × Time)
-- Daily returns = Principal × 0.0167

-- Examples:
-- Investment: 1000 USDT
-- Daily return: 1000 × 0.0167 = 16.70 USDT per day
-- Total return after 30 days: 1000 × 0.0167 × 30 = 501 USDT
-- Total amount after 30 days: 1000 + 501 = 1501 USDT

SELECT 
    'Example Calculations' as info,
    '1000 USDT investment' as scenario,
    '16.70 USDT' as daily_return,
    '501 USDT' as total_interest_30_days,
    '1501 USDT' as final_amount_30_days;
