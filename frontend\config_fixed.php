<?php
/**
 * Frontend Configuration - PSR-4 Compatible Entry Point
 * 
 * This file provides backward compatibility while using PSR-4 autoloading
 */

// Include PSR-4 autoloader
require_once __DIR__ . '/../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig as PSR4Config;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize PSR-4 configuration
PSR4Config::init();

/**
 * Backward compatible FrontendConfig class
 * Delegates to PSR-4 FrontendConfig for actual functionality
 */
class FrontendConfig {
    // API Configuration
    const API_BASE_URL = 'http://localhost:8000';
    const API_TIMEOUT = 30;
    
    // Session Configuration
    const SESSION_LIFETIME = 3600;
    const SESSION_NAME = 'tls_session';
    
    // Security Configuration
    const ENABLE_HTTPS_ONLY = false;
    const SECURE_COOKIES = false;
    
    // Application Configuration
    const APP_NAME = 'TLS Crypto Wallet';
    const APP_VERSION = '1.0.0';
    const DEFAULT_CURRENCY = 'TRX';
    
    // Pagination Settings
    const DEFAULT_PAGE_SIZE = 20;
    const MAX_PAGE_SIZE = 100;
    
    // File Upload Settings
    const MAX_UPLOAD_SIZE = 5242880;
    const ALLOWED_UPLOAD_TYPES = ['jpg', 'jpeg', 'png', 'gif'];
    
    // Debug Settings
    const DEBUG_MODE = true;
    const LOG_ERRORS = true;

    /**
     * Get API base URL
     */
    public static function getApiUrl() {
        return PSR4Config::get('API_BASE_URL', self::API_BASE_URL);
    }
    
    /**
     * Get session configuration
     */
    public static function getSessionConfig() {
        return [
            'lifetime' => PSR4Config::get('SESSION_LIFETIME', self::SESSION_LIFETIME),
            'name' => PSR4Config::get('SESSION_NAME', self::SESSION_NAME),
            'secure' => PSR4Config::get('SECURE_COOKIES', self::SECURE_COOKIES),
            'httponly' => true,
            'samesite' => 'Lax'
        ];
    }
    
    /**
     * Initialize session (backward compatibility)
     */
    public static function initSession() {
        SessionService::init();
    }
    
    /**
     * Check if user is authenticated (backward compatibility)
     */
    public static function isAuthenticated() {
        return SessionService::isAuthenticated();
    }
    
    /**
     * Get current user (backward compatibility)
     */
    public static function getCurrentUser() {
        return SessionService::getCurrentUser();
    }
    
    /**
     * Check if user is admin (backward compatibility)
     */
    public static function isAdmin() {
        return SessionService::isAdmin();
    }
    
    /**
     * Delegate all other calls to PSR4Config
     */
    public static function __callStatic($name, $arguments) {
        if (method_exists(PSR4Config::class, $name)) {
            return PSR4Config::$name(...$arguments);
        }
        throw new BadMethodCallException("Method $name does not exist");
    }
}
