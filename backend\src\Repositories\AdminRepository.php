<?php

namespace Simbi\Tls\Repositories;

use PDO;
use Exception;

class AdminRepository
{
    private PDO $pdo;

    public function __construct(PDO $pdo)
    {
        $this->pdo = $pdo;
    }

    public function logActivity(int $adminUserId, string $action, string $targetType, 
                               string $targetId = null, array $details = null, 
                               string $ipAddress = null, string $userAgent = null): ?int
    {
        try {
            $sql = "INSERT INTO admin_logs (
                admin_user_id, action, target_type, target_id, details, ip_address, user_agent
            ) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->pdo->prepare($sql);
            $success = $stmt->execute([
                $adminUserId,
                $action,
                $targetType,
                $targetId,
                $details ? json_encode($details) : null,
                $ipAddress,
                $userAgent
            ]);

            return $success ? $this->pdo->lastInsertId() : null;
        } catch (Exception $e) {
            error_log("Admin log creation error: " . $e->getMessage());
            return null;
        }
    }

    public function getActivityLogs(int $limit = 100, int $offset = 0, array $filters = []): array
    {
        try {
            $whereClause = [];
            $params = [];

            if (!empty($filters['admin_user_id'])) {
                $whereClause[] = "al.admin_user_id = ?";
                $params[] = $filters['admin_user_id'];
            }

            if (!empty($filters['action'])) {
                $whereClause[] = "al.action = ?";
                $params[] = $filters['action'];
            }

            if (!empty($filters['target_type'])) {
                $whereClause[] = "al.target_type = ?";
                $params[] = $filters['target_type'];
            }

            if (!empty($filters['date_from'])) {
                $whereClause[] = "al.created_at >= ?";
                $params[] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $whereClause[] = "al.created_at <= ?";
                $params[] = $filters['date_to'];
            }

            $whereSQL = empty($whereClause) ? "" : "WHERE " . implode(" AND ", $whereClause);
            
            $sql = "SELECT al.*, u.email as admin_email 
                    FROM admin_logs al
                    JOIN users u ON al.admin_user_id = u.id
                    $whereSQL
                    ORDER BY al.created_at DESC
                    LIMIT ? OFFSET ?";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Decode JSON details for each log
            foreach ($logs as &$log) {
                $log['details'] = $log['details'] ? json_decode($log['details'], true) : null;
            }
            
            return $logs;
        } catch (Exception $e) {
            error_log("Admin logs fetch error: " . $e->getMessage());
            return [];
        }
    }

    public function getSystemStatistics(): array
    {
        try {
            // Get user statistics
            $userStats = $this->pdo->query("
                SELECT 
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_users,
                    COUNT(CASE WHEN is_admin = TRUE THEN 1 END) as admin_users,
                    COUNT(CASE WHEN email_verified = TRUE THEN 1 END) as verified_users,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as users_last_30_days
                FROM users
            ")->fetch(PDO::FETCH_ASSOC);

            // Get wallet statistics
            $walletStats = $this->pdo->query("
                SELECT 
                    COUNT(*) as total_wallets,
                    SUM(balance) as total_balance,
                    AVG(balance) as average_balance,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as wallets_last_30_days
                FROM wallets
            ")->fetch(PDO::FETCH_ASSOC);

            // Get transaction statistics
            $transactionStats = $this->pdo->query("
                SELECT 
                    COUNT(*) as total_transactions,
                    SUM(amount) as total_volume,
                    AVG(amount) as average_amount,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
                    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_transactions,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as transactions_last_30_days            FROM transactions
            ")->fetch(PDO::FETCH_ASSOC);

            return [
                'users' => $userStats ?: [],
                'wallets' => $walletStats ?: [],
                'transactions' => $transactionStats ?: [],
                'system' => [
                    'php_version' => phpversion(),
                    'memory_usage' => memory_get_usage(true),
                    'peak_memory' => memory_get_peak_usage(true),
                    'uptime' => time() - $_SERVER['REQUEST_TIME'],
                ]
            ];
        } catch (Exception $e) {
            error_log("System statistics error: " . $e->getMessage());
            return [];
        }
    }

    public function getUserList(int $limit = 50, int $offset = 0, array $filters = []): array
    {
        try {
            $whereClause = [];
            $params = [];

            if (!empty($filters['is_active'])) {
                $whereClause[] = "is_active = ?";
                $params[] = $filters['is_active'] === 'true' ? 1 : 0;
            }

            if (!empty($filters['is_admin'])) {
                $whereClause[] = "is_admin = ?";
                $params[] = $filters['is_admin'] === 'true' ? 1 : 0;
            }

            if (!empty($filters['email_search'])) {
                $whereClause[] = "email LIKE ?";
                $params[] = '%' . $filters['email_search'] . '%';
            }

            $whereSQL = empty($whereClause) ? "" : "WHERE " . implode(" AND ", $whereClause);
            
            $sql = "SELECT u.id, u.email, u.is_admin, u.is_active, u.email_verified, 
                           u.created_at, u.updated_at,
                           COUNT(w.id) as wallet_count,
                           COALESCE(SUM(w.balance), 0) as total_balance
                    FROM users u
                    LEFT JOIN wallets w ON u.id = w.user_id
                    $whereSQL
                    GROUP BY u.id
                    ORDER BY u.created_at DESC
                    LIMIT ? OFFSET ?";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("User list fetch error: " . $e->getMessage());
            return [];
        }
    }

    public function updateUserStatus(int $userId, bool $isActive, int $adminUserId): bool
    {
        try {
            $this->pdo->beginTransaction();

            // Update user status
            $sql = "UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $success = $stmt->execute([$isActive ? 1 : 0, $userId]);

            if ($success) {
                // Log admin activity
                $this->logActivity(
                    $adminUserId,
                    $isActive ? 'user_activated' : 'user_deactivated',
                    'user',
                    (string)$userId,
                    ['is_active' => $isActive]
                );
            }

            $this->pdo->commit();
            return $success;
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("User status update error: " . $e->getMessage());
            return false;
        }
    }

    public function promoteToAdmin(int $userId, int $adminUserId): bool
    {
        try {
            $this->pdo->beginTransaction();

            // Update user to admin
            $sql = "UPDATE users SET is_admin = TRUE, updated_at = NOW() WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $success = $stmt->execute([$userId]);

            if ($success) {
                // Log admin activity
                $this->logActivity(
                    $adminUserId,
                    'user_promoted_to_admin',
                    'user',
                    (string)$userId
                );
            }

            $this->pdo->commit();
            return $success;
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("User promotion error: " . $e->getMessage());
            return false;
        }
    }
}
