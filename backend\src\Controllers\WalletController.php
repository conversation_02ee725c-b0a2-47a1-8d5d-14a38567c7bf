<?php

namespace Simbi\Tls\Controllers;

use Simbi\Tls\Repositories\WalletRepository;
use Simbi\Tls\Services\TronService;
use Simbi\Tls\Services\TronGridService;
use Simbi\Tls\Services\WalletService;
use Exception;

class WalletController
{
    private WalletRepository $walletRepository;
    private TronService $tronService;
    private TronGridService $tronGridService;
    private WalletService $walletService;

    public function __construct(WalletRepository $walletRepository, TronService $tronService, TronGridService $tronGridService = null, WalletService $walletService = null)
    {
        $this->walletRepository = $walletRepository;
        $this->tronService = $tronService;
        $this->tronGridService = $tronGridService ?: new TronGridService();
        $this->walletService = $walletService ?: new WalletService();
    }
    
    public function createWallet(array $user): array
    {
        $userId = $user['id'];

        try {
            // Check if wallet already exists for this user
            $existingWallet = $this->walletRepository->findByUserId($userId);
            if ($existingWallet) {
                return ['error' => 'Wallet already exists for this user', 'code' => 409];
            }            // Generate new wallet
            $account = $this->tronService->generateAddress();
            
            // Save to database
            $success = $this->walletRepository->create(
                $userId,
                $account['address'],
                $account['hexAddress'],
                $account['privateKey']
            );

            if (!$success) {
                return ['error' => 'Failed to save wallet to database', 'code' => 500];
            }            return [
                'success' => true,
                'address' => $account['address'],
                'hex_address' => $account['hexAddress'],
                'message' => 'Wallet created successfully'
            ];
        } catch (Exception $e) {
            error_log("Create wallet error: " . $e->getMessage());
            return ['error' => $e->getMessage(), 'code' => 500];
        }
    }    
    
    public function sweepFunds(array $data, array $user): array
    {
        $userId = $user['id'];

        try {
            // Find wallet
            $wallet = $this->walletRepository->findByUserId($userId);

            if (!$wallet) {
                return ['error' => 'Wallet not found', 'code' => 404];
            }

            // Sweep funds
            $result = $this->tronService->sweepFunds(
                $wallet['private_key'],
                $wallet['address']
            );

            return array_merge($result, ['success' => true]);
        } catch (Exception $e) {
            error_log("Sweep funds error: " . $e->getMessage());
            return ['error' => $e->getMessage(), 'code' => 500];
        }
    }    
    
    public function withdraw(array $data, array $user): array
    {
        $to = $data['to'] ?? null;
        $amount = $data['amount'] ?? null;
        $userId = $user['id'];

        if (!$to || !$amount) {
            return ['error' => 'Missing required fields: to, amount', 'code' => 400];
        }

        // Validate amount
        if (!is_numeric($amount) || (float)$amount <= 0) {
            return ['error' => 'Invalid amount', 'code' => 400];
        }

        // Validate address
        if (!$this->tronService->validateAddress($to)) {
            return ['error' => 'Invalid TRON address', 'code' => 400];
        }

        try {
            // Find user's wallet
            $wallet = $this->walletRepository->findByUserId($userId);
            if (!$wallet) {
                return ['error' => 'Wallet not found', 'code' => 404];
            }

            $result = $this->tronService->withdraw($to, $amount, $wallet['private_key']);
            return array_merge($result, ['success' => true]);
        } catch (Exception $e) {
            error_log("Withdraw error: " . $e->getMessage());
            return ['error' => $e->getMessage(), 'code' => 500];
        }
    }        public function getBalance(array $user): array
    {
        $userId = $user['id'];        try {
            $wallet = $this->walletRepository->findByUserId($userId);
            if (!$wallet) {
                return ['error' => 'Wallet not found', 'code' => 404];
            }

            // Get available balance using WalletService (wallet balance - active investments)
            $balance = $this->walletService->getBalance($userId);
            
            return [
                'success' => true,
                'address' => $wallet['address'],
                'balance' => $balance,
                'balance_formatted' => number_format((float)$balance, 2, '.', '') // Format to 2 decimal places for USDT
            ];
        } catch (Exception $e) {
            error_log("Get balance error: " . $e->getMessage());
            return ['error' => $e->getMessage(), 'code' => 500];
        }
    }

    public function getWallet(array $user): array
    {
        $userId = $user['id'];

        try {
            $wallet = $this->walletRepository->findByUserId($userId);
            if (!$wallet) {
                return [
                    'success' => false,
                    'error' => 'Wallet not found',
                    'code' => 404
                ];
            }

            // Get balance
            $balance = $wallet['balance'] ?? '0.000000';
              return [
                'success' => true,
                'wallet' => [
                    'id' => $wallet['id'],
                    'address' => $wallet['address'],
                    'hex_address' => $wallet['hex_address'] ?? '',
                    'balance' => $balance,
                    'balance_formatted' => number_format((float)$balance, 6, '.', ''),
                    'created_at' => $wallet['created_at']
                ]
            ];
        } catch (Exception $e) {
            error_log("Get wallet error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'code' => 500
            ];
        }
    }

    public function getLiveBalance(array $data, array $user): array
    {
        $address = $data['address'] ?? null;
        
        if (!$address) {
            return ['error' => 'Wallet address is required', 'code' => 400];
        }

        // Validate that the address belongs to the authenticated user
        $userId = $user['id'];
        $wallet = $this->walletRepository->findByUserId($userId);
        
        if (!$wallet || $wallet['address'] !== $address) {
            return ['error' => 'Address does not belong to authenticated user', 'code' => 403];
        }

        try {
            // Get live balance from TronGrid
            $balanceResult = $this->tronGridService->getUSDTBalance($address);
            
            if (!$balanceResult['success']) {
                // Fallback to TronService if TronGrid fails
                error_log("TronGrid balance check failed: " . ($balanceResult['error'] ?? 'Unknown error'));
                $fallbackBalance = $this->tronService->getUSDTBalance($address);
                
                return [
                    'success' => true,
                    'balance' => $fallbackBalance,
                    'balance_formatted' => number_format((float)$fallbackBalance, 6, '.', ''),
                    'address' => $address,
                    'source' => 'fallback',
                    'note' => 'Using fallback service due to TronGrid unavailability'
                ];
            }            // Update database records if balance is more than 0
            $balance = (float)$balanceResult['balance'];
            if ($balance > 0) {
                try {
                    // Get wallet details for the address
                    $walletDetails = $this->walletRepository->findByAddress($address);
                    if ($walletDetails) {
                        // Update wallet balance if it's different from database
                        $currentDbBalance = (float)$walletDetails['balance'];
                        if (abs($balance - $currentDbBalance) > 1) { // Tolerance for floating point comparison
                            $this->walletRepository->updateBalance($walletDetails['id'], $balance);
                            error_log("Updated wallet balance for address {$address}: {$currentDbBalance} -> {$balance}");
                        }

                        // Log the balance check activity (using deposit_logs table structure)
                        $this->logBalanceCheck($walletDetails['user_id'], $balance, $address);
                    }
                } catch (Exception $e) {
                    error_log("Error updating database records during balance check: " . $e->getMessage());
                    // Don't fail the response if database updates fail
                }
            }

            return [
                'success' => true,
                'balance' => $balanceResult['balance'],
                'balance_formatted' => number_format((float)$balanceResult['balance'], 6, '.', ''),
                'balance_raw' => $balanceResult['balance_raw'] ?? '0',
                'address' => $address,
                'contract' => $balanceResult['contract'] ?? '',
                'source' => $balanceResult['source'] ?? 'trongrid',
                'timestamp' => date('c')
            ];

        } catch (Exception $e) {
            error_log("Live balance error: " . $e->getMessage());
            
            // Fallback to TronService on any error
            try {
                $fallbackBalance = $this->tronService->getUSDTBalance($address);
                
                return [
                    'success' => true,
                    'balance' => $fallbackBalance,
                    'balance_formatted' => number_format((float)$fallbackBalance, 6, '.', ''),
                    'address' => $address,
                    'source' => 'fallback',
                    'note' => 'TronGrid error: ' . $e->getMessage()
                ];
            } catch (Exception $fallbackError) {
                error_log("Fallback balance error: " . $fallbackError->getMessage());
                return [
                    'success' => false,
                    'error' => 'Unable to retrieve balance from any source',
                    'code' => 500
                ];
            }
        }
    }    /**
     * Log balance check activity to deposit_logs table
     */    private function logBalanceCheck(int $userId, float $balance, string $address): void
    {
        // Log balance check activity to error log only
        // Note: Removed database logging to deposit_logs table due to foreign key constraint
        // that requires transaction_id field, but balance checks are not actual transactions
        if ($balance > 0) {
            error_log("Balance check detected funds: User {$userId}, Balance {$balance} USDT, Address {$address}");
        } else {
            error_log("Balance check (no funds): User {$userId}, Balance {$balance} USDT, Address {$address}");
        }
    }
}
