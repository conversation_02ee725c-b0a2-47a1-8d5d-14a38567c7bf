<?php

namespace Simbi\Tls\Config;

use PDO;
use PDOException;

class Database
{
    private static ?PDO $connection = null;    public static function getConnection(): ?PDO
    {
        if (self::$connection === null) {
            try {
                Config::load();
                
                $host = Config::get('DB_HOST');
                $db = Config::get('DB_NAME');
                $user = Config::get('DB_USER');
                $pass = Config::get('DB_PASS');
                $charset = 'utf8mb4';
                $dsn = "mysql:host=$host;dbname=$db;charset=$charset";
                $options = [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION];

                self::$connection = new PDO($dsn, $user, $pass, $options);
            } catch (PDOException $e) {
                error_log("Database connection failed: " . $e->getMessage());
                return null;
            }
        }

        return self::$connection;
    }
}
