<?php

namespace Simbi\Tls\Services;

use Simbi\Tls\Config\Database;
use Exception;

class ConfigurationService
{
    private $db;

    public function __construct()
    {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    /**
     * Get system configuration values
     */
    public function getSystemConfiguration(): array
    {
        try {
            // For now, return hardcoded values but this could be moved to database
            $config = [
                'deposit' => [
                    'minimum_amount' => 1.0,
                    'maximum_amount' => 1000000.0,
                    'currency' => 'USDT',
                    'network' => 'TRC20'
                ],
                'investment' => [
                    'minimum_amounts' => [
                        'basic' => 600.0,
                        'premium' => 2000.0,
                        'vip' => 5000.0
                    ]
                ]
            ];

            return [
                'success' => true,
                'data' => $config
            ];

        } catch (Exception $e) {
            error_log("Configuration service error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to load configuration'
            ];
        }
    }

    /**
     * Get specific configuration value
     */
    public function getConfigValue(string $key, $default = null)
    {
        try {
            $config = $this->getSystemConfiguration();
            
            if (!$config['success']) {
                return $default;
            }

            $keys = explode('.', $key);
            $value = $config['data'];

            foreach ($keys as $k) {
                if (isset($value[$k])) {
                    $value = $value[$k];
                } else {
                    return $default;
                }
            }

            return $value;

        } catch (Exception $e) {
            error_log("Get config value error: " . $e->getMessage());
            return $default;
        }
    }
}
