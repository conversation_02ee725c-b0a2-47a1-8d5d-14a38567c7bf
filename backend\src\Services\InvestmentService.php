<?php

namespace Simbi\Tls\Services;

use Simbi\Tls\Config\Database;
use Exception;
use DateTime;

class InvestmentService
{
    private $db;

    public function __construct()
    {
        $database = new Database();
        $this->db = $database->getConnection();
    }    public function createInvestment($userId, $amount, $planType, $planDetails)
    {
        try {
            // Get user's wallet ID
            $stmt = $this->db->prepare("SELECT id FROM wallets WHERE user_id = ? LIMIT 1");
            $stmt->execute([$userId]);
            $wallet = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$wallet) {
                error_log("No wallet found for user $userId");
                return ['success' => false, 'message' => 'No wallet found for user'];
            }            // Check balance BEFORE creating investment record to avoid circular dependency
            $walletService = new \Simbi\Tls\Services\WalletService();
            $currentBalance = $walletService->getBalance($userId);
            
            if ($currentBalance < $amount) {
                return ['success' => false, 'message' => 'Insufficient balance for investment'];
            }

            $dailyReturn = $amount * $planDetails['dailyRate'];
            $totalReturn = $dailyReturn * $planDetails['duration'];
            $nextPayout = new DateTime();
            $nextPayout->modify('+1 day');

            $stmt = $this->db->prepare("
                INSERT INTO investments (
                    user_id, wallet_id, amount, plan_type, plan_name, daily_rate, 
                    duration, daily_return, total_return, next_payout, status,
                    investment_type
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?)
            ");

            $result = $stmt->execute([
                $userId,
                $wallet['id'],
                $amount,
                $planType,
                $planDetails['name'],
                $planDetails['dailyRate'],
                $planDetails['duration'],
                $dailyReturn,
                $totalReturn,
                $nextPayout->format('Y-m-d H:i:s'),
                $planType
            ]);

            if ($result) {
                $investmentId = $this->db->lastInsertId();
                  // Deduct amount from user balance (create transaction record)
                // Skip balance check since we already verified it above
                $deductResult = $walletService->deductBalance($userId, $amount, 'investment', true);
                
                if ($deductResult) {
                    return [
                        'success' => true, 
                        'investment_id' => $investmentId,
                        'message' => 'Investment created successfully'
                    ];
                } else {
                    // Rollback the investment if balance deduction failed
                    $this->db->prepare("DELETE FROM investments WHERE id = ?")->execute([$investmentId]);
                    return ['success' => false, 'message' => 'Failed to deduct balance'];
                }
            }

            return ['success' => false, 'message' => 'Failed to create investment'];

        } catch (Exception $e) {
            error_log("Investment creation error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Investment creation failed: ' . $e->getMessage()];
        }
    }    public function getActiveInvestments($userId)
    {
        try {            $stmt = $this->db->prepare("
                SELECT 
                    id,
                    amount,
                    plan_type,
                    plan_name,
                    daily_rate,
                    duration,
                    daily_return,
                    total_return,
                    total_earned,
                    days_elapsed,
                    status,
                    created_at
                FROM investments 
                WHERE user_id = ? AND status = 'active'
                ORDER BY created_at DESC
            ");

            $stmt->execute([$userId]);
            $investments = $stmt->fetchAll(\PDO::FETCH_ASSOC);
              // Apply the same calculation logic as getInvestmentDetails
            foreach ($investments as &$investment) {
                $investment['amount'] = floatval($investment['amount']);
                $investment['daily_rate'] = floatval($investment['daily_rate']);
                $investment['daily_return'] = floatval($investment['daily_return']);
                $investment['total_return'] = floatval($investment['total_return']);
                $investment['duration'] = intval($investment['duration']);
                
                // Calculate based on actual dates:
                $createdDate = new DateTime($investment['created_at']);
                $currentDate = new DateTime();
                
                // Current Progress = current_date - start_date (in days)
                $daysDiff = $currentDate->diff($createdDate);
                $currentProgress = $daysDiff->days;
                
                // Days Remaining = 30 - Current Progress
                $daysRemaining = max(0, 30 - $currentProgress);
                
                // Total Earned = daily_return * Current Progress
                $totalEarned = $investment['daily_return'] * $currentProgress;
                
                // Update calculated values
                $investment['days_elapsed'] = $currentProgress;
                $investment['total_earned'] = $totalEarned;
                $investment['days_remaining'] = $daysRemaining;
                
                // Use fixed 30-day duration for calculations
                $fixedDuration = 30;
                $investment['duration'] = $fixedDuration;
                $investment['progress'] = ($currentProgress / $fixedDuration) * 100;
                $investment['is_completed'] = $currentProgress >= $fixedDuration;
            }
            
            return $investments;

        } catch (Exception $e) {
            error_log("Get active investments error: " . $e->getMessage());
            return [];
        }
    }

    public function getInvestmentHistory($userId)
    {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    id,
                    amount,
                    plan_type,
                    plan_name,
                    daily_rate,
                    duration,
                    daily_return,
                    total_return,
                    total_earned,
                    days_elapsed,
                    status,
                    created_at,
                    completed_at
                FROM investments 
                WHERE user_id = ? AND status IN ('completed', 'cancelled')
                ORDER BY created_at DESC
                LIMIT 50
            ");
            
            $stmt->execute([$userId]);
            $investments = $stmt->fetchAll(\PDO::FETCH_ASSOC);            // Format the data
            foreach ($investments as &$investment) {
                $investment['amount'] = floatval($investment['amount']);
                $investment['daily_rate'] = floatval($investment['daily_rate']);
                $investment['daily_return'] = floatval($investment['daily_return']);
                $investment['total_return'] = floatval($investment['total_return']);
                $investment['duration'] = intval($investment['duration']);
                
                // Calculate based on actual dates for historical data:
                $createdDate = new DateTime($investment['created_at']);
                $endDate = $investment['completed_at'] ? new DateTime($investment['completed_at']) : new DateTime();
                
                // Current Progress = end_date - start_date (in days)
                $daysDiff = $endDate->diff($createdDate);
                $currentProgress = $daysDiff->days;
                
                // Total Earned = daily_return * Current Progress
                $totalEarned = $investment['daily_return'] * $currentProgress;
                
                // Update calculated values
                $investment['days_elapsed'] = $currentProgress;
                $investment['total_earned'] = $totalEarned;
                
                // Use fixed 30-day duration for calculations
                $fixedDuration = 30;
                $investment['duration'] = $fixedDuration;
                $investment['progress'] = ($currentProgress / $fixedDuration) * 100;
            }
            
            return [
                'success' => true,
                'data' => $investments
            ];
            
        } catch (Exception $e) {
            error_log("Error fetching investment history: " . $e->getMessage());
            throw new Exception("Failed to fetch investment history");
        }
    }

    public function updateInvestmentProgress($investmentId, $daysElapsed, $totalEarned)
    {
        try {
            $stmt = $this->db->prepare("
                UPDATE investments 
                SET days_elapsed = ?, total_earned = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");

            return $stmt->execute([$daysElapsed, $totalEarned, $investmentId]);

        } catch (Exception $e) {
            error_log("Update investment progress error: " . $e->getMessage());
            return false;
        }
    }

    public function completeInvestment($investmentId, $totalReturn)
    {
        try {
            $stmt = $this->db->prepare("
                UPDATE investments 
                SET status = 'completed', total_return = ?, completed_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");

            return $stmt->execute([$totalReturn, $investmentId]);

        } catch (Exception $e) {
            error_log("Complete investment error: " . $e->getMessage());
            return false;
        }
    }

    public function recordPayout($investmentId, $userId, $amount, $payoutDate)
    {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO investment_payouts (investment_id, user_id, amount, payout_date)
                VALUES (?, ?, ?, ?)
            ");

            return $stmt->execute([$investmentId, $userId, $amount, $payoutDate]);

        } catch (Exception $e) {
            error_log("Record payout error: " . $e->getMessage());
            return false;
        }
    }

    public function getInvestmentsDueForPayout()
    {
        try {
            $now = new DateTime();
            $stmt = $this->db->prepare("
                SELECT * FROM investments 
                WHERE status = 'active' AND next_payout <= ?
            ");

            $stmt->execute([$now->format('Y-m-d H:i:s')]);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Get investments due for payout error: " . $e->getMessage());
            return [];
        }
    }

    public function updateNextPayout($investmentId, $nextPayoutDate)
    {
        try {
            $stmt = $this->db->prepare("
                UPDATE investments 
                SET next_payout = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");

            return $stmt->execute([$nextPayoutDate, $investmentId]);

        } catch (Exception $e) {
            error_log("Update next payout error: " . $e->getMessage());
            return false;
        }
    }

    public function getInvestmentPlans()
    {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    id,
                    plan_code,
                    plan_name,
                    daily_rate,
                    duration,
                    min_amount,
                    max_amount,
                    description,
                    features,
                    is_active,
                    is_featured,
                    display_order
                FROM investment_plans 
                ORDER BY display_order ASC, plan_name ASC
            ");
            
            $stmt->execute();
            $plans = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Process features JSON
            foreach ($plans as &$plan) {
                if ($plan['features']) {
                    $plan['features'] = json_decode($plan['features'], true);
                }
                
                // Convert decimal fields to proper format
                $plan['daily_rate'] = floatval($plan['daily_rate']);
                $plan['min_amount'] = floatval($plan['min_amount']);
                $plan['max_amount'] = $plan['max_amount'] ? floatval($plan['max_amount']) : null;
                $plan['is_active'] = (bool)$plan['is_active'];
                $plan['is_featured'] = (bool)$plan['is_featured'];
            }
            
            return [
                'success' => true,
                'data' => $plans
            ];
            
        } catch (Exception $e) {
            error_log("Error fetching investment plans: " . $e->getMessage());            throw new Exception("Failed to fetch investment plans");
        }
    }

    public function getInvestmentStatistics($userId)
    {
        try {
            $statistics = [
                'total_invested' => 0,
                'total_earned' => 0,
                'active_investments' => 0,
                'completed_investments' => 0
            ];

            // Get total invested amount
            $stmt = $this->db->prepare("
                SELECT COALESCE(SUM(amount), 0) as total_invested
                FROM investments 
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            $statistics['total_invested'] = floatval($result['total_invested']);

            // Get total earned amount
            $stmt = $this->db->prepare("
                SELECT COALESCE(SUM(total_earned), 0) as total_earned
                FROM investments 
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            $statistics['total_earned'] = floatval($result['total_earned']);

            // Get active investments count
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as active_count
                FROM investments 
                WHERE user_id = ? AND status = 'active'
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            $statistics['active_investments'] = intval($result['active_count']);

            // Get completed investments count
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as completed_count
                FROM investments 
                WHERE user_id = ? AND status = 'completed'
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            $statistics['completed_investments'] = intval($result['completed_count']);

            return $statistics;        } catch (Exception $e) {
            error_log("Error fetching investment statistics: " . $e->getMessage());
            throw new Exception("Failed to fetch investment statistics");
        }
    }    public function getInvestmentDetails($investmentId, $userId)
    {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    id,
                    amount,
                    plan_type,
                    plan_name,
                    daily_rate,
                    duration,
                    daily_return,
                    total_return,
                    total_earned,
                    days_elapsed,
                    status,
                    created_at,
                    next_payout,
                    completed_at
                FROM investments 
                WHERE id = ? AND user_id = ?
                LIMIT 1
            ");

            $stmt->execute([$investmentId, $userId]);
            $investment = $stmt->fetch(\PDO::FETCH_ASSOC);

            if ($investment) {
                // Format the data
                $investment['amount'] = floatval($investment['amount']);
                $investment['daily_rate'] = floatval($investment['daily_rate']);
                $investment['daily_return'] = floatval($investment['daily_return']);
                $investment['total_return'] = floatval($investment['total_return']);
                $investment['duration'] = intval($investment['duration']);
                
                // Calculate based on requirements using actual dates:
                $createdDate = new DateTime($investment['created_at']);
                $currentDate = new DateTime();
                
                // Current Progress = current_date - start_date (in days)
                $daysDiff = $currentDate->diff($createdDate);
                $currentProgress = $daysDiff->days;
                
                // Days Remaining = 30 - Current Progress
                $daysRemaining = max(0, 30 - $currentProgress);
                
                // Total Earned = daily_return * Current Progress
                $totalEarned = $investment['daily_return'] * $currentProgress;
                
                // Update calculated values
                $investment['days_elapsed'] = $currentProgress;
                $investment['total_earned'] = $totalEarned;
                $investment['days_remaining'] = $daysRemaining;
                
                // Fixed 30-day duration for all plans
                $fixedDuration = 30;
                $investment['duration'] = $fixedDuration;
                
                // Completion date = creation date + 30 days
                $completionDate = clone $createdDate;
                $completionDate->modify('+30 days');
                $investment['completion_date'] = $completionDate->format('Y-m-d H:i:s');
                
                // Add computed fields based on fixed 30-day duration
                $investment['progress_percentage'] = ($currentProgress / $fixedDuration) * 100;
                $investment['is_completed'] = $currentProgress >= $fixedDuration;
                
                // Calculate last payout date (for timer purposes)
                $lastPayoutDate = clone $createdDate;
                $lastPayoutDate->modify('+' . ($currentProgress - 1) . ' days');
                $investment['last_payout'] = $lastPayoutDate->format('Y-m-d H:i:s');
            }

            return $investment;

        } catch (Exception $e) {
            error_log("Error fetching investment details: " . $e->getMessage());
            return null;
        }
    }    public function getInvestmentEarnings($investmentId, $userId)
    {
        try {
            // First verify the investment belongs to the user
            $stmt = $this->db->prepare("
                SELECT id, amount, daily_return, created_at 
                FROM investments 
                WHERE id = ? AND user_id = ?
                LIMIT 1
            ");
            $stmt->execute([$investmentId, $userId]);
            $investment = $stmt->fetch(\PDO::FETCH_ASSOC);

            if (!$investment) {
                return [];
            }

            // Calculate current progress based on actual dates
            $createdDate = new DateTime($investment['created_at']);
            $currentDate = new DateTime();
            $daysDiff = $currentDate->diff($createdDate);
            $currentProgress = $daysDiff->days;

            // Generate earnings history based on actual days elapsed
            $earnings = [];
            $runningTotal = 0;
            $dailyEarning = floatval($investment['daily_return']);

            for ($day = 1; $day <= $currentProgress; $day++) {
                $earningDate = clone $createdDate;
                $earningDate->modify('+' . $day . ' days');
                
                $runningTotal += $dailyEarning;
                
                $earnings[] = [
                    'date' => $earningDate->format('Y-m-d'),
                    'day' => $day,
                    'amount' => $dailyEarning,
                    'running_total' => $runningTotal,
                    'status' => 'paid' // For now, assume all past earnings are paid
                ];
            }

            return $earnings;

        } catch (Exception $e) {
            error_log("Error fetching investment earnings: " . $e->getMessage());
            return [];
        }
    }
}
