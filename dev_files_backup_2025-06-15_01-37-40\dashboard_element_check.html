<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Element Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .element-check {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .element-check.found {
            background: #d4edda;
            color: #155724;
        }
        .element-check.missing {
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Dashboard Element Availability Check</h1>
        <p>This test checks if all elements referenced in dashboard-simple.js actually exist in the dashboard HTML.</p>
        
        <h3>Required Elements:</h3>
        <div id="elementResults">
            <!-- Results will be populated here -->
        </div>
        
        <div style="margin-top: 20px;">
            <button class="btn" onclick="checkElements()">Check Elements</button>
            <button class="btn" onclick="simulateJSExecution()">Simulate JS Execution</button>
        </div>
        
        <div id="simulationResults" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; display: none;">
            <h3>Simulation Results:</h3>
            <pre id="simulationLog"></pre>
        </div>
    </div>

    <script>
        // Elements that dashboard-simple.js tries to access
        const requiredElements = [
            'totalBalance',
            'totalTransactions', 
            'totalDeposits',
            'totalInvested',
            'activeInvestmentsList',
            'recentTransactionsList',
            'message'
        ];
        
        function checkElements() {
            const resultsContainer = document.getElementById('elementResults');
            let html = '';
            
            requiredElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                const exists = element !== null;
                
                html += `
                    <div class="element-check ${exists ? 'found' : 'missing'}">
                        <span><strong>#${elementId}</strong></span>
                        <span>${exists ? '✅ Found' : '❌ Missing'}</span>
                    </div>
                `;
            });
            
            resultsContainer.innerHTML = html;
        }
        
        function simulateJSExecution() {
            const resultsContainer = document.getElementById('simulationResults');
            const logContainer = document.getElementById('simulationLog');
            
            let log = '';
            
            // Simulate the dashboard-simple.js execution
            log += 'Simulating dashboard-simple.js execution:\n\n';
            
            // Check totalBalance
            const totalBalance = document.getElementById('totalBalance');
            if (totalBalance) {
                totalBalance.textContent = '5.00';
                log += '✅ totalBalance.textContent = "5.00" - SUCCESS\n';
            } else {
                log += '❌ totalBalance not found - Would cause TypeError\n';
            }
            
            // Check totalTransactions
            const totalTransactions = document.getElementById('totalTransactions');
            if (totalTransactions) {
                totalTransactions.textContent = '5';
                log += '✅ totalTransactions.textContent = "5" - SUCCESS\n';
            } else {
                log += '❌ totalTransactions not found - Would cause TypeError\n';
            }
            
            // Check totalDeposits
            const totalDeposits = document.getElementById('totalDeposits');
            if (totalDeposits) {
                totalDeposits.textContent = '0.000000';
                log += '✅ totalDeposits.textContent = "0.000000" - SUCCESS\n';
            } else {
                log += '❌ totalDeposits not found - Would cause TypeError\n';
            }
            
            // Check totalInvested
            const totalInvested = document.getElementById('totalInvested');
            if (totalInvested) {
                totalInvested.textContent = '0 USDT';
                log += '✅ totalInvested.textContent = "0 USDT" - SUCCESS\n';
            } else {
                log += '❌ totalInvested not found - Would cause TypeError\n';
            }
            
            // Check activeInvestmentsList
            const activeInvestmentsList = document.getElementById('activeInvestmentsList');
            if (activeInvestmentsList) {
                log += '✅ activeInvestmentsList found - SUCCESS\n';
            } else {
                log += '❌ activeInvestmentsList not found - Would cause issues\n';
            }
            
            // Check recentTransactionsList
            const recentTransactionsList = document.getElementById('recentTransactionsList');
            if (recentTransactionsList) {
                log += '✅ recentTransactionsList found - SUCCESS\n';
            } else {
                log += '❌ recentTransactionsList not found - Would cause issues\n';
            }
            
            // Check message
            const message = document.getElementById('message');
            if (message) {
                log += '✅ message element found - SUCCESS\n';
            } else {
                log += '⚠️ message element not found - Will fallback to console\n';
            }
            
            log += '\n--- SUMMARY ---\n';
            const missingElements = requiredElements.filter(id => !document.getElementById(id));
            if (missingElements.length === 0) {
                log += '✅ All elements found! JavaScript should execute without errors.\n';
            } else {
                log += `❌ Missing elements: ${missingElements.join(', ')}\n`;
                log += 'These would cause TypeError: Cannot set properties of null\n';
            }
            
            logContainer.textContent = log;
            resultsContainer.style.display = 'block';
        }
        
        // Auto-check on page load
        window.addEventListener('load', checkElements);
    </script>
</body>
</html>
