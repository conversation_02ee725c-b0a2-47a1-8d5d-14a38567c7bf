<?php

declare(strict_types=1);

namespace Simbi\Tls\Frontend\Services;

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Utils\ValidationUtils;

/**
 * PSR-4 compliant AJAX handler service
 * Handles all AJAX requests from frontend
 */
class AjaxHandler
{
    private ApiService $apiService;
    private array $input;

    public function __construct()
    {
        $this->apiService = new ApiService();
        $this->input = $this->getInput();
    }

    /**
     * Get and sanitize input data
     */
    private function getInput(): array
    {
        $input = json_decode(file_get_contents('php://input'), true) ?? [];
        return ValidationUtils::sanitizeInput($input);
    }

    /**
     * Handle AJAX request
     */
    public function handle(): void
    {
        // Ensure session is initialized
        SessionService::init();

        // Set JSON response header
        header('Content-Type: application/json');

        // Check request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        $action = $this->input['action'] ?? '';

        try {
            $result = match ($action) {
                // Authentication actions
                'register' => $this->handleRegister(),
                'login' => $this->handleLogin(),
                'logout' => $this->handleLogout(),
                'password_reset_request' => $this->handlePasswordResetRequest(),
                'password_reset' => $this->handlePasswordReset(),

                // User actions
                'get_profile' => $this->handleGetProfile(),
                'change_password' => $this->handleChangePassword(),

                // Wallet actions
                'create_wallet' => $this->handleCreateWallet(),
                'get_balance' => $this->handleGetBalance(),
                'get_wallet' => $this->handleGetWallet(),
                'get_live_wallet' => $this->handleGetLiveWallet(),
                'withdraw' => $this->handleWithdraw(),
                'sweep_funds' => $this->handleSweepFunds(),

                // Transaction actions
                'get_transactions' => $this->handleGetTransactions(),
                'get_transaction_statistics' => $this->handleGetTransactionStatistics(),

                // Investment actions
                'get_investment_plans' => $this->handleGetInvestmentPlans(),
                'create_investment' => $this->handleCreateInvestment(),
                'get_investments' => $this->handleGetInvestments(),
                'get_active_investments' => $this->handleGetActiveInvestments(),

                // Payment actions
                'confirm_payment' => $this->handleConfirmPayment(),
                'get_payment_status' => $this->handleGetPaymentStatus(),

                // Admin actions
                'get_system_statistics' => $this->handleGetSystemStatistics(),
                'get_user_list' => $this->handleGetUserList(),
                'get_all_transactions' => $this->handleGetAllTransactions(),
                'update_user_status' => $this->handleUpdateUserStatus(),
                'promote_user' => $this->handlePromoteUser(),
                'demote_user' => $this->handleDemoteUser(),
                'get_activity_logs' => $this->handleGetActivityLogs(),
                'get_admin_transaction_statistics' => $this->handleGetAdminTransactionStatistics(),
                'get_system_health' => $this->handleGetSystemHealth(),

                default => ['error' => 'Unknown action: ' . $action]
            };

            echo json_encode($result);

        } catch (\Exception $e) {
            error_log('AJAX Error: ' . $e->getMessage());
            echo json_encode(['error' => 'Internal server error']);
        }
    }

    /**
     * Check if user is authenticated
     */
    private function requireAuth(): bool
    {
        if (!SessionService::isAuthenticated()) {
            echo json_encode(['error' => 'Not authenticated']);
            return false;
        }
        
        // Update API service token
        $this->apiService->setToken(SessionService::getToken());
        return true;
    }

    /**
     * Check if user is admin
     */
    private function requireAdmin(): bool
    {
        if (!$this->requireAuth()) {
            return false;
        }
        
        if (!SessionService::isAdmin()) {
            echo json_encode(['error' => 'Admin access required']);
            return false;
        }
        
        return true;
    }

    // Authentication handlers
    private function handleRegister(): array
    {
        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'email' => ['type' => 'email', 'required' => true],
            'password' => ['type' => 'string', 'required' => true, 'min_length' => 8]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        $data = $validation['data'];
        $result = $this->apiService->register($data['email'], $data['password']);

        if (isset($result['success']) && $result['success']) {
            // Store session data
            SessionService::setUser($result['user']);
        }

        return $result;
    }

    private function handleLogin(): array
    {
        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'email' => ['type' => 'email', 'required' => true],
            'password' => ['type' => 'string', 'required' => true]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        $data = $validation['data'];
        $result = $this->apiService->login($data['email'], $data['password']);

        if (isset($result['success']) && $result['success']) {
            // Store session data
            SessionService::setUser($result['user']);
        }

        return $result;
    }

    private function handleLogout(): array
    {
        SessionService::clearUser();
        return ['success' => true, 'message' => 'Logged out successfully'];
    }

    private function handlePasswordResetRequest(): array
    {
        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'email' => ['type' => 'email', 'required' => true]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        return $this->apiService->passwordResetRequest($validation['data']['email']);
    }

    private function handlePasswordReset(): array
    {
        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'token' => ['type' => 'string', 'required' => true],
            'new_password' => ['type' => 'string', 'required' => true, 'min_length' => 8]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        $data = $validation['data'];
        return $this->apiService->passwordReset($data['token'], $data['new_password']);
    }

    // User handlers
    private function handleGetProfile(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];
        return $this->apiService->getUserProfile();
    }

    private function handleChangePassword(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];

        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'current_password' => ['type' => 'string', 'required' => true],
            'new_password' => ['type' => 'string', 'required' => true, 'min_length' => 8]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        $data = $validation['data'];
        return $this->apiService->changePassword($data['current_password'], $data['new_password']);
    }

    // Wallet handlers
    private function handleCreateWallet(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];
        return $this->apiService->createWallet();
    }

    private function handleGetBalance(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];
        return $this->apiService->getBalance();
    }

    private function handleGetWallet(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];
        return $this->apiService->getWallet();
    }

    private function handleGetLiveWallet(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];

        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'address' => ['type' => 'tron_address', 'required' => true]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        return $this->apiService->getLiveWalletBalance($validation['data']['address']);
    }

    private function handleWithdraw(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];

        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'to' => ['type' => 'tron_address', 'required' => true],
            'amount' => ['type' => 'float', 'required' => true, 'min' => 0.000001]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        $data = $validation['data'];
        return $this->apiService->withdraw($data['to'], $data['amount']);
    }

    private function handleSweepFunds(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];
        return $this->apiService->sweepFunds();
    }

    // Transaction handlers
    private function handleGetTransactions(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];

        $type = $this->input['type'] ?? null;
        $limit = (int)($this->input['limit'] ?? 10);
        $page = (int)($this->input['page'] ?? 0);

        return $this->apiService->getTransactions($type, $limit, $page);
    }

    private function handleGetTransactionStatistics(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];
        return $this->apiService->getTransactionStatistics();
    }

    // Investment handlers
    private function handleGetInvestmentPlans(): array
    {
        return $this->apiService->getInvestmentPlans();
    }

    private function handleCreateInvestment(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];

        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'amount' => ['type' => 'float', 'required' => true, 'min' => 1],
            'plan' => ['type' => 'string', 'required' => true]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        if (!ValidationUtils::isValidInvestmentPlan($validation['data']['plan'])) {
            return ['error' => 'Invalid investment plan'];
        }

        $data = $validation['data'];
        return $this->apiService->createInvestment($data['amount'], $data['plan']);
    }

    private function handleGetInvestments(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];
        return $this->apiService->getInvestments();
    }

    private function handleGetActiveInvestments(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];
        return $this->apiService->getActiveInvestments();
    }

    // Payment handlers
    private function handleConfirmPayment(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];

        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'wallet_id' => ['type' => 'integer', 'required' => true],
            'amount' => ['type' => 'float', 'required' => true, 'min' => 0.000001]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        $data = $validation['data'];
        return $this->apiService->confirmPayment($data['wallet_id'], $data['amount']);
    }

    private function handleGetPaymentStatus(): array
    {
        if (!$this->requireAuth()) return ['error' => 'Authentication required'];

        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'transaction_hash' => ['type' => 'string', 'required' => true]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        return $this->apiService->getPaymentStatus($validation['data']['transaction_hash']);
    }

    // Admin handlers
    private function handleGetSystemStatistics(): array
    {
        if (!$this->requireAdmin()) return ['error' => 'Admin access required'];
        return $this->apiService->getSystemStatistics();
    }

    private function handleGetUserList(): array
    {
        if (!$this->requireAdmin()) return ['error' => 'Admin access required'];

        $limit = (int)($this->input['limit'] ?? 20);
        $offset = (int)($this->input['offset'] ?? 0);
        $status = $this->input['status'] ?? null;

        return $this->apiService->getUserList($limit, $offset, $status);
    }

    private function handleGetAllTransactions(): array
    {
        if (!$this->requireAdmin()) return ['error' => 'Admin access required'];

        $limit = (int)($this->input['limit'] ?? 100);
        $offset = (int)($this->input['offset'] ?? 0);

        return $this->apiService->getAllTransactions($limit, $offset);
    }

    private function handleUpdateUserStatus(): array
    {
        if (!$this->requireAdmin()) return ['error' => 'Admin access required'];

        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'user_id' => ['type' => 'integer', 'required' => true],
            'status' => ['type' => 'string', 'required' => true]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        $data = $validation['data'];
        return $this->apiService->updateUserStatus($data['user_id'], $data['status']);
    }

    private function handlePromoteUser(): array
    {
        if (!$this->requireAdmin()) return ['error' => 'Admin access required'];

        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'user_id' => ['type' => 'integer', 'required' => true]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        return $this->apiService->promoteUser($validation['data']['user_id']);
    }

    private function handleDemoteUser(): array
    {
        if (!$this->requireAdmin()) return ['error' => 'Admin access required'];

        $validation = ValidationUtils::cleanAndValidate($this->input, [
            'user_id' => ['type' => 'integer', 'required' => true]
        ]);

        if (!$validation['valid']) {
            return ['error' => 'Validation failed', 'details' => $validation['errors']];
        }

        return $this->apiService->demoteUser($validation['data']['user_id']);
    }

    private function handleGetActivityLogs(): array
    {
        if (!$this->requireAdmin()) return ['error' => 'Admin access required'];

        $limit = (int)($this->input['limit'] ?? 50);
        $offset = (int)($this->input['offset'] ?? 0);

        return $this->apiService->getActivityLogs($limit, $offset);
    }

    private function handleGetAdminTransactionStatistics(): array
    {
        if (!$this->requireAdmin()) return ['error' => 'Admin access required'];
        return $this->apiService->getAdminTransactionStatistics();
    }

    private function handleGetSystemHealth(): array
    {
        if (!$this->requireAdmin()) return ['error' => 'Admin access required'];
        return $this->apiService->getSystemHealth();
    }
}
