<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'Profile - TLS Wallet';
$currentPage = 'profile';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>            <!-- Profile Content -->
            <div class="profile-page">
                <!-- Message container for notifications -->
                <div id="message" class="message" style="display: none;"></div>
                
                <div class="card profile-card">
                    <div class="profile-header">
                        <div class="profile-header-content">
                            <h2>👤 User Profile</h2>
                            <p>Manage your account settings and preferences</p>
                        </div>
                        <div class="profile-header-status">
                            <span class="status-badge status-active">Active Account</span>
                        </div>
                    </div>                    <!-- Profile Information -->
                    <!-- <div class="profile-section account-info-section"> -->
                        <h3>📋 Account Information</h3>
                        <div class="profile-info">
                            <div class="profile-field">
                                <label>📧 Email Address:</label>
                                <span id="userEmail" class="field-value"><?php echo htmlspecialchars($user['email'] ?? ''); ?></span>
                            </div>
                            <div class="profile-field">
                                <label>👤 Username:</label>
                                <span id="userName" class="field-value"><?php echo htmlspecialchars($user['email'] ?? ''); ?></span>
                            </div>
                            <div class="profile-field">
                                <label>📅 Account Created:</label>
                                <span id="userCreated" class="field-value"><?php echo isset($user['created_at']) ? date('M j, Y', strtotime($user['created_at'])) : 'N/A'; ?></span>
                            </div>
                            <div class="profile-field">
                                <label>🟢 Account Status:</label>
                                <span class="status-badge status-active">Active</span>
                            </div>
                        </div>
                    <!-- </div>                    Password Change -->
                    <!-- <div class="profile-section password-section"> -->
                        <div class="form-container">
                            <form id="changePasswordForm" class="password-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="currentPassword">Current Password</label>
                                        <input type="password" id="currentPassword" name="current_password" required class="form-control">
                                        <div class="field-help">Enter your current password</div>
                                    </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="newPassword">New Password</label>
                                        <input type="password" id="newPassword" name="new_password" required minlength="8" class="form-control">
                                        <div class="field-help">Minimum 8 characters required</div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="confirmPassword">Confirm New Password</label>
                                        <input type="password" id="confirmPassword" name="confirm_password" required class="form-control">
                                        <div class="field-help">Re-enter your new password</div>
                                    </div>
                                </div>
                                
                                <div class="password-requirements">
                                    <h4>Password Requirements:</h4>
                                    <ul>
                                        <li>At least 8 characters long</li>
                                        <li>Use a mix of letters, numbers, and symbols</li>
                                        <li>Avoid using personal information</li>
                                    </ul>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">🔄 Update Password</button>
                                    <button type="reset" class="btn btn-outline">↺ Reset Form</button>
                                </div>
                            </form>
                        </div>
                    <!-- </div>                    Security Settings -->
                    <div class="profile-section security-section">
                        <hr />
                        <h3>🛡️ Security Settings</h3>
                        <div class="security-info">
                            <div class="security-card">
                                <div class="security-icon">🔒</div>
                                <div class="security-content">
                                    <h4>Account Security Tips</h4>
                                    <p>Your account security is important. Here are some recommendations:</p>
                                    <ul class="security-list">
                                        <li>✅ Use a strong, unique password</li>
                                        <li>✅ Never share your login credentials</li>
                                        <li>✅ Log out when using shared computers</li>
                                        <li>✅ Contact support if you notice any suspicious activity</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Actions -->
                    <div class="profile-section actions-section">
                        <h3>⚙️ Account Actions</h3>
                        <div class="account-actions">
                            <a href="dashboard.php" class="btn btn-outline">
                                <span class="btn-icon">🏠</span>
                                Back to Dashboard
                            </a>
                            <a href="../index.php?logout=1" class="btn btn-danger" onclick="return confirm('Are you sure you want to logout?')">
                                <span class="btn-icon">🚪</span>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>    <script>
        // Profile page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            const changePasswordForm = document.getElementById('changePasswordForm');
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            
            // Real-time password validation
            function validatePasswords() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                // Clear previous validation styles
                newPasswordInput.classList.remove('error', 'success');
                confirmPasswordInput.classList.remove('error', 'success');
                
                // Validate new password
                if (newPassword.length >= 8) {
                    newPasswordInput.classList.add('success');
                } else if (newPassword.length > 0) {
                    newPasswordInput.classList.add('error');
                }
                
                // Validate password confirmation
                if (confirmPassword.length > 0) {
                    if (newPassword === confirmPassword && newPassword.length >= 8) {
                        confirmPasswordInput.classList.add('success');
                    } else {
                        confirmPasswordInput.classList.add('error');
                    }
                }
            }
            
            // Add real-time validation
            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', validatePasswords);
            }
            if (confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', validatePasswords);
            }
            
            if (changePasswordForm) {
                changePasswordForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const currentPassword = document.getElementById('currentPassword').value;
                    const newPassword = newPasswordInput.value;
                    const confirmPassword = confirmPasswordInput.value;
                    
                    // Validate passwords match
                    if (newPassword !== confirmPassword) {
                        showMessage('🚫 New passwords do not match', 'error');
                        confirmPasswordInput.focus();
                        return;
                    }
                    
                    // Validate password length
                    if (newPassword.length < 8) {
                        showMessage('🚫 New password must be at least 8 characters long', 'error');
                        newPasswordInput.focus();
                        return;
                    }
                    
                    // Validate current password
                    if (currentPassword.length === 0) {
                        showMessage('🚫 Please enter your current password', 'error');
                        document.getElementById('currentPassword').focus();
                        return;
                    }
                    
                    // Disable submit button during processing
                    const submitBtn = changePasswordForm.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '⏳ Updating...';
                    
                    // Make API call to change password
                    apiCall('change_password', {
                        current_password: currentPassword,
                        new_password: newPassword
                    }).then(response => {
                        if (response.success) {
                            showMessage('✅ Password changed successfully', 'success');
                            changePasswordForm.reset();
                            validatePasswords(); // Clear validation styles
                        } else {
                            showMessage('🚫 ' + (response.message || 'Failed to change password'), 'error');
                        }
                    }).catch(error => {
                        console.error('Error changing password:', error);
                        showMessage('🚫 Error changing password. Please try again.', 'error');
                    }).finally(() => {
                        // Re-enable submit button
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    });
                });
            }
            
            // Add smooth scroll to top when message is shown
            function scrollToMessage() {
                const messageEl = document.getElementById('message');
                if (messageEl && messageEl.style.display !== 'none') {
                    messageEl.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }
            
            // Enhanced message display with auto-hide
            window.showMessage = function(message, type = 'info') {
                const messageEl = document.getElementById('message');
                if (messageEl) {
                    messageEl.textContent = message;
                    messageEl.className = `message ${type}`;
                    messageEl.style.display = 'block';
                    
                    // Scroll to message
                    setTimeout(scrollToMessage, 100);
                    
                    // Auto-hide after 5 seconds
                    setTimeout(() => {
                        messageEl.style.opacity = '0';
                        setTimeout(() => {
                            messageEl.style.display = 'none';
                            messageEl.style.opacity = '1';
                        }, 300);
                    }, 5000);
                }
            };
        });

        // Enhanced API call with better error handling
        async function apiCall(endpoint, data = {}) {
            try {
                const response = await fetch('../api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: endpoint,
                        ...data
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        }
        
        // Add some interactive enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to profile fields
            const profileFields = document.querySelectorAll('.profile-field');
            profileFields.forEach(field => {
                field.addEventListener('mouseenter', function() {
                    this.style.borderColor = '#667eea';
                });
                field.addEventListener('mouseleave', function() {
                    this.style.borderColor = '#e9ecef';
                });
            });
            
            // Add click effect to buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
