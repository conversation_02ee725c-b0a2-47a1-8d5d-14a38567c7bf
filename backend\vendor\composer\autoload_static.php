<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit8b5f9a5f51e1d2b8ef0de961298cf21e
{
    public static $files = array (
        '7b11c4dc42b3b3023073cb14e519683c' => __DIR__ . '/..' . '/ralouphie/getallheaders/src/getallheaders.php',
        '6e3fae29631ef280660b3cdad06f25a8' => __DIR__ . '/..' . '/symfony/deprecation-contracts/function.php',
        '37a3dc5111fe8f707ab4c132ef1dbc62' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/functions_include.php',
        '0e6d7bf4a5811bfa5cf40c5ccd6fae6a' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/bootstrap.php',
        'decc78cc4436b1292c6c0d151b19445c' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/bootstrap.php',
    );

    public static $prefixLengthsPsr4 = array (
        'p' => 
        array (
            'phpseclib\\' => 10,
        ),
        'k' => 
        array (
            'kornrunner\\' => 11,
        ),
        'W' => 
        array (
            'Web3\\' => 5,
        ),
        'S' => 
        array (
            'Symfony\\Polyfill\\Mbstring\\' => 26,
            'Simbi\\Tls\\Tests\\' => 16,
            'Simbi\\Tls\\Frontend\\' => 19,
            'Simbi\\Tls\\CronJob\\' => 18,
            'Simbi\\Tls\\' => 10,
        ),
        'P' => 
        array (
            'Psr\\Http\\Message\\' => 17,
            'Psr\\Http\\Client\\' => 16,
        ),
        'M' => 
        array (
            'Mdanter\\Ecc\\' => 12,
        ),
        'I' => 
        array (
            'IEXBase\\TronAPI\\' => 16,
        ),
        'G' => 
        array (
            'GuzzleHttp\\Psr7\\' => 16,
            'GuzzleHttp\\Promise\\' => 19,
            'GuzzleHttp\\' => 11,
        ),
        'F' => 
        array (
            'FG\\' => 3,
        ),
        'E' => 
        array (
            'Elliptic\\' => 9,
        ),
        'C' => 
        array (
            'Comely\\DataTypes\\' => 17,
        ),
        'B' => 
        array (
            'BN\\' => 3,
            'BI\\' => 3,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'phpseclib\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib',
        ),
        'kornrunner\\' => 
        array (
            0 => __DIR__ . '/..' . '/kornrunner/secp256k1/src',
            1 => __DIR__ . '/..' . '/kornrunner/keccak/src',
        ),
        'Web3\\' => 
        array (
            0 => __DIR__ . '/..' . '/iexbase/web3.php/src',
        ),
        'Symfony\\Polyfill\\Mbstring\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-mbstring',
        ),
        'Simbi\\Tls\\Tests\\' => 
        array (
            0 => __DIR__ . '/../..' . '/tests',
        ),
        'Simbi\\Tls\\Frontend\\' => 
        array (
            0 => __DIR__ . '/../..' . '/../frontend/src',
        ),
        'Simbi\\Tls\\CronJob\\' => 
        array (
            0 => __DIR__ . '/../..' . '/../cronJob/src',
        ),
        'Simbi\\Tls\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-factory/src',
            1 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
        'Psr\\Http\\Client\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-client/src',
        ),
        'Mdanter\\Ecc\\' => 
        array (
            0 => __DIR__ . '/..' . '/mdanter/ecc/src',
        ),
        'IEXBase\\TronAPI\\' => 
        array (
            0 => __DIR__ . '/..' . '/iexbase/tron-api/src',
        ),
        'GuzzleHttp\\Psr7\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/psr7/src',
        ),
        'GuzzleHttp\\Promise\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/promises/src',
        ),
        'GuzzleHttp\\' => 
        array (
            0 => __DIR__ . '/..' . '/guzzlehttp/guzzle/src',
        ),
        'FG\\' => 
        array (
            0 => __DIR__ . '/..' . '/fgrosse/phpasn1/lib',
        ),
        'Elliptic\\' => 
        array (
            0 => __DIR__ . '/..' . '/simplito/elliptic-php/lib',
        ),
        'Comely\\DataTypes\\' => 
        array (
            0 => __DIR__ . '/..' . '/comely-io/data-types/src',
        ),
        'BN\\' => 
        array (
            0 => __DIR__ . '/..' . '/simplito/bn-php/lib',
        ),
        'BI\\' => 
        array (
            0 => __DIR__ . '/..' . '/simplito/bigint-wrapper-php/lib',
        ),
    );

    public static $classMap = array (
        'BI\\BigInteger' => __DIR__ . '/..' . '/simplito/bigint-wrapper-php/lib/BigInteger.php',
        'BN\\BN' => __DIR__ . '/..' . '/simplito/bn-php/lib/BN.php',
        'BN\\Red' => __DIR__ . '/..' . '/simplito/bn-php/lib/Red.php',
        'Comely\\DataTypes\\BcMath\\BaseConvert' => __DIR__ . '/..' . '/comely-io/data-types/src/BcMath/BaseConvert.php',
        'Comely\\DataTypes\\BcMath\\BcMath' => __DIR__ . '/..' . '/comely-io/data-types/src/BcMath/BcMath.php',
        'Comely\\DataTypes\\BcNumber' => __DIR__ . '/..' . '/comely-io/data-types/src/BcNumber.php',
        'Comely\\DataTypes\\Buffer\\AbstractBuffer' => __DIR__ . '/..' . '/comely-io/data-types/src/Buffer/AbstractBuffer.php',
        'Comely\\DataTypes\\Buffer\\Base16' => __DIR__ . '/..' . '/comely-io/data-types/src/Buffer/Base16.php',
        'Comely\\DataTypes\\Buffer\\Base16\\Decoder' => __DIR__ . '/..' . '/comely-io/data-types/src/Buffer/Base16/Decoder.php',
        'Comely\\DataTypes\\Buffer\\Base64' => __DIR__ . '/..' . '/comely-io/data-types/src/Buffer/Base64.php',
        'Comely\\DataTypes\\Buffer\\Binary' => __DIR__ . '/..' . '/comely-io/data-types/src/Buffer/Binary.php',
        'Comely\\DataTypes\\Buffer\\Binary\\ByteReader' => __DIR__ . '/..' . '/comely-io/data-types/src/Buffer/Binary/ByteReader.php',
        'Comely\\DataTypes\\Buffer\\Binary\\Digest' => __DIR__ . '/..' . '/comely-io/data-types/src/Buffer/Binary/Digest.php',
        'Comely\\DataTypes\\Buffer\\Binary\\LenSize' => __DIR__ . '/..' . '/comely-io/data-types/src/Buffer/Binary/LenSize.php',
        'Comely\\DataTypes\\Buffer\\Bitwise' => __DIR__ . '/..' . '/comely-io/data-types/src/Buffer/Bitwise.php',
        'Comely\\DataTypes\\DataTypes' => __DIR__ . '/..' . '/comely-io/data-types/src/DataTypes.php',
        'Comely\\DataTypes\\Integers' => __DIR__ . '/..' . '/comely-io/data-types/src/Integers.php',
        'Comely\\DataTypes\\Strings\\ASCII' => __DIR__ . '/..' . '/comely-io/data-types/src/Strings/ASCII.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Elliptic\\Curve\\BaseCurve' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/BaseCurve.php',
        'Elliptic\\Curve\\BaseCurve\\Point' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/BaseCurve/Point.php',
        'Elliptic\\Curve\\EdwardsCurve' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/EdwardsCurve.php',
        'Elliptic\\Curve\\EdwardsCurve\\Point' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/EdwardsCurve/Point.php',
        'Elliptic\\Curve\\MontCurve' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/MontCurve.php',
        'Elliptic\\Curve\\MontCurve\\Point' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/MontCurve/Point.php',
        'Elliptic\\Curve\\PresetCurve' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/PresetCurve.php',
        'Elliptic\\Curve\\ShortCurve' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/ShortCurve.php',
        'Elliptic\\Curve\\ShortCurve\\JPoint' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/ShortCurve/JPoint.php',
        'Elliptic\\Curve\\ShortCurve\\Point' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curve/ShortCurve/Point.php',
        'Elliptic\\Curves' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Curves.php',
        'Elliptic\\EC' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/EC.php',
        'Elliptic\\EC\\KeyPair' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/EC/KeyPair.php',
        'Elliptic\\EC\\Signature' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/EC/Signature.php',
        'Elliptic\\EdDSA' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/EdDSA.php',
        'Elliptic\\EdDSA\\KeyPair' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/EdDSA/KeyPair.php',
        'Elliptic\\EdDSA\\Signature' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/EdDSA/Signature.php',
        'Elliptic\\HmacDRBG' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/HmacDRBG.php',
        'Elliptic\\Utils' => __DIR__ . '/..' . '/simplito/elliptic-php/lib/Utils.php',
        'FG\\ASN1\\ASNObject' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/ASNObject.php',
        'FG\\ASN1\\AbstractString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/AbstractString.php',
        'FG\\ASN1\\AbstractTime' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/AbstractTime.php',
        'FG\\ASN1\\Base128' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Base128.php',
        'FG\\ASN1\\Composite\\AttributeTypeAndValue' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Composite/AttributeTypeAndValue.php',
        'FG\\ASN1\\Composite\\RDNString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Composite/RDNString.php',
        'FG\\ASN1\\Composite\\RelativeDistinguishedName' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Composite/RelativeDistinguishedName.php',
        'FG\\ASN1\\Construct' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Construct.php',
        'FG\\ASN1\\Exception\\NotImplementedException' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Exception/NotImplementedException.php',
        'FG\\ASN1\\Exception\\ParserException' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Exception/ParserException.php',
        'FG\\ASN1\\ExplicitlyTaggedObject' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/ExplicitlyTaggedObject.php',
        'FG\\ASN1\\Identifier' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Identifier.php',
        'FG\\ASN1\\OID' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/OID.php',
        'FG\\ASN1\\Parsable' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Parsable.php',
        'FG\\ASN1\\TemplateParser' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/TemplateParser.php',
        'FG\\ASN1\\Universal\\BMPString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/BMPString.php',
        'FG\\ASN1\\Universal\\BitString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/BitString.php',
        'FG\\ASN1\\Universal\\Boolean' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/Boolean.php',
        'FG\\ASN1\\Universal\\CharacterString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/CharacterString.php',
        'FG\\ASN1\\Universal\\Enumerated' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/Enumerated.php',
        'FG\\ASN1\\Universal\\GeneralString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/GeneralString.php',
        'FG\\ASN1\\Universal\\GeneralizedTime' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/GeneralizedTime.php',
        'FG\\ASN1\\Universal\\GraphicString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/GraphicString.php',
        'FG\\ASN1\\Universal\\IA5String' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/IA5String.php',
        'FG\\ASN1\\Universal\\Integer' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/Integer.php',
        'FG\\ASN1\\Universal\\NullObject' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/NullObject.php',
        'FG\\ASN1\\Universal\\NumericString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/NumericString.php',
        'FG\\ASN1\\Universal\\ObjectDescriptor' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/ObjectDescriptor.php',
        'FG\\ASN1\\Universal\\ObjectIdentifier' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/ObjectIdentifier.php',
        'FG\\ASN1\\Universal\\OctetString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/OctetString.php',
        'FG\\ASN1\\Universal\\PrintableString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/PrintableString.php',
        'FG\\ASN1\\Universal\\RelativeObjectIdentifier' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/RelativeObjectIdentifier.php',
        'FG\\ASN1\\Universal\\Sequence' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/Sequence.php',
        'FG\\ASN1\\Universal\\Set' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/Set.php',
        'FG\\ASN1\\Universal\\T61String' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/T61String.php',
        'FG\\ASN1\\Universal\\UTCTime' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/UTCTime.php',
        'FG\\ASN1\\Universal\\UTF8String' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/UTF8String.php',
        'FG\\ASN1\\Universal\\UniversalString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/UniversalString.php',
        'FG\\ASN1\\Universal\\VisibleString' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/Universal/VisibleString.php',
        'FG\\ASN1\\UnknownConstructedObject' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/UnknownConstructedObject.php',
        'FG\\ASN1\\UnknownObject' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/ASN1/UnknownObject.php',
        'FG\\Utility\\BigInteger' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/Utility/BigInteger.php',
        'FG\\Utility\\BigIntegerBcmath' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/Utility/BigIntegerBcmath.php',
        'FG\\Utility\\BigIntegerGmp' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/Utility/BigIntegerGmp.php',
        'FG\\X509\\AlgorithmIdentifier' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/AlgorithmIdentifier.php',
        'FG\\X509\\CSR\\Attributes' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/CSR/Attributes.php',
        'FG\\X509\\CSR\\CSR' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/CSR/CSR.php',
        'FG\\X509\\CertificateExtensions' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/CertificateExtensions.php',
        'FG\\X509\\CertificateSubject' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/CertificateSubject.php',
        'FG\\X509\\PrivateKey' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/PrivateKey.php',
        'FG\\X509\\PublicKey' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/PublicKey.php',
        'FG\\X509\\SAN\\DNSName' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/SAN/DNSName.php',
        'FG\\X509\\SAN\\IPAddress' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/SAN/IPAddress.php',
        'FG\\X509\\SAN\\SubjectAlternativeNames' => __DIR__ . '/..' . '/fgrosse/phpasn1/lib/X509/SAN/SubjectAlternativeNames.php',
        'GuzzleHttp\\BodySummarizer' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/BodySummarizer.php',
        'GuzzleHttp\\BodySummarizerInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
        'GuzzleHttp\\Client' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Client.php',
        'GuzzleHttp\\ClientInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/ClientInterface.php',
        'GuzzleHttp\\ClientTrait' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/ClientTrait.php',
        'GuzzleHttp\\Cookie\\CookieJar' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
        'GuzzleHttp\\Cookie\\CookieJarInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
        'GuzzleHttp\\Cookie\\FileCookieJar' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
        'GuzzleHttp\\Cookie\\SessionCookieJar' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
        'GuzzleHttp\\Cookie\\SetCookie' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
        'GuzzleHttp\\Exception\\BadResponseException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
        'GuzzleHttp\\Exception\\ClientException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/ClientException.php',
        'GuzzleHttp\\Exception\\ConnectException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/ConnectException.php',
        'GuzzleHttp\\Exception\\GuzzleException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
        'GuzzleHttp\\Exception\\InvalidArgumentException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
        'GuzzleHttp\\Exception\\RequestException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/RequestException.php',
        'GuzzleHttp\\Exception\\ServerException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/ServerException.php',
        'GuzzleHttp\\Exception\\TooManyRedirectsException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
        'GuzzleHttp\\Exception\\TransferException' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Exception/TransferException.php',
        'GuzzleHttp\\HandlerStack' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/HandlerStack.php',
        'GuzzleHttp\\Handler\\CurlFactory' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
        'GuzzleHttp\\Handler\\CurlFactoryInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
        'GuzzleHttp\\Handler\\CurlHandler' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
        'GuzzleHttp\\Handler\\CurlMultiHandler' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
        'GuzzleHttp\\Handler\\EasyHandle' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
        'GuzzleHttp\\Handler\\HeaderProcessor' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
        'GuzzleHttp\\Handler\\MockHandler' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/MockHandler.php',
        'GuzzleHttp\\Handler\\Proxy' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/Proxy.php',
        'GuzzleHttp\\Handler\\StreamHandler' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
        'GuzzleHttp\\MessageFormatter' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/MessageFormatter.php',
        'GuzzleHttp\\MessageFormatterInterface' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
        'GuzzleHttp\\Middleware' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Middleware.php',
        'GuzzleHttp\\Pool' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Pool.php',
        'GuzzleHttp\\PrepareBodyMiddleware' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
        'GuzzleHttp\\Promise\\AggregateException' => __DIR__ . '/..' . '/guzzlehttp/promises/src/AggregateException.php',
        'GuzzleHttp\\Promise\\CancellationException' => __DIR__ . '/..' . '/guzzlehttp/promises/src/CancellationException.php',
        'GuzzleHttp\\Promise\\Coroutine' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Coroutine.php',
        'GuzzleHttp\\Promise\\Create' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Create.php',
        'GuzzleHttp\\Promise\\Each' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Each.php',
        'GuzzleHttp\\Promise\\EachPromise' => __DIR__ . '/..' . '/guzzlehttp/promises/src/EachPromise.php',
        'GuzzleHttp\\Promise\\FulfilledPromise' => __DIR__ . '/..' . '/guzzlehttp/promises/src/FulfilledPromise.php',
        'GuzzleHttp\\Promise\\Is' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Is.php',
        'GuzzleHttp\\Promise\\Promise' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Promise.php',
        'GuzzleHttp\\Promise\\PromiseInterface' => __DIR__ . '/..' . '/guzzlehttp/promises/src/PromiseInterface.php',
        'GuzzleHttp\\Promise\\PromisorInterface' => __DIR__ . '/..' . '/guzzlehttp/promises/src/PromisorInterface.php',
        'GuzzleHttp\\Promise\\RejectedPromise' => __DIR__ . '/..' . '/guzzlehttp/promises/src/RejectedPromise.php',
        'GuzzleHttp\\Promise\\RejectionException' => __DIR__ . '/..' . '/guzzlehttp/promises/src/RejectionException.php',
        'GuzzleHttp\\Promise\\TaskQueue' => __DIR__ . '/..' . '/guzzlehttp/promises/src/TaskQueue.php',
        'GuzzleHttp\\Promise\\TaskQueueInterface' => __DIR__ . '/..' . '/guzzlehttp/promises/src/TaskQueueInterface.php',
        'GuzzleHttp\\Promise\\Utils' => __DIR__ . '/..' . '/guzzlehttp/promises/src/Utils.php',
        'GuzzleHttp\\Psr7\\AppendStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/AppendStream.php',
        'GuzzleHttp\\Psr7\\BufferStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/BufferStream.php',
        'GuzzleHttp\\Psr7\\CachingStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/CachingStream.php',
        'GuzzleHttp\\Psr7\\DroppingStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/DroppingStream.php',
        'GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
        'GuzzleHttp\\Psr7\\FnStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/FnStream.php',
        'GuzzleHttp\\Psr7\\Header' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Header.php',
        'GuzzleHttp\\Psr7\\HttpFactory' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/HttpFactory.php',
        'GuzzleHttp\\Psr7\\InflateStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/InflateStream.php',
        'GuzzleHttp\\Psr7\\LazyOpenStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/LazyOpenStream.php',
        'GuzzleHttp\\Psr7\\LimitStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/LimitStream.php',
        'GuzzleHttp\\Psr7\\Message' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Message.php',
        'GuzzleHttp\\Psr7\\MessageTrait' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/MessageTrait.php',
        'GuzzleHttp\\Psr7\\MimeType' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/MimeType.php',
        'GuzzleHttp\\Psr7\\MultipartStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/MultipartStream.php',
        'GuzzleHttp\\Psr7\\NoSeekStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/NoSeekStream.php',
        'GuzzleHttp\\Psr7\\PumpStream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/PumpStream.php',
        'GuzzleHttp\\Psr7\\Query' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Query.php',
        'GuzzleHttp\\Psr7\\Request' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Request.php',
        'GuzzleHttp\\Psr7\\Response' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Response.php',
        'GuzzleHttp\\Psr7\\Rfc7230' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Rfc7230.php',
        'GuzzleHttp\\Psr7\\ServerRequest' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/ServerRequest.php',
        'GuzzleHttp\\Psr7\\Stream' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Stream.php',
        'GuzzleHttp\\Psr7\\StreamDecoratorTrait' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
        'GuzzleHttp\\Psr7\\StreamWrapper' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/StreamWrapper.php',
        'GuzzleHttp\\Psr7\\UploadedFile' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/UploadedFile.php',
        'GuzzleHttp\\Psr7\\Uri' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Uri.php',
        'GuzzleHttp\\Psr7\\UriComparator' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/UriComparator.php',
        'GuzzleHttp\\Psr7\\UriNormalizer' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/UriNormalizer.php',
        'GuzzleHttp\\Psr7\\UriResolver' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/UriResolver.php',
        'GuzzleHttp\\Psr7\\Utils' => __DIR__ . '/..' . '/guzzlehttp/psr7/src/Utils.php',
        'GuzzleHttp\\RedirectMiddleware' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/RedirectMiddleware.php',
        'GuzzleHttp\\RequestOptions' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/RequestOptions.php',
        'GuzzleHttp\\RetryMiddleware' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/RetryMiddleware.php',
        'GuzzleHttp\\TransferStats' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/TransferStats.php',
        'GuzzleHttp\\Utils' => __DIR__ . '/..' . '/guzzlehttp/guzzle/src/Utils.php',
        'IEXBase\\TronAPI\\Concerns\\ManagesTronscan' => __DIR__ . '/..' . '/iexbase/tron-api/src/Concerns/ManagesTronscan.php',
        'IEXBase\\TronAPI\\Concerns\\ManagesUniversal' => __DIR__ . '/..' . '/iexbase/tron-api/src/Concerns/ManagesUniversal.php',
        'IEXBase\\TronAPI\\Exception\\ErrorException' => __DIR__ . '/..' . '/iexbase/tron-api/src/Exception/ErrorException.php',
        'IEXBase\\TronAPI\\Exception\\NotFoundException' => __DIR__ . '/..' . '/iexbase/tron-api/src/Exception/NotFoundException.php',
        'IEXBase\\TronAPI\\Exception\\TRC20Exception' => __DIR__ . '/..' . '/iexbase/tron-api/src/Exception/TRC20Exception.php',
        'IEXBase\\TronAPI\\Exception\\TronException' => __DIR__ . '/..' . '/iexbase/tron-api/src/Exception/TronException.php',
        'IEXBase\\TronAPI\\Provider\\HttpProvider' => __DIR__ . '/..' . '/iexbase/tron-api/src/Provider/HttpProvider.php',
        'IEXBase\\TronAPI\\Provider\\HttpProviderInterface' => __DIR__ . '/..' . '/iexbase/tron-api/src/Provider/HttpProviderInterface.php',
        'IEXBase\\TronAPI\\Support\\Base58' => __DIR__ . '/..' . '/iexbase/tron-api/src/Support/Base58.php',
        'IEXBase\\TronAPI\\Support\\Base58Check' => __DIR__ . '/..' . '/iexbase/tron-api/src/Support/Base58Check.php',
        'IEXBase\\TronAPI\\Support\\BigInteger' => __DIR__ . '/..' . '/iexbase/tron-api/src/Support/BigInteger.php',
        'IEXBase\\TronAPI\\Support\\Crypto' => __DIR__ . '/..' . '/iexbase/tron-api/src/Support/Crypto.php',
        'IEXBase\\TronAPI\\Support\\Hash' => __DIR__ . '/..' . '/iexbase/tron-api/src/Support/Hash.php',
        'IEXBase\\TronAPI\\Support\\Keccak' => __DIR__ . '/..' . '/iexbase/tron-api/src/Support/Keccak.php',
        'IEXBase\\TronAPI\\Support\\Secp' => __DIR__ . '/..' . '/iexbase/tron-api/src/Support/Secp.php',
        'IEXBase\\TronAPI\\Support\\Utils' => __DIR__ . '/..' . '/iexbase/tron-api/src/Support/Utils.php',
        'IEXBase\\TronAPI\\TRC20Contract' => __DIR__ . '/..' . '/iexbase/tron-api/src/TRC20Contract.php',
        'IEXBase\\TronAPI\\TransactionBuilder' => __DIR__ . '/..' . '/iexbase/tron-api/src/TransactionBuilder.php',
        'IEXBase\\TronAPI\\Tron' => __DIR__ . '/..' . '/iexbase/tron-api/src/Tron.php',
        'IEXBase\\TronAPI\\TronAddress' => __DIR__ . '/..' . '/iexbase/tron-api/src/TronAddress.php',
        'IEXBase\\TronAPI\\TronAwareTrait' => __DIR__ . '/..' . '/iexbase/tron-api/src/TronAwareTrait.php',
        'IEXBase\\TronAPI\\TronInterface' => __DIR__ . '/..' . '/iexbase/tron-api/src/TronInterface.php',
        'IEXBase\\TronAPI\\TronManager' => __DIR__ . '/..' . '/iexbase/tron-api/src/TronManager.php',
        'Mdanter\\Ecc\\Crypto\\EcDH\\EcDH' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/EcDH/EcDH.php',
        'Mdanter\\Ecc\\Crypto\\EcDH\\EcDHInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/EcDH/EcDHInterface.php',
        'Mdanter\\Ecc\\Crypto\\Key\\PrivateKey' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/Key/PrivateKey.php',
        'Mdanter\\Ecc\\Crypto\\Key\\PrivateKeyInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/Key/PrivateKeyInterface.php',
        'Mdanter\\Ecc\\Crypto\\Key\\PublicKey' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/Key/PublicKey.php',
        'Mdanter\\Ecc\\Crypto\\Key\\PublicKeyInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/Key/PublicKeyInterface.php',
        'Mdanter\\Ecc\\Crypto\\Signature\\HasherInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/Signature/HasherInterface.php',
        'Mdanter\\Ecc\\Crypto\\Signature\\SignHasher' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/Signature/SignHasher.php',
        'Mdanter\\Ecc\\Crypto\\Signature\\Signature' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/Signature/Signature.php',
        'Mdanter\\Ecc\\Crypto\\Signature\\SignatureInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/Signature/SignatureInterface.php',
        'Mdanter\\Ecc\\Crypto\\Signature\\Signer' => __DIR__ . '/..' . '/mdanter/ecc/src/Crypto/Signature/Signer.php',
        'Mdanter\\Ecc\\Curves\\CurveFactory' => __DIR__ . '/..' . '/mdanter/ecc/src/Curves/CurveFactory.php',
        'Mdanter\\Ecc\\Curves\\NamedCurveFp' => __DIR__ . '/..' . '/mdanter/ecc/src/Curves/NamedCurveFp.php',
        'Mdanter\\Ecc\\Curves\\NistCurve' => __DIR__ . '/..' . '/mdanter/ecc/src/Curves/NistCurve.php',
        'Mdanter\\Ecc\\Curves\\SecgCurve' => __DIR__ . '/..' . '/mdanter/ecc/src/Curves/SecgCurve.php',
        'Mdanter\\Ecc\\EccFactory' => __DIR__ . '/..' . '/mdanter/ecc/src/EccFactory.php',
        'Mdanter\\Ecc\\Exception\\ExchangeException' => __DIR__ . '/..' . '/mdanter/ecc/src/Exception/ExchangeException.php',
        'Mdanter\\Ecc\\Exception\\NumberTheoryException' => __DIR__ . '/..' . '/mdanter/ecc/src/Exception/NumberTheoryException.php',
        'Mdanter\\Ecc\\Exception\\PointException' => __DIR__ . '/..' . '/mdanter/ecc/src/Exception/PointException.php',
        'Mdanter\\Ecc\\Exception\\PointNotOnCurveException' => __DIR__ . '/..' . '/mdanter/ecc/src/Exception/PointNotOnCurveException.php',
        'Mdanter\\Ecc\\Exception\\PointRecoveryException' => __DIR__ . '/..' . '/mdanter/ecc/src/Exception/PointRecoveryException.php',
        'Mdanter\\Ecc\\Exception\\PublicKeyException' => __DIR__ . '/..' . '/mdanter/ecc/src/Exception/PublicKeyException.php',
        'Mdanter\\Ecc\\Exception\\SignatureDecodeException' => __DIR__ . '/..' . '/mdanter/ecc/src/Exception/SignatureDecodeException.php',
        'Mdanter\\Ecc\\Exception\\SquareRootException' => __DIR__ . '/..' . '/mdanter/ecc/src/Exception/SquareRootException.php',
        'Mdanter\\Ecc\\Exception\\UnsupportedCurveException' => __DIR__ . '/..' . '/mdanter/ecc/src/Exception/UnsupportedCurveException.php',
        'Mdanter\\Ecc\\Math\\DebugDecorator' => __DIR__ . '/..' . '/mdanter/ecc/src/Math/DebugDecorator.php',
        'Mdanter\\Ecc\\Math\\GmpMath' => __DIR__ . '/..' . '/mdanter/ecc/src/Math/GmpMath.php',
        'Mdanter\\Ecc\\Math\\GmpMathInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Math/GmpMathInterface.php',
        'Mdanter\\Ecc\\Math\\MathAdapterFactory' => __DIR__ . '/..' . '/mdanter/ecc/src/Math/MathAdapterFactory.php',
        'Mdanter\\Ecc\\Math\\ModularArithmetic' => __DIR__ . '/..' . '/mdanter/ecc/src/Math/ModularArithmetic.php',
        'Mdanter\\Ecc\\Math\\NumberTheory' => __DIR__ . '/..' . '/mdanter/ecc/src/Math/NumberTheory.php',
        'Mdanter\\Ecc\\Primitives\\CurveFp' => __DIR__ . '/..' . '/mdanter/ecc/src/Primitives/CurveFp.php',
        'Mdanter\\Ecc\\Primitives\\CurveFpInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Primitives/CurveFpInterface.php',
        'Mdanter\\Ecc\\Primitives\\CurveParameters' => __DIR__ . '/..' . '/mdanter/ecc/src/Primitives/CurveParameters.php',
        'Mdanter\\Ecc\\Primitives\\GeneratorPoint' => __DIR__ . '/..' . '/mdanter/ecc/src/Primitives/GeneratorPoint.php',
        'Mdanter\\Ecc\\Primitives\\Point' => __DIR__ . '/..' . '/mdanter/ecc/src/Primitives/Point.php',
        'Mdanter\\Ecc\\Primitives\\PointInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Primitives/PointInterface.php',
        'Mdanter\\Ecc\\Random\\DebugDecorator' => __DIR__ . '/..' . '/mdanter/ecc/src/Random/DebugDecorator.php',
        'Mdanter\\Ecc\\Random\\HmacRandomNumberGenerator' => __DIR__ . '/..' . '/mdanter/ecc/src/Random/HmacRandomNumberGenerator.php',
        'Mdanter\\Ecc\\Random\\RandomGeneratorFactory' => __DIR__ . '/..' . '/mdanter/ecc/src/Random/RandomGeneratorFactory.php',
        'Mdanter\\Ecc\\Random\\RandomNumberGenerator' => __DIR__ . '/..' . '/mdanter/ecc/src/Random/RandomNumberGenerator.php',
        'Mdanter\\Ecc\\Random\\RandomNumberGeneratorInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Random/RandomNumberGeneratorInterface.php',
        'Mdanter\\Ecc\\Serializer\\Point\\CompressedPointSerializer' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/Point/CompressedPointSerializer.php',
        'Mdanter\\Ecc\\Serializer\\Point\\PointSerializerInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/Point/PointSerializerInterface.php',
        'Mdanter\\Ecc\\Serializer\\Point\\UncompressedPointSerializer' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/Point/UncompressedPointSerializer.php',
        'Mdanter\\Ecc\\Serializer\\PrivateKey\\DerPrivateKeySerializer' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/PrivateKey/DerPrivateKeySerializer.php',
        'Mdanter\\Ecc\\Serializer\\PrivateKey\\PemPrivateKeySerializer' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/PrivateKey/PemPrivateKeySerializer.php',
        'Mdanter\\Ecc\\Serializer\\PrivateKey\\PrivateKeySerializerInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/PrivateKey/PrivateKeySerializerInterface.php',
        'Mdanter\\Ecc\\Serializer\\PublicKey\\DerPublicKeySerializer' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/PublicKey/DerPublicKeySerializer.php',
        'Mdanter\\Ecc\\Serializer\\PublicKey\\Der\\Formatter' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/PublicKey/Der/Formatter.php',
        'Mdanter\\Ecc\\Serializer\\PublicKey\\Der\\Parser' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/PublicKey/Der/Parser.php',
        'Mdanter\\Ecc\\Serializer\\PublicKey\\PemPublicKeySerializer' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/PublicKey/PemPublicKeySerializer.php',
        'Mdanter\\Ecc\\Serializer\\PublicKey\\PublicKeySerializerInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/PublicKey/PublicKeySerializerInterface.php',
        'Mdanter\\Ecc\\Serializer\\Signature\\DerSignatureSerializer' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/Signature/DerSignatureSerializer.php',
        'Mdanter\\Ecc\\Serializer\\Signature\\DerSignatureSerializerInterface' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/Signature/DerSignatureSerializerInterface.php',
        'Mdanter\\Ecc\\Serializer\\Signature\\Der\\Formatter' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/Signature/Der/Formatter.php',
        'Mdanter\\Ecc\\Serializer\\Signature\\Der\\Parser' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/Signature/Der/Parser.php',
        'Mdanter\\Ecc\\Serializer\\Util\\CurveOidMapper' => __DIR__ . '/..' . '/mdanter/ecc/src/Serializer/Util/CurveOidMapper.php',
        'Mdanter\\Ecc\\Util\\BinaryString' => __DIR__ . '/..' . '/mdanter/ecc/src/Util/BinaryString.php',
        'Mdanter\\Ecc\\Util\\NumberSize' => __DIR__ . '/..' . '/mdanter/ecc/src/Util/NumberSize.php',
        'Psr\\Http\\Client\\ClientExceptionInterface' => __DIR__ . '/..' . '/psr/http-client/src/ClientExceptionInterface.php',
        'Psr\\Http\\Client\\ClientInterface' => __DIR__ . '/..' . '/psr/http-client/src/ClientInterface.php',
        'Psr\\Http\\Client\\NetworkExceptionInterface' => __DIR__ . '/..' . '/psr/http-client/src/NetworkExceptionInterface.php',
        'Psr\\Http\\Client\\RequestExceptionInterface' => __DIR__ . '/..' . '/psr/http-client/src/RequestExceptionInterface.php',
        'Psr\\Http\\Message\\MessageInterface' => __DIR__ . '/..' . '/psr/http-message/src/MessageInterface.php',
        'Psr\\Http\\Message\\RequestFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/RequestFactoryInterface.php',
        'Psr\\Http\\Message\\RequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/RequestInterface.php',
        'Psr\\Http\\Message\\ResponseFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/ResponseFactoryInterface.php',
        'Psr\\Http\\Message\\ResponseInterface' => __DIR__ . '/..' . '/psr/http-message/src/ResponseInterface.php',
        'Psr\\Http\\Message\\ServerRequestFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
        'Psr\\Http\\Message\\ServerRequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/ServerRequestInterface.php',
        'Psr\\Http\\Message\\StreamFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/StreamFactoryInterface.php',
        'Psr\\Http\\Message\\StreamInterface' => __DIR__ . '/..' . '/psr/http-message/src/StreamInterface.php',
        'Psr\\Http\\Message\\UploadedFileFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
        'Psr\\Http\\Message\\UploadedFileInterface' => __DIR__ . '/..' . '/psr/http-message/src/UploadedFileInterface.php',
        'Psr\\Http\\Message\\UriFactoryInterface' => __DIR__ . '/..' . '/psr/http-factory/src/UriFactoryInterface.php',
        'Psr\\Http\\Message\\UriInterface' => __DIR__ . '/..' . '/psr/http-message/src/UriInterface.php',
        'Simbi\\Tls\\Config\\Config' => __DIR__ . '/../..' . '/src/Config/Config.php',
        'Simbi\\Tls\\Config\\Database' => __DIR__ . '/../..' . '/src/Config/Database.php',
        'Simbi\\Tls\\Controllers\\AdminController' => __DIR__ . '/../..' . '/src/Controllers/AdminController.php',
        'Simbi\\Tls\\Controllers\\AuthController' => __DIR__ . '/../..' . '/src/Controllers/AuthController.php',
        'Simbi\\Tls\\Controllers\\InvestmentController' => __DIR__ . '/../..' . '/src/Controllers/InvestmentController.php',
        'Simbi\\Tls\\Controllers\\PaymentController' => __DIR__ . '/../..' . '/src/Controllers/PaymentController.php',
        'Simbi\\Tls\\Controllers\\TransactionController' => __DIR__ . '/../..' . '/src/Controllers/TransactionController.php',
        'Simbi\\Tls\\Controllers\\WalletController' => __DIR__ . '/../..' . '/src/Controllers/WalletController.php',
        'Simbi\\Tls\\CronJob\\Config\\CronConfig' => __DIR__ . '/../..' . '/../cronJob/src/Config/CronConfig.php',
        'Simbi\\Tls\\CronJob\\CronRunner' => __DIR__ . '/../..' . '/../cronJob/src/CronRunner.php',
        'Simbi\\Tls\\CronJob\\Services\\PaymentConfirmationService' => __DIR__ . '/../..' . '/../cronJob/src/Services/PaymentConfirmationService.php',
        'Simbi\\Tls\\CronJob\\Services\\TronGridService' => __DIR__ . '/../..' . '/../cronJob/src/Services/TronGridService.php',
        'Simbi\\Tls\\Frontend\\Config\\FrontendConfig' => __DIR__ . '/../..' . '/../frontend/src/Config/FrontendConfig.php',
        'Simbi\\Tls\\Frontend\\Services\\AjaxHandler' => __DIR__ . '/../..' . '/../frontend/src/Services/AjaxHandler.php',
        'Simbi\\Tls\\Frontend\\Services\\ApiService' => __DIR__ . '/../..' . '/../frontend/src/Services/ApiService.php',
        'Simbi\\Tls\\Frontend\\Services\\SessionService' => __DIR__ . '/../..' . '/../frontend/src/Services/SessionService.php',
        'Simbi\\Tls\\Frontend\\Utils\\FormatUtils' => __DIR__ . '/../..' . '/../frontend/src/Utils/FormatUtils.php',
        'Simbi\\Tls\\Frontend\\Utils\\ValidationUtils' => __DIR__ . '/../..' . '/../frontend/src/Utils/ValidationUtils.php',
        'Simbi\\Tls\\Middleware\\AdminMiddleware' => __DIR__ . '/../..' . '/src/Middleware/AdminMiddleware.php',
        'Simbi\\Tls\\Middleware\\AuthMiddleware' => __DIR__ . '/../..' . '/src/Middleware/AuthMiddleware.php',
        'Simbi\\Tls\\Repositories\\AdminRepository' => __DIR__ . '/../..' . '/src/Repositories/AdminRepository.php',
        'Simbi\\Tls\\Repositories\\TransactionRepository' => __DIR__ . '/../..' . '/src/Repositories/TransactionRepository.php',
        'Simbi\\Tls\\Repositories\\UserRepository' => __DIR__ . '/../..' . '/src/Repositories/UserRepository.php',
        'Simbi\\Tls\\Repositories\\WalletRepository' => __DIR__ . '/../..' . '/src/Repositories/WalletRepository.php',
        'Simbi\\Tls\\Services\\AuthService' => __DIR__ . '/../..' . '/src/Services/AuthService.php',
        'Simbi\\Tls\\Services\\ConfigurationService' => __DIR__ . '/../..' . '/src/Services/ConfigurationService.php',
        'Simbi\\Tls\\Services\\InvestmentService' => __DIR__ . '/../..' . '/src/Services/InvestmentService.php',
        'Simbi\\Tls\\Services\\Router' => __DIR__ . '/../..' . '/src/Services/Router.php',
        'Simbi\\Tls\\Services\\TransactionService' => __DIR__ . '/../..' . '/src/Services/TransactionService.php',
        'Simbi\\Tls\\Services\\TronGridService' => __DIR__ . '/../..' . '/src/Services/TronGridService.php',
        'Simbi\\Tls\\Services\\TronService' => __DIR__ . '/../..' . '/src/Services/TronService.php',
        'Simbi\\Tls\\Services\\WalletService' => __DIR__ . '/../..' . '/src/Services/WalletService.php',
        'Symfony\\Polyfill\\Mbstring\\Mbstring' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/Mbstring.php',
        'Web3\\Contract' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contract.php',
        'Web3\\Contracts\\Ethabi' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/Ethabi.php',
        'Web3\\Contracts\\SolidityType' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/SolidityType.php',
        'Web3\\Contracts\\Types\\Address' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/Types/Address.php',
        'Web3\\Contracts\\Types\\Boolean' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/Types/Boolean.php',
        'Web3\\Contracts\\Types\\Bytes' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/Types/Bytes.php',
        'Web3\\Contracts\\Types\\DynamicBytes' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/Types/DynamicBytes.php',
        'Web3\\Contracts\\Types\\IType' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/Types/IType.php',
        'Web3\\Contracts\\Types\\Integer' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/Types/Integer.php',
        'Web3\\Contracts\\Types\\Str' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/Types/Str.php',
        'Web3\\Contracts\\Types\\Uinteger' => __DIR__ . '/..' . '/iexbase/web3.php/src/Contracts/Types/Uinteger.php',
        'Web3\\Eth' => __DIR__ . '/..' . '/iexbase/web3.php/src/Eth.php',
        'Web3\\Formatters\\AddressFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/AddressFormatter.php',
        'Web3\\Formatters\\BigNumberFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/BigNumberFormatter.php',
        'Web3\\Formatters\\BooleanFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/BooleanFormatter.php',
        'Web3\\Formatters\\HexFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/HexFormatter.php',
        'Web3\\Formatters\\IFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/IFormatter.php',
        'Web3\\Formatters\\IntegerFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/IntegerFormatter.php',
        'Web3\\Formatters\\NumberFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/NumberFormatter.php',
        'Web3\\Formatters\\OptionalQuantityFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/OptionalQuantityFormatter.php',
        'Web3\\Formatters\\PostFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/PostFormatter.php',
        'Web3\\Formatters\\QuantityFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/QuantityFormatter.php',
        'Web3\\Formatters\\StringFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/StringFormatter.php',
        'Web3\\Formatters\\TransactionFormatter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Formatters/TransactionFormatter.php',
        'Web3\\Methods\\EthMethod' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/EthMethod.php',
        'Web3\\Methods\\Eth\\Accounts' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/Accounts.php',
        'Web3\\Methods\\Eth\\BlockNumber' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/BlockNumber.php',
        'Web3\\Methods\\Eth\\Call' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/Call.php',
        'Web3\\Methods\\Eth\\Coinbase' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/Coinbase.php',
        'Web3\\Methods\\Eth\\CompileLLL' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/CompileLLL.php',
        'Web3\\Methods\\Eth\\CompileSerpent' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/CompileSerpent.php',
        'Web3\\Methods\\Eth\\CompileSolidity' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/CompileSolidity.php',
        'Web3\\Methods\\Eth\\EstimateGas' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/EstimateGas.php',
        'Web3\\Methods\\Eth\\GasPrice' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GasPrice.php',
        'Web3\\Methods\\Eth\\GetBalance' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetBalance.php',
        'Web3\\Methods\\Eth\\GetBlockByHash' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetBlockByHash.php',
        'Web3\\Methods\\Eth\\GetBlockByNumber' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetBlockByNumber.php',
        'Web3\\Methods\\Eth\\GetBlockTransactionCountByHash' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetBlockTransactionCountByHash.php',
        'Web3\\Methods\\Eth\\GetBlockTransactionCountByNumber' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetBlockTransactionCountByNumber.php',
        'Web3\\Methods\\Eth\\GetCode' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetCode.php',
        'Web3\\Methods\\Eth\\GetFilterChanges' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetFilterChanges.php',
        'Web3\\Methods\\Eth\\GetFilterLogs' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetFilterLogs.php',
        'Web3\\Methods\\Eth\\GetLogs' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetLogs.php',
        'Web3\\Methods\\Eth\\GetStorageAt' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetStorageAt.php',
        'Web3\\Methods\\Eth\\GetTransactionByBlockHashAndIndex' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetTransactionByBlockHashAndIndex.php',
        'Web3\\Methods\\Eth\\GetTransactionByBlockNumberAndIndex' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetTransactionByBlockNumberAndIndex.php',
        'Web3\\Methods\\Eth\\GetTransactionByHash' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetTransactionByHash.php',
        'Web3\\Methods\\Eth\\GetTransactionCount' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetTransactionCount.php',
        'Web3\\Methods\\Eth\\GetTransactionReceipt' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetTransactionReceipt.php',
        'Web3\\Methods\\Eth\\GetUncleByBlockHashAndIndex' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetUncleByBlockHashAndIndex.php',
        'Web3\\Methods\\Eth\\GetUncleByBlockNumberAndIndex' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetUncleByBlockNumberAndIndex.php',
        'Web3\\Methods\\Eth\\GetUncleCountByBlockHash' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetUncleCountByBlockHash.php',
        'Web3\\Methods\\Eth\\GetUncleCountByBlockNumber' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetUncleCountByBlockNumber.php',
        'Web3\\Methods\\Eth\\GetWork' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/GetWork.php',
        'Web3\\Methods\\Eth\\Hashrate' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/Hashrate.php',
        'Web3\\Methods\\Eth\\Mining' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/Mining.php',
        'Web3\\Methods\\Eth\\NewBlockFilter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/NewBlockFilter.php',
        'Web3\\Methods\\Eth\\NewFilter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/NewFilter.php',
        'Web3\\Methods\\Eth\\NewPendingTransactionFilter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/NewPendingTransactionFilter.php',
        'Web3\\Methods\\Eth\\ProtocolVersion' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/ProtocolVersion.php',
        'Web3\\Methods\\Eth\\SendRawTransaction' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/SendRawTransaction.php',
        'Web3\\Methods\\Eth\\SendTransaction' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/SendTransaction.php',
        'Web3\\Methods\\Eth\\Sign' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/Sign.php',
        'Web3\\Methods\\Eth\\SubmitHashrate' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/SubmitHashrate.php',
        'Web3\\Methods\\Eth\\SubmitWork' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/SubmitWork.php',
        'Web3\\Methods\\Eth\\Syncing' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/Syncing.php',
        'Web3\\Methods\\Eth\\UninstallFilter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Eth/UninstallFilter.php',
        'Web3\\Methods\\IMethod' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/IMethod.php',
        'Web3\\Methods\\IRPC' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/IRPC.php',
        'Web3\\Methods\\JSONRPC' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/JSONRPC.php',
        'Web3\\Methods\\Net\\Listening' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Net/Listening.php',
        'Web3\\Methods\\Net\\PeerCount' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Net/PeerCount.php',
        'Web3\\Methods\\Net\\Version' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Net/Version.php',
        'Web3\\Methods\\Personal\\ListAccounts' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Personal/ListAccounts.php',
        'Web3\\Methods\\Personal\\LockAccount' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Personal/LockAccount.php',
        'Web3\\Methods\\Personal\\NewAccount' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Personal/NewAccount.php',
        'Web3\\Methods\\Personal\\SendTransaction' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Personal/SendTransaction.php',
        'Web3\\Methods\\Personal\\UnlockAccount' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Personal/UnlockAccount.php',
        'Web3\\Methods\\Shh\\AddToGroup' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/AddToGroup.php',
        'Web3\\Methods\\Shh\\GetFilterChanges' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/GetFilterChanges.php',
        'Web3\\Methods\\Shh\\GetMessages' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/GetMessages.php',
        'Web3\\Methods\\Shh\\HasIdentity' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/HasIdentity.php',
        'Web3\\Methods\\Shh\\NewFilter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/NewFilter.php',
        'Web3\\Methods\\Shh\\NewGroup' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/NewGroup.php',
        'Web3\\Methods\\Shh\\NewIdentity' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/NewIdentity.php',
        'Web3\\Methods\\Shh\\Post' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/Post.php',
        'Web3\\Methods\\Shh\\UninstallFilter' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/UninstallFilter.php',
        'Web3\\Methods\\Shh\\Version' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Shh/Version.php',
        'Web3\\Methods\\Web3\\ClientVersion' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Web3/ClientVersion.php',
        'Web3\\Methods\\Web3\\Sha3' => __DIR__ . '/..' . '/iexbase/web3.php/src/Methods/Web3/Sha3.php',
        'Web3\\Net' => __DIR__ . '/..' . '/iexbase/web3.php/src/Net.php',
        'Web3\\Personal' => __DIR__ . '/..' . '/iexbase/web3.php/src/Personal.php',
        'Web3\\Providers\\HttpProvider' => __DIR__ . '/..' . '/iexbase/web3.php/src/Providers/HttpProvider.php',
        'Web3\\Providers\\IProvider' => __DIR__ . '/..' . '/iexbase/web3.php/src/Providers/IProvider.php',
        'Web3\\Providers\\Provider' => __DIR__ . '/..' . '/iexbase/web3.php/src/Providers/Provider.php',
        'Web3\\RequestManagers\\HttpRequestManager' => __DIR__ . '/..' . '/iexbase/web3.php/src/RequestManagers/HttpRequestManager.php',
        'Web3\\RequestManagers\\IRequestManager' => __DIR__ . '/..' . '/iexbase/web3.php/src/RequestManagers/IRequestManager.php',
        'Web3\\RequestManagers\\RequestManager' => __DIR__ . '/..' . '/iexbase/web3.php/src/RequestManagers/RequestManager.php',
        'Web3\\Shh' => __DIR__ . '/..' . '/iexbase/web3.php/src/Shh.php',
        'Web3\\Utils' => __DIR__ . '/..' . '/iexbase/web3.php/src/Utils.php',
        'Web3\\Validators\\AddressValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/AddressValidator.php',
        'Web3\\Validators\\BlockHashValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/BlockHashValidator.php',
        'Web3\\Validators\\BooleanValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/BooleanValidator.php',
        'Web3\\Validators\\CallValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/CallValidator.php',
        'Web3\\Validators\\FilterValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/FilterValidator.php',
        'Web3\\Validators\\HexValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/HexValidator.php',
        'Web3\\Validators\\IValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/IValidator.php',
        'Web3\\Validators\\IdentityValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/IdentityValidator.php',
        'Web3\\Validators\\NonceValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/NonceValidator.php',
        'Web3\\Validators\\PostValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/PostValidator.php',
        'Web3\\Validators\\QuantityValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/QuantityValidator.php',
        'Web3\\Validators\\ShhFilterValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/ShhFilterValidator.php',
        'Web3\\Validators\\StringValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/StringValidator.php',
        'Web3\\Validators\\TagValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/TagValidator.php',
        'Web3\\Validators\\TransactionValidator' => __DIR__ . '/..' . '/iexbase/web3.php/src/Validators/TransactionValidator.php',
        'Web3\\Web3' => __DIR__ . '/..' . '/iexbase/web3.php/src/Web3.php',
        'kornrunner\\Keccak' => __DIR__ . '/..' . '/kornrunner/keccak/src/Keccak.php',
        'kornrunner\\Secp256k1' => __DIR__ . '/..' . '/kornrunner/secp256k1/src/Secp256k1.php',
        'kornrunner\\Serializer\\HexPrivateKeySerializer' => __DIR__ . '/..' . '/kornrunner/secp256k1/src/Serializer/HexPrivateKeySerializer.php',
        'kornrunner\\Serializer\\HexSignatureSerializer' => __DIR__ . '/..' . '/kornrunner/secp256k1/src/Serializer/HexSignatureSerializer.php',
        'kornrunner\\Signature\\Signature' => __DIR__ . '/..' . '/kornrunner/secp256k1/src/Signature/Signature.php',
        'kornrunner\\Signature\\SignatureInterface' => __DIR__ . '/..' . '/kornrunner/secp256k1/src/Signature/SignatureInterface.php',
        'kornrunner\\Signature\\Signer' => __DIR__ . '/..' . '/kornrunner/secp256k1/src/Signature/Signer.php',
        'phpseclib\\Crypt\\AES' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/AES.php',
        'phpseclib\\Crypt\\Base' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/Base.php',
        'phpseclib\\Crypt\\Blowfish' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/Blowfish.php',
        'phpseclib\\Crypt\\DES' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/DES.php',
        'phpseclib\\Crypt\\Hash' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/Hash.php',
        'phpseclib\\Crypt\\RC2' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/RC2.php',
        'phpseclib\\Crypt\\RC4' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/RC4.php',
        'phpseclib\\Crypt\\RSA' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/RSA.php',
        'phpseclib\\Crypt\\Random' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/Random.php',
        'phpseclib\\Crypt\\Rijndael' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/Rijndael.php',
        'phpseclib\\Crypt\\TripleDES' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/TripleDES.php',
        'phpseclib\\Crypt\\Twofish' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/Twofish.php',
        'phpseclib\\File\\ANSI' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/File/ANSI.php',
        'phpseclib\\File\\ASN1' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/File/ASN1.php',
        'phpseclib\\File\\ASN1\\Element' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/File/ASN1/Element.php',
        'phpseclib\\File\\X509' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/File/X509.php',
        'phpseclib\\Math\\BigInteger' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Math/BigInteger.php',
        'phpseclib\\Net\\SCP' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Net/SCP.php',
        'phpseclib\\Net\\SFTP' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Net/SFTP.php',
        'phpseclib\\Net\\SFTP\\Stream' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Net/SFTP/Stream.php',
        'phpseclib\\Net\\SSH1' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Net/SSH1.php',
        'phpseclib\\Net\\SSH2' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Net/SSH2.php',
        'phpseclib\\System\\SSH\\Agent' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/System/SSH/Agent.php',
        'phpseclib\\System\\SSH\\Agent\\Identity' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/System/SSH/Agent/Identity.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit8b5f9a5f51e1d2b8ef0de961298cf21e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit8b5f9a5f51e1d2b8ef0de961298cf21e::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit8b5f9a5f51e1d2b8ef0de961298cf21e::$classMap;

        }, null, ClassLoader::class);
    }
}
