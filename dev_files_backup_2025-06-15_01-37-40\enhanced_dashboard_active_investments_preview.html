<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Active Investments Dashboard Preview</title>
    <link rel="stylesheet" href="frontend/user/css/dashboard.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .preview-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .preview-header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin: 0 0 10px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .preview-header p {
            color: #6c757d;
            font-size: 1.2rem;
            margin: 0;
        }
        
        .demo-controls {
            display: flex;
            gap: 12px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .demo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .demo-btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }
        
        .card h3 {
            color: #2c3e50;
            margin: 0 0 20px 0;
            font-size: 1.25rem;
            font-weight: 600;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 12px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- Header -->
        <div class="preview-header">
            <h1>🚀 Enhanced Active Investments Dashboard</h1>
            <p>Modern, interactive, and user-friendly investment portfolio display</p>
        </div>

        <!-- Demo Controls -->
        <div class="demo-controls">
            <button class="demo-btn" onclick="showMultipleInvestments()">📊 Multiple Investments</button>
            <button class="demo-btn secondary" onclick="showSingleInvestment()">💎 Single Investment</button>
            <button class="demo-btn" onclick="showLoadingState()">⏳ Loading State</button>
            <button class="demo-btn" onclick="showEmptyState()">📭 Empty State</button>
            <button class="demo-btn danger" onclick="showErrorState()">⚠️ Error State</button>
        </div>

        <!-- Active Investments Card Preview -->
        <div class="card">
            <h3>Active Investments</h3>
            <div id="activeInvestmentsList">
                <!-- Demo content will be inserted here -->
            </div>
        </div>
        
        <!-- Implementation Info -->
        <div class="card">
            <h3>✨ Enhanced Features</h3>
            <ul style="line-height: 1.8; color: #495057;">
                <li><strong>🎨 Modern Design:</strong> Gradient backgrounds, smooth shadows, and hover effects</li>
                <li><strong>📊 Portfolio Overview:</strong> Investment summary with key statistics</li>
                <li><strong>📈 Animated Progress Bars:</strong> Shimmer effects and smooth transitions</li>
                <li><strong>🔄 Interactive Elements:</strong> Click animations and hover feedback</li>
                <li><strong>📱 Responsive Layout:</strong> Optimized for mobile, tablet, and desktop</li>
                <li><strong>⚡ Performance Indicators:</strong> Visual status badges and completion markers</li>
                <li><strong>🎯 Auto-refresh:</strong> Real-time updates every 5 minutes</li>
                <li><strong>💫 Smooth Animations:</strong> Entrance effects and loading states</li>
            </ul>
        </div>
    </div>

    <script>
        // Demo data
        const mockInvestments = [
            {
                id: 1,
                plan_name: "Basic Plan",
                amount: "1500.00",
                daily_return: "75.00",
                total_earned: "450.00",
                days_elapsed: 6,
                duration: 30
            },
            {
                id: 2,
                plan_name: "Premium Plan", 
                amount: "3000.00",
                daily_return: "210.00",
                total_earned: "840.00",
                days_elapsed: 4,
                duration: 30
            }
        ];

        function showMultipleInvestments() {
            const container = document.getElementById('activeInvestmentsList');
            
            const totalInvested = mockInvestments.reduce((sum, inv) => sum + parseFloat(inv.amount), 0);
            const totalEarned = mockInvestments.reduce((sum, inv) => sum + parseFloat(inv.total_earned), 0);
            const dailyEarnings = mockInvestments.reduce((sum, inv) => sum + parseFloat(inv.daily_return), 0);
            
            container.innerHTML = `
                <div class="investment-summary-card">
                    <h4 style="margin: 0 0 16px 0; font-size: 1.3rem; font-weight: 600;">💼 Portfolio Overview</h4>
                    <div class="summary-stats">
                        <div class="summary-stat">
                            <div class="summary-stat-value">${mockInvestments.length}</div>
                            <div class="summary-stat-label">Active Plans</div>
                        </div>
                        <div class="summary-stat">
                            <div class="summary-stat-value">${totalInvested.toFixed(2)}</div>
                            <div class="summary-stat-label">Total Invested</div>
                        </div>
                        <div class="summary-stat">
                            <div class="summary-stat-value">+${dailyEarnings.toFixed(2)}</div>
                            <div class="summary-stat-label">Daily Returns</div>
                        </div>
                        <div class="summary-stat">
                            <div class="summary-stat-value">${totalEarned.toFixed(2)}</div>
                            <div class="summary-stat-label">Total Earned</div>
                        </div>
                    </div>
                </div>
                <div class="investment-grid">
                    ${mockInvestments.map((investment, index) => {
                        const progressPercentage = Math.round((investment.days_elapsed / investment.duration) * 100);
                        const daysRemaining = Math.max(0, investment.duration - investment.days_elapsed);
                        
                        return `
                            <div class="investment-item ${index === 0 ? 'pulse' : ''}" data-investment-id="${investment.id}">
                                <div class="investment-status active">🔄 Active</div>
                                <div class="investment-info">
                                    <div class="investment-details">
                                        <div class="investment-plan">${investment.plan_name}</div>
                                        <div class="investment-amount">${parseFloat(investment.amount).toFixed(2)} USDT</div>
                                        <div class="performance-indicator positive">Performance: Excellent</div>
                                    </div>
                                    <div class="investment-earnings">
                                        <div class="daily-return">+${parseFloat(investment.daily_return).toFixed(2)} USDT/day</div>
                                        <div class="total-earned">${parseFloat(investment.total_earned).toFixed(2)} USDT earned</div>
                                    </div>
                                </div>
                                <div class="investment-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                                    </div>
                                    <div class="progress-text" data-percentage="${progressPercentage}%">
                                        <span>Day ${investment.days_elapsed} of ${investment.duration}</span>
                                        <span style="font-size: 0.8rem; color: #6c757d;">${daysRemaining} days remaining</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
            
            setTimeout(() => {
                const investmentItems = container.querySelectorAll('.investment-item');
                investmentItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('new-investment');
                    }, index * 100);
                });
            }, 100);
        }

        function showSingleInvestment() {
            const container = document.getElementById('activeInvestmentsList');
            const investment = mockInvestments[0];
            const progressPercentage = Math.round((investment.days_elapsed / investment.duration) * 100);
            const daysRemaining = Math.max(0, investment.duration - investment.days_elapsed);
            
            container.innerHTML = `
                <div class="investment-grid">
                    <div class="investment-item pulse" data-investment-id="${investment.id}">
                        <div class="investment-status active">🔄 Active</div>
                        <div class="investment-info">
                            <div class="investment-details">
                                <div class="investment-plan">${investment.plan_name}</div>
                                <div class="investment-amount">${parseFloat(investment.amount).toFixed(2)} USDT</div>
                                <div class="performance-indicator positive">Performance: Excellent</div>
                            </div>
                            <div class="investment-earnings">
                                <div class="daily-return">+${parseFloat(investment.daily_return).toFixed(2)} USDT/day</div>
                                <div class="total-earned">${parseFloat(investment.total_earned).toFixed(2)} USDT earned</div>
                            </div>
                        </div>
                        <div class="investment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                            </div>
                            <div class="progress-text" data-percentage="${progressPercentage}%">
                                <span>Day ${investment.days_elapsed} of ${investment.duration}</span>
                                <span style="font-size: 0.8rem; color: #6c757d;">${daysRemaining} days remaining</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            setTimeout(() => {
                container.querySelector('.investment-item').classList.add('new-investment');
            }, 100);
        }

        function showLoadingState() {
            const container = document.getElementById('activeInvestmentsList');
            container.innerHTML = `
                <div class="loading-investments">
                    <div class="loading-spinner-container">
                        <div class="loading-spinner-ring"></div>
                    </div>
                    <p class="loading-text">Loading your investments<span class="loading-dots"></span></p>
                </div>
            `;
        }

        function showEmptyState() {
            const container = document.getElementById('activeInvestmentsList');
            container.innerHTML = `
                <div class="no-data">
                    <h4>No Active Investments</h4>
                    <p>Start your investment journey today!<br>
                    Choose from our available investment plans to begin earning daily returns.</p>
                </div>
            `;
        }

        function showErrorState() {
            const container = document.getElementById('activeInvestmentsList');
            container.innerHTML = `
                <div class="error">
                    <h4>Failed to Load Investments</h4>
                    <p>Please check your connection and try again.</p>
                    <button onclick="showMultipleInvestments()" class="btn btn-primary" style="margin-top: 12px;">
                        🔄 Retry
                    </button>
                </div>
            `;
        }

        // Add click handlers for investment interactions
        document.addEventListener('click', (e) => {
            const investmentItem = e.target.closest('.investment-item');
            if (investmentItem) {
                investmentItem.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    investmentItem.style.transform = '';
                }, 150);
            }
        });

        // Auto-demo on page load
        setTimeout(() => {
            showMultipleInvestments();
        }, 500);
    </script>
</body>
</html>
