<?php
/**
 * Payment Confirmation API
 * Standalone API for confirming USDT payments from TronGrid
 * 
 * Accepts: wallet_id, amount
 * Returns: payment confirmation status and updates database
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Enable CORS for API access
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Load configuration
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/TronGridService.php';
require_once __DIR__ . '/PaymentConfirmationService.php';

try {
    // Initialize services
    $tronGridService = new TronGridService();
    $paymentService = new PaymentConfirmationService();
    
    // Route requests
    $requestMethod = $_SERVER['REQUEST_METHOD'];
    $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    
    switch ($requestMethod) {
        case 'POST':
            handlePaymentConfirmation($paymentService, $tronGridService);
            break;
            
        case 'GET':
            if (strpos($requestUri, '/status') !== false) {
                handleStatusCheck($paymentService);
            } else {
                handleHealthCheck($tronGridService);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
    error_log("Payment API error: " . $e->getMessage());
}

/**
 * Handle payment confirmation request
 */
function handlePaymentConfirmation($paymentService, $tronGridService) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $walletId = $input['wallet_id'] ?? null;
    $amount = $input['amount'] ?? null;
    $transactionHash = $input['transaction_hash'] ?? null;
    
    if (!$walletId || !$amount) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'wallet_id and amount are required'
        ]);
        return;
    }
    
    if (!is_numeric($amount) || $amount <= 0) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Amount must be a positive number'
        ]);
        return;
    }
    
    // Process payment confirmation
    $result = $paymentService->confirmPayment($walletId, (float)$amount, $transactionHash);
    
    // Set appropriate HTTP status code
    if (!$result['success']) {
        http_response_code($result['code'] ?? 400);
    }
    
    echo json_encode($result);
}

/**
 * Handle status check request
 */
function handleStatusCheck($paymentService) {
    $transactionHash = $_GET['hash'] ?? null;
    
    if (!$transactionHash) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'transaction hash is required'
        ]);
        return;
    }
    
    $result = $paymentService->getTransactionStatus($transactionHash);
    echo json_encode($result);
}

/**
 * Handle health check request
 */
function handleHealthCheck($tronGridService) {
    $health = $tronGridService->healthCheck();
    echo json_encode([
        'success' => true,
        'status' => 'operational',
        'timestamp' => date('c'),
        'trongrid_status' => $health
    ]);
}
