<?php

namespace Simbi\Tls\Controllers;

use Simbi\Tls\Services\AuthService;

class AuthController
{
    private AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }    public function register(array $data): array
    {
        $email = $data['email'] ?? null;
        $password = $data['password'] ?? null;

        if (!$email || !$password) {
            return ['error' => 'Email and password are required', 'code' => 400];
        }

        return $this->authService->register($email, $password);
    }

    public function login(array $data): array
    {
        $email = $data['email'] ?? null;
        $password = $data['password'] ?? null;

        if (!$email || !$password) {
            return ['error' => 'Email and password are required', 'code' => 400];
        }

        return $this->authService->login($email, $password);
    }

    public function me(array $user): array
    {
        return [
            'success' => true,
            'user' => [
                'id' => $user['id'],
                'email' => $user['email'],
                'created_at' => $user['created_at']
            ]
        ];
    }

    public function requestPasswordReset(array $data): array
    {
        $email = $data['email'] ?? null;

        if (!$email) {
            return ['error' => 'Email is required', 'code' => 400];
        }

        return $this->authService->requestPasswordReset($email);
    }

    public function resetPassword(array $data): array
    {
        $token = $data['token'] ?? null;
        $newPassword = $data['new_password'] ?? null;

        if (!$token || !$newPassword) {
            return ['error' => 'Token and new password are required', 'code' => 400];
        }

        return $this->authService->resetPassword($token, $newPassword);
    }    
    
    public function changePassword(array $data, array $user): array
    {
        $currentPassword = $data['current_password'] ?? null;
        $newPassword = $data['new_password'] ?? null;

        if (!$currentPassword || !$newPassword) {
            return ['error' => 'Current password and new password are required', 'code' => 400];
        }

        return $this->authService->changePassword($user['id'], $currentPassword, $newPassword);
    }
}
