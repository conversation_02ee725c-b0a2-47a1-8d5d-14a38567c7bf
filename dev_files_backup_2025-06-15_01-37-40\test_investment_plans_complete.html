<!DOCTYPE html>
<html>
<head>
    <title>Make Investment Page Full Test</title>
    <link rel="stylesheet" href="frontend/css/style.css">
    <link rel="stylesheet" href="frontend/css/dashboard.css">
    <style>
        .test-info { 
            background: #e3f2fd; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px; 
            border-left: 4px solid #2196f3;
        }
        .error-info { 
            background: #ffebee; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px; 
            border-left: 4px solid #f44336;
        }
    </style>
</head>
<body>
    <h1>Investment Plans Styling Test</h1>
    
    <div class="test-info">
        <h3>CSS Loading Test</h3>
        <p>If you see styled cards below, CSS is working correctly.</p>
    </div>
    
    <!-- Test the exact structure from make_investment.php -->
    <div class="make-investment-page">
        <div class="page-header">
            <h2>Make Investment</h2>
            <p>Choose your investment plan and start earning daily returns</p>
        </div>

        <!-- Current Balance Display -->
        <div class="balance-overview">
            <div class="balance-card">
                <h3>Available Balance</h3>
                <div class="balance-amount">5.00</div>
                <div class="balance-currency">USDT</div>
            </div>
        </div>

        <!-- Investment Plans -->
        <div class="investment-plans-section">
            <h3>Choose Investment Plan</h3>
            <div class="investment-plans" id="testPlansContainer">
                <!-- Test plans will be rendered here -->
            </div>
        </div>
    </div>

    <div class="test-info">
        <h3>JavaScript Rendering Test</h3>
        <button onclick="renderTestPlans()">Render Test Plans</button>
        <button onclick="testAPICall()">Test API Call</button>
        <div id="testResult"></div>
    </div>

    <script>
        // Copy the apiCall function from make_investment.js
        async function apiCall(endpoint, data = {}) {
            const url = `frontend/ajax.php`;
            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: endpoint,
                    ...data
                })
            };

            const response = await fetch(url, options);
            return await response.json();
        }

        async function testAPICall() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<p>Testing API call...</p>';
            
            try {
                const response = await apiCall('get_investment_plans');
                console.log('API Response:', response);
                
                if (response.success) {
                    renderActualPlans(response.plans);
                    resultDiv.innerHTML = '<p style="color: green;">✅ API call successful! Plans rendered.</p>';
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">❌ API call failed: ' + (response.message || response.error) + '</p>';
                }
            } catch (error) {
                console.error('API Error:', error);
                resultDiv.innerHTML = '<p style="color: red;">❌ API call error: ' + error.message + '</p>';
            }
        }

        function renderTestPlans() {
            const container = document.getElementById('testPlansContainer');
            const testPlans = [
                {
                    plan_code: 'basic',
                    plan_name: 'Basic Plan',
                    daily_rate: 0.0167,
                    duration: 30,
                    min_amount: 600,
                    features: ['1.67% daily simple interest', 'Non-compounding returns', '30-day investment duration', 'Low risk profile', 'Perfect for beginners'],
                    is_active: true,
                    is_featured: true
                },
                {
                    plan_code: 'premium',
                    plan_name: 'Premium Plan',
                    daily_rate: 0.025,
                    duration: 25,
                    min_amount: 2000,
                    features: ['2.5% daily returns', 'Shorter duration', 'Higher minimum investment', 'Medium risk profile', 'For experienced traders'],
                    is_active: false,
                    is_featured: false
                }
            ];
            
            renderActualPlans(testPlans);
        }

        function renderActualPlans(plans) {
            const container = document.getElementById('testPlansContainer');
            if (!container) return;
            
            container.innerHTML = '';
            
            plans.forEach(plan => {
                const isBasic = plan.plan_code === 'basic';
                const isDisabled = !plan.is_active || !isBasic;
                
                const planCard = document.createElement('div');
                planCard.className = `plan-card ${isDisabled ? 'disabled' : ''}`;
                planCard.dataset.plan = plan.plan_code;
                
                planCard.innerHTML = `
                    <div class="plan-header">
                        <div class="plan-name">${plan.plan_name}</div>
                        ${plan.is_featured ? '<div class="plan-badge">Most Popular</div>' : ''}
                        ${isDisabled && !isBasic ? '<div class="plan-badge coming-soon">Coming Soon</div>' : ''}
                        <div class="plan-rate">${(plan.daily_rate * 100).toFixed(1)}%</div>
                        <div class="plan-period">Daily</div>
                        <div class="plan-duration">${plan.duration} Days</div>
                        <div class="plan-minimum-header">Min: ${plan.min_amount} USDT</div>
                    </div>
                    <div class="plan-details">
                        <div class="plan-features">
                            ${plan.features.map(feature => `<li>✅ ${feature}</li>`).join('')}
                        </div>
                    </div>
                    ${isDisabled && !isBasic ? '<div class="plan-overlay"><span>Coming Soon</span></div>' : ''}
                `;
                
                container.appendChild(planCard);
            });
        }

        // Auto-render test plans on load
        window.addEventListener('load', function() {
            setTimeout(renderTestPlans, 500);
        });
    </script>
</body>
</html>
