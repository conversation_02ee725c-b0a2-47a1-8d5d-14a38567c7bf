<?php
// Simple test to verify PSR-4 autoloading is working
require_once __DIR__ . '/backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Config\FrontendConfig;
use Simbi\Tls\Frontend\Services\SessionService;
use Simbi\Tls\Frontend\Services\ApiService;

echo "Testing PSR-4 classes...\n";

try {
    // Test FrontendConfig init method
    FrontendConfig::init();
    echo "✅ FrontendConfig::init() works\n";
    
    // Test SessionService
    SessionService::init();
    echo "✅ SessionService::init() works\n";
    
    // Test ApiService
    $api = new ApiService();
    echo "✅ ApiService instantiation works\n";
    
    echo "\n🎉 All PSR-4 classes are working correctly!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
