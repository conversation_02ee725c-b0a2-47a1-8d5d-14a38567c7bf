<?php

namespace Simbi\Tls\Services;

use IEXBase\TronAPI\Tron;
use Simbi\Tls\Config\Config;
use Exception;

class TronService
{
    private Tron $tron;
    private string $masterAddress;
    private string $masterPrivateKey;
    private string $usdtContract;

    public function __construct()
    {
        Config::load();
        
        // Get network from environment and set appropriate API URL
        $network = Config::get('TRON_NETWORK', 'nile');
        $apiUrl = match($network) {
            'mainnet' => Config::get('TRON_MAINNET_URL', 'https://api.trongrid.io'),
            'shasta' => Config::get('TRON_SHASTA_URL', 'https://api.shasta.trongrid.io'),
            'nile' => Config::get('TRON_NILE_URL', 'https://nile.trongrid.io'),
            default => Config::get('TRON_BASE_URL', 'https://nile.trongrid.io')
        };

        $fullNode = new \IEXBase\TronAPI\Provider\HttpProvider($apiUrl);
        $solidityNode = new \IEXBase\TronAPI\Provider\HttpProvider($apiUrl);
        $eventServer = new \IEXBase\TronAPI\Provider\HttpProvider($apiUrl);
        
        $this->tron = new Tron($fullNode, $solidityNode, $eventServer);
          $this->masterAddress = Config::get('MASTER_ADDRESS', '');
        $this->masterPrivateKey = Config::get('MASTER_PRIVATE_KEY', '');
        
        // Get network-specific contract from environment variables
        $networkContract = match($network) {
            'mainnet' => Config::get('USDT_CONTRACT_MAINNET', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'),
            'nile' => Config::get('USDT_CONTRACT_NILE', 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'),
            'shasta' => Config::get('USDT_CONTRACT_SHASTA', 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs'),
            default => Config::get('USDT_CONTRACT_NILE', 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf')
        };
        
        // Allow override with legacy USDT_CONTRACT environment variable
        $this->usdtContract = Config::get('USDT_CONTRACT', $networkContract);
    }
      public function generateAddress(): array
    {
        try {
            $account = $this->tron->generateAddress();
            
            // Handle TronAddress object (which is the actual return type)
            if (is_object($account)) {
                // Access the protected response property to get the address data
                $reflection = new \ReflectionClass($account);
                $responseProperty = $reflection->getProperty('response');
                $responseProperty->setAccessible(true);
                $response = $responseProperty->getValue($account);
                
                return [
                    'address' => $response['address_base58'], // TRON address format (starts with 'T')
                    'hexAddress' => $response['address_hex'], // Hex address format (starts with '41')
                    'privateKey' => $response['private_key']
                ];
            }
            // Legacy fallback for array format (though TronAPI returns object)
            else if (is_array($account)) {
                return [
                    'address' => $account['address_base58'] ?? $account['base58Check'] ?? $account['address'] ?? $account['base58'],
                    'hexAddress' => $account['address_hex'] ?? $account['hex'] ?? $account['address'] ?? $account['base58'],
                    'privateKey' => $account['privateKey'] ?? $account['private_key']
                ];
            } else {
                // If it's an object, try to get the properties
                return [
                    'address' => $account->getAddress(),
                    'hexAddress' => $account->getHexAddress() ?? $account->getAddress(),
                    'privateKey' => $account->getPrivateKey()
                ];
            }
        } catch (Exception $e) {
            error_log("Failed to generate TRON address: " . $e->getMessage());
            throw new Exception("Failed to generate address: " . $e->getMessage());        }
    }

    public function getBalance(string $address): string
    {
        try {
            if (empty($this->usdtContract)) {
                // Return TRX balance if no USDT contract is configured
                $balance = $this->tron->getBalance($address);
                return (string)$balance;
            }

            // For USDT balance, we need to use contract calls differently
            // This is a simplified version - you may need to adjust based on the exact API
            $contract = $this->tron->contract($this->usdtContract);
            return $contract->balanceOf($address)->call();
        } catch (Exception $e) {
            error_log("Failed to get balance: " . $e->getMessage());
            return "0"; // Return 0 if balance check fails
        }
    }

    /**
     * Get USDT balance using live TronGrid API data
     */
    public function getUSDTBalance(string $address): string
    {        try {
            if (empty($this->usdtContract)) {
                error_log("USDT contract not configured");
                return "0";
            }

            $contract = $this->tron->contract($this->usdtContract);
            
            // Check if contract exists and is callable
            if (!$contract) {
                error_log("Failed to get USDT contract instance");
                return "0";
            }
            
            $balanceCall = $contract->balanceOf($address);
            if (!$balanceCall) {
                error_log("Failed to create balance call for address: $address");
                return "0";
            }
            
            $balance = $balanceCall->call();
            
            // Convert from raw balance to readable format (6 decimals for USDT)
            $decimals = 6;
            $divisor = bcpow('10', (string)$decimals);
            $readableBalance = bcdiv((string)$balance, $divisor, $decimals);
            
            return $readableBalance;
        } catch (Exception $e) {
            error_log("Failed to get USDT balance: " . $e->getMessage());
            return "0";
        }
    }

    public function sweepFunds(string $fromPrivateKey, string $fromAddress): array
    {
        try {
            // Validate inputs
            if (empty($fromPrivateKey)) {
                return ['status' => 'error', 'message' => 'Private key is required'];
            }

            if (empty($fromAddress)) {
                return ['status' => 'error', 'message' => 'Source address is required'];
            }

            if (empty($this->masterAddress)) {
                return ['status' => 'error', 'message' => 'Master address not configured in environment'];
            }

            // Log sweep attempt for debugging
            error_log("Attempting to sweep funds from {$fromAddress} to {$this->masterAddress}");

            // Set private key for transaction signing
            $this->tron->setPrivateKey($fromPrivateKey);

            if (empty($this->usdtContract)) {
                // Sweep TRX instead of USDT
                error_log("USDT contract not configured, sweeping TRX instead");

                try {
                    $balance = $this->tron->getBalance($fromAddress);
                    error_log("TRX balance for {$fromAddress}: {$balance}");

                    if ($balance > 0) {
                        // Reserve some TRX for transaction fees (approximately 1 TRX)
                        $feeReserve = 1000000; // 1 TRX in SUN
                        $sweepAmount = max(0, $balance - $feeReserve);

                        if ($sweepAmount > 0) {
                            $tx = $this->tron->sendTrx($this->masterAddress, $sweepAmount, $fromAddress);
                            error_log("TRX sweep transaction: " . json_encode($tx));
                            return ['status' => 'swept', 'tx' => $tx, 'amount' => $sweepAmount, 'currency' => 'TRX'];
                        } else {
                            return ['status' => 'no funds to sweep', 'amount' => '0', 'message' => 'Insufficient balance after fee reserve'];
                        }
                    } else {
                        return ['status' => 'no funds to sweep', 'amount' => '0', 'currency' => 'TRX'];
                    }
                } catch (Exception $trxError) {
                    error_log("TRX sweep error: " . $trxError->getMessage());
                    return ['status' => 'error', 'message' => 'TRX sweep failed: ' . $trxError->getMessage()];
                }
            }

            // Sweep USDT tokens
            try {
                error_log("Sweeping USDT tokens using contract: {$this->usdtContract}");

                $contract = $this->tron->contract($this->usdtContract);
                $balance = $contract->balanceOf($fromAddress)->call();

                error_log("USDT balance for {$fromAddress}: {$balance}");

                if ($balance > 0) {
                    $tx = $contract->transfer($this->masterAddress, $balance)->send();
                    error_log("USDT sweep transaction: " . json_encode($tx));
                    return ['status' => 'swept', 'tx' => $tx, 'amount' => $balance, 'currency' => 'USDT'];
                } else {
                    return ['status' => 'no funds to sweep', 'amount' => '0', 'currency' => 'USDT'];
                }
            } catch (Exception $usdtError) {
                error_log("USDT sweep error: " . $usdtError->getMessage());
                return ['status' => 'error', 'message' => 'USDT sweep failed: ' . $usdtError->getMessage()];
            }

        } catch (Exception $e) {
            error_log("Failed to sweep funds: " . $e->getMessage());
            error_log("Sweep funds stack trace: " . $e->getTraceAsString());
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    public function withdraw(string $toAddress, string $amount): array
    {
        try {
            if (empty($this->masterPrivateKey)) {
                throw new Exception("Master private key not configured");
            }

            $this->tron->setPrivateKey($this->masterPrivateKey);

            if (empty($this->usdtContract)) {
                // Send TRX instead of USDT
                $amountSun = bcmul($amount, '1000000'); // Convert TRX to SUN
                $tx = $this->tron->sendTrx($toAddress, $amountSun);
                return ['status' => 'withdrawn', 'tx' => $tx, 'amount' => $amount];
            }

            $contract = $this->tron->contract($this->usdtContract);
            $value = bcmul($amount, '1000000'); // USDT decimals
            $tx = $contract->transfer($toAddress, $value)->send();

            return ['status' => 'withdrawn', 'tx' => $tx, 'amount' => $amount];
        } catch (Exception $e) {
            error_log("Failed to withdraw: " . $e->getMessage());
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    public function validateAddress(string $address): bool
    {
        try {
            return $this->tron->isAddress($address);
        } catch (Exception $e) {
            return false;
        }
    }
}
