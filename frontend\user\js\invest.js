// Investment Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initInvestPage();
});

let userBalance = 0;
let selectedPlan = 'basic';
let investmentPlans = {}; // Will be loaded from API
let systemConfig = null; // Will store system configuration

function initInvestPage() {
    loadSystemConfiguration();
    loadInvestmentPlans();
    loadUserBalance();
    loadActiveInvestments();
    loadInvestmentHistory();
    initFooterMenu();
}

function setupInvestmentForm() {
    const investForm = document.getElementById('investmentForm');
    const calculateBtn = document.getElementById('calculateBtn');
    const investAmountInput = document.getElementById('investAmount');
    const planCards = document.querySelectorAll('.plan-card');

    // Only setup plan selection if plan cards exist
    if (planCards.length > 0) {
        // Plan selection
        planCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove active class from all cards
                planCards.forEach(c => c.classList.remove('active'));
                // Add active class to clicked card
                this.classList.add('active');
                
                selectedPlan = this.dataset.plan;
                const selectedPlanInput = document.getElementById('selectedPlan');
                if (selectedPlanInput) {
                    selectedPlanInput.value = selectedPlan;
                }
                
                // Update minimum amount
                const minAmount = investmentPlans[selectedPlan].minAmount;
                if (investAmountInput) {
                    investAmountInput.min = minAmount;
                    investAmountInput.placeholder = `Enter amount (minimum ${minAmount} USDT)`;
                }
                
                // Clear previous calculations
                const investmentSummary = document.getElementById('investmentSummary');
                const investBtn = document.getElementById('investBtn');
                if (investmentSummary) investmentSummary.style.display = 'none';
                if (investBtn) investBtn.disabled = true;
            });
        });

        // Set default selected plan (only if basic plan card exists)
        const basicPlanCard = document.querySelector('[data-plan="basic"]');
        if (basicPlanCard) {
            basicPlanCard.classList.add('active');
        }
    }

    // Calculate button
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateReturns);
    }

    // Form submission
    if (investForm) {
        investForm.addEventListener('submit', handleInvestment);
    }

    // Amount input validation
    if (investAmountInput) {
        investAmountInput.addEventListener('input', function() {
            const investmentSummary = document.getElementById('investmentSummary');
            const investBtn = document.getElementById('investBtn');
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (investBtn) investBtn.disabled = true;
        });
    }
}

async function loadUserBalance() {
    try {
        const response = await apiCall('get_balance');
        if (response.success) {
            userBalance = parseFloat(response.balance) || 0;
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) {
                balanceElement.textContent = userBalance.toFixed(2);
            }
            
            // Update balance-based container content
            updateBalanceBasedContent();
        } else {
            console.error('Error loading balance:', response.message);
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) balanceElement.textContent = 'Error';
            
            // Show error message
            showBalanceError();
        }
    } catch (error) {
        console.error('Error loading balance:', error);
        const balanceElement = document.getElementById('availableBalance');
        if (balanceElement) balanceElement.textContent = 'Error';
        
        // Show error message
        showBalanceError();
    }
}

// Update balance-based container content
function updateBalanceBasedContent() {
    const balanceBasedContainer = document.getElementById('balanceBasedContainer');
    const insufficientMsg = document.getElementById('insufficientBalanceMsg');
    const investForm = document.getElementById('investmentForm');
    
    if (!balanceBasedContainer) return;
    
    if (userBalance < 600) {
        // Show insufficient balance message
        balanceBasedContainer.innerHTML = `
            <div class="insufficient-balance-container">
                <div class="warning-card">
                    <div class="warning-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#ffc107" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                    </div>
                    <h4>Insufficient Balance</h4>
                    <p>Your current balance is <strong>${userBalance.toFixed(2)} USDT</strong></p>
                    <p>You need at least <strong>600 USDT</strong> to start investing.</p>
                    <div class="balance-needed">
                        <span class="needed-amount">Amount needed: <strong>${(600 - userBalance).toFixed(2)} USDT</strong></span>
                    </div>
                    <div class="warning-actions">
                        <a href="deposit.php" class="btn btn-primary">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            Add Funds
                        </a>
                        <a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                    </div>
                </div>
            </div>
        `;
        
        // Hide the separate insufficient message and form
        if (insufficientMsg) insufficientMsg.style.display = 'none';
        if (investForm) investForm.style.display = 'none';
    } else {
        // Show investment form
        balanceBasedContainer.innerHTML = `
            <div class="investment-available-container">
                <div class="balance-status-card">
                    <div class="status-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#28a745" stroke-width="2">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22,4 12,14.01 9,11.01"></polyline>
                        </svg>
                    </div>
                    <div class="status-content">
                        <h4>Ready to Invest</h4>
                        <p>Your balance: <strong>${userBalance.toFixed(2)} USDT</strong></p>
                        <p>You can start investing now!</p>
                    </div>
                </div>                <div class="investment-actions">
                    <a href="make_investment.php" class="btn btn-primary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Start Investment
                    </a><a href="deposit.php" class="btn btn-outline">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Add More Funds
                    </a>
                </div>
            </div>
        `;
        
        // Hide the separate insufficient message and show form
        if (insufficientMsg) insufficientMsg.style.display = 'none';
        if (investForm) investForm.style.display = 'block';
    }
}

function calculateReturns() {
    const amountInput = document.getElementById('investAmount');
    if (!amountInput) return;
    
    const amount = parseFloat(amountInput.value);
    
    if (!amount || amount < investmentPlans[selectedPlan].minAmount) {
        showMessage(`Minimum investment for ${investmentPlans[selectedPlan].name} is ${investmentPlans[selectedPlan].minAmount} USDT`, 'error');
        return;
    }
    
    if (amount > userBalance) {
        showMessage('Insufficient balance for this investment amount', 'error');
        return;
    }
    
    const plan = investmentPlans[selectedPlan];
    const dailyReturn = amount * plan.dailyRate;
    const totalReturn = dailyReturn * plan.duration;
      // Update summary
    const summaryAmount = document.getElementById('summaryAmount');
    const summaryDailyReturn = document.getElementById('summaryDailyReturn');
    const summaryTotalReturn = document.getElementById('summaryTotalReturn');
    
    if (summaryAmount) summaryAmount.textContent = `${amount.toFixed(2)} USDT`;
    if (summaryDailyReturn) summaryDailyReturn.textContent = `${dailyReturn.toFixed(2)} USDT`;
    if (summaryTotalReturn) summaryTotalReturn.textContent = `${totalReturn.toFixed(2)} USDT`;
    
    const investmentSummary = document.getElementById('investmentSummary');
    const investBtn = document.getElementById('investBtn');
    if (investmentSummary) investmentSummary.style.display = 'block';
    if (investBtn) investBtn.disabled = false;
}

async function handleInvestment(e) {
    e.preventDefault();
    
    const investAmountInput = document.getElementById('investAmount');
    if (!investAmountInput) return;
    
    const amount = parseFloat(investAmountInput.value);
    
    if (!amount || amount < investmentPlans[selectedPlan].minAmount) {
        showMessage(`Minimum investment for ${investmentPlans[selectedPlan].name} is ${investmentPlans[selectedPlan].minAmount} USDT`, 'error');
        return;
    }
    
    if (amount > userBalance) {
        showMessage('Insufficient balance for this investment amount', 'error');
        return;
    }
    
    // Disable button to prevent double submission
    const investBtn = document.getElementById('investBtn');
    if (!investBtn) return;
    
    const originalText = investBtn.textContent;
    investBtn.disabled = true;
    investBtn.textContent = 'Processing...';
    
    try {
        const response = await apiCall('create_investment', {
            amount: amount,
            plan: selectedPlan
        });
          if (response.success) {
            showMessage('Investment created successfully!', 'success');
            
            // Reset form
            const investmentForm = document.getElementById('investmentForm');
            const investmentSummary = document.getElementById('investmentSummary');
            const basicPlanCard = document.querySelector('[data-plan="basic"]');
            const otherPlanCards = document.querySelectorAll('.plan-card:not([data-plan="basic"])');
            
            if (investmentForm) investmentForm.reset();
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (basicPlanCard) basicPlanCard.classList.add('active');
            if (otherPlanCards.length > 0) {
                otherPlanCards.forEach(card => {
                    card.classList.remove('active');
                });
            }
            
            // Reload data
            loadUserBalance();
            loadActiveInvestments();
            loadInvestmentHistory();
        } else {
            showMessage(response.message || 'Failed to create investment', 'error');
        }
    } catch (error) {
        console.error('Error creating investment:', error);
        showMessage('Network error. Please try again.', 'error');
    } finally {
        investBtn.disabled = false;
        investBtn.textContent = originalText;
    }
}

async function loadActiveInvestments() {
    try {
        const response = await apiCall('get_active_investments');
        const container = document.getElementById('activeInvestmentsList');
        
        if (!container) return;
          if (response.success && response.investments && response.investments.length > 0) {
            container.innerHTML = response.investments.map(investment => `
                <div class="investment-item" data-investment-id="${investment.id}" style="cursor: pointer;">
                    <div class="investment-info">
                        <div class="investment-plan">${investment.plan_name}</div>
                        <div class="investment-amount">${parseFloat(investment.amount).toFixed(2)} USDT</div>
                        <div class="investment-progress">
                            <div class="progress-bar">                                <div class="progress-fill" style="width: ${Math.round((investment.days_elapsed / 30) * 100)}%"></div>
                            </div>
                            <span class="progress-text">${investment.days_elapsed}/30 days</span>
                        </div>
                    </div>
                    <div class="investment-earnings">
                        <div class="daily-return">+${parseFloat(investment.daily_return).toFixed(2)} USDT/day</div>
                        <div class="total-earned">${parseFloat(investment.total_earned).toFixed(2)} USDT earned</div>
                    </div>
                </div>
            `).join('');
            
            // Add click event listeners to investment items
            container.addEventListener('click', (e) => {
                const investmentItem = e.target.closest('.investment-item');
                if (investmentItem) {
                    const investmentId = investmentItem.dataset.investmentId;
                    if (investmentId) {
                        // Add click animation
                        investmentItem.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            investmentItem.style.transform = '';
                            // Navigate to active plan page
                            window.location.href = `active_plan.php?id=${investmentId}`;
                        }, 150);
                    }
                }
            });
        } else {
            container.innerHTML = '<div class="no-data">No active investments found</div>';        }
    } catch (error) {
        console.error('Error loading active investments:', error);
        if (container) {
            container.innerHTML = '<div class="error">Error loading investments</div>';
        }
    }
}

async function loadInvestmentHistory() {
    try {
        const response = await apiCall('get_investment_history');
        const container = document.getElementById('investmentHistoryList');
        
        if (!container) return;
        
        if (response.success && response.history && response.history.length > 0) {
            container.innerHTML = response.history.map(investment => `
                <div class="history-item">
                    <div class="history-info">
                        <div class="history-plan">${investment.plan_name}</div>
                        <div class="history-amount">${parseFloat(investment.amount).toFixed(2)} USDT</div>
                        <div class="history-date">${new Date(investment.created_at).toLocaleDateString()}</div>
                    </div>
                    <div class="history-status">
                        <span class="status-badge ${investment.status}">${investment.status}</span>
                        <div class="history-return">${parseFloat(investment.total_return).toFixed(2)} USDT</div>
                    </div>
                </div>
            `).join('');
        } else {
            container.innerHTML = '<div class="no-data">No investment history found</div>';
        }
    } catch (error) {
        console.error('Error loading investment history:', error);
        const container = document.getElementById('investmentHistoryList');
        if (container) {
            container.innerHTML = '<div class="error-message">Error loading investment history</div>';
        }
    }
}

async function loadSystemConfiguration() {
    try {
        const response = await apiCall('get_system_configuration');
        if (response.success) {
            systemConfig = response.data;
            console.log('System configuration loaded:', systemConfig);
        } else {
            console.error('Failed to load system configuration:', response.message);
        }
    } catch (error) {
        console.error('Error loading system configuration:', error);
    }
}

async function loadInvestmentPlans() {
    try {
        const response = await apiCall('get_investment_plans');
        
        if (response.success) {
            const plans = response.plans;
            
            // Convert plans array to object for easy access
            investmentPlans = {};
            plans.forEach(plan => {
                investmentPlans[plan.plan_code] = {
                    id: plan.id,
                    name: plan.plan_name,
                    dailyRate: plan.daily_rate,
                    duration: plan.duration,
                    minAmount: plan.min_amount,
                    maxAmount: plan.max_amount,
                    description: plan.description,
                    features: plan.features || [],
                    isActive: plan.is_active,
                    isFeatured: plan.is_featured
                };
            });
            
            console.log('Investment plans loaded:', investmentPlans);
            setupInvestmentForm();
        } else {
            console.error('Failed to load investment plans:', response.message);
            // Fallback to basic plan only
            useDefaultPlans();
        }
    } catch (error) {
        console.error('Error loading investment plans:', error);
        useDefaultPlans();
    }
}

function useDefaultPlans() {
    investmentPlans = {
        basic: {
            name: 'Basic Plan',
            dailyRate: 0.0167,
            duration: 30,
            minAmount: 600
        }
    };
    setupInvestmentForm();
}

// API call function
async function apiCall(endpoint, data = {}) {
    try {
        const response = await fetch('../ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: endpoint,
                ...data
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Message display function
function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    if (messageDiv) {
        messageDiv.textContent = message;
        messageDiv.className = `message ${type}`;
        messageDiv.style.display = 'block';
        
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
}

// Footer menu initialization
function initFooterMenu() {
    // Footer menu is already initialized by the main layout
}

function showBalanceError() {
    const balanceBasedContainer = document.getElementById('balanceBasedContainer');
    
    if (!balanceBasedContainer) return;
    
    balanceBasedContainer.innerHTML = `
        <div class="error-container">
            <div class="error-card">
                <div class="error-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#dc3545" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                </div>
                <h4>Unable to Load Balance</h4>
                <p>We couldn't load your account balance. Please try refreshing the page.</p>
                <div class="error-actions">
                    <button onclick="window.location.reload()" class="btn btn-primary">Refresh Page</button>
                    <a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    `;
}
