/* Dashboard page styles */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.app-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.header-actions .btn {
    padding: 8px 16px;
    font-size: 14px;
    min-height: 36px;
}

/* Navigation */
.app-nav {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    position: sticky;
    top: 64px;
    z-index: 99;
}

.app-nav {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.app-nav::-webkit-scrollbar {
    display: none;
}

.nav-btn {
    background: none;
    border: none;
    padding: 16px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
    font-size: 14px;
}

.nav-btn:hover {
    color: #495057;
    background-color: #f8f9fa;
}

.nav-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

/* Main content */
.app-main {
    flex: 1;
    padding: 24px 0;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
}

.tab-content {
    display: none;
}

.tab-content {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
    opacity: 1;
    transform: translateY(0);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

/* Balance card */
.balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
}

.balance-card h3 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
    font-size: 1.1rem;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.balance-currency {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Stats card */
.stats-card {
    background: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #6c757d;
    font-size: 14px;
}

.stat-value {
    font-weight: 600;
    color: #495057;
    font-size: 16px;
}

/* Recent transactions */
.recent-transactions {
    background: white;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f1f3f4;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    flex: 1;
}

.transaction-type {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.transaction-date {
    color: #6c757d;
    font-size: 13px;
    margin-top: 4px;
}

.transaction-amount {
    font-weight: 600;
    font-size: 16px;
}

.transaction-amount.positive {
    color: #28a745;
}

.transaction-amount.negative {
    color: #dc3545;
}

/* Wallet section */
.wallet-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

.wallet-address,
.wallet-balance {
    margin-bottom: 16px;
}

.wallet-address label,
.wallet-balance label {
    display: block;
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
}

.address-display,
.balance-display {
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    word-break: break-all;
    border: 1px solid #e9ecef;
}

.wallet-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Transactions section */
.transactions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.transaction-filters {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.transaction-filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.transaction-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.transaction-row {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
}

.transaction-row:last-child {
    border-bottom: none;
}

.transaction-details {
    min-width: 0;
}

.transaction-hash {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #6c757d;
    word-break: break-all;
}

.transaction-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.transaction-status.confirmed {
    background-color: #d4edda;
    color: #155724;
}

.transaction-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.transaction-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* Deposit section */
.deposit-info {
    text-align: center;
    margin-bottom: 32px;
}

.deposit-address {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border: 2px dashed #dee2e6;
}

.deposit-address div {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    word-break: break-all;
    margin-bottom: 12px;
}

.deposit-qr {
    margin-top: 20px;
    text-align: center;
}

.manual-deposit {
    border-top: 1px solid #e9ecef;
    padding-top: 24px;
    margin-top: 24px;
}

.manual-deposit h4 {
    margin-bottom: 16px;
    color: #495057;
}

/* Profile modal */
.profile-info {
    margin-bottom: 24px;
}

.profile-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.profile-field:last-child {
    border-bottom: none;
}

.profile-field label {
    font-weight: 500;
    color: #6c757d;
}

.profile-actions {
    text-align: center;
}

/* QR Code styles */
.qr-code-container {
    text-align: center;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin: 1rem 0;
}

.qr-code-image {
    max-width: 200px;
    height: auto;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qr-code-address {
    font-family: monospace;
    font-size: 0.875rem;
    color: var(--text-muted);
    word-break: break-all;
    margin: 0.5rem 0;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.copy-success-message {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

#depositQR {
    margin-top: 1rem;
}

/* Sticky Footer Menu */
.footer-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e9ecef;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: block !important; /* Always visible on all screen sizes */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform: translateY(0) !important; /* Always shown */
    transition: transform 0.3s ease;
}

.footer-menu.show {
    transform: translateY(0);
}

/* Ensure all pages have bottom padding for the sticky footer */
body {
    padding-bottom: 80px;
}

.app-container {
    padding-bottom: 80px;
}

.footer-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 12px 8px 8px 8px;
    max-width: 100%;
    background: rgba(255, 255, 255, 0.95);
}

.footer-btn {
    background: none;
    border: none;
    padding: 8px 4px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
    border-radius: 8px;
    position: relative;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.footer-btn:hover,
.footer-btn:focus {
    color: #495057;
    background-color: rgba(102, 126, 234, 0.1);
    outline: none;
}

.footer-btn:active {
    transform: scale(0.95);
}

.footer-btn.active {
    color: #667eea;
}

.footer-btn.active::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background: #667eea;
    border-radius: 2px;
}

.footer-icon {
    width: 22px;
    height: 22px;
    transition: all 0.3s ease;
}

.footer-btn.active .footer-icon {
    transform: scale(1.1);
}

.footer-btn span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    margin-top: 2px;
}

/* Add bottom padding to main content when footer is visible */
.app-container.with-footer {
    padding-bottom: 70px;
}

/* Safe area insets for modern devices (iPhone X, etc.) */
@supports (padding: max(0px)) {
    .footer-menu {
        padding-bottom: max(8px, env(safe-area-inset-bottom));
    }
    
    .app-container {
        padding-bottom: calc(70px + max(0px, env(safe-area-inset-bottom)));
    }
}

/* Dark mode support for footer */
@media (prefers-color-scheme: dark) {
    .footer-menu {
        background: #1a1a1a;
        border-top-color: #333;
    }
    
    .footer-nav {
        background: rgba(26, 26, 26, 0.95);
    }
    
    .footer-btn {
        color: #a0a0a0;
    }
    
    .footer-btn:hover {
        color: #ffffff;
        background-color: rgba(102, 126, 234, 0.2);
    }
    
    .footer-btn.active {
        color: #667eea;
    }
}

/* Desktop and all screen sizes - always show footer menu */
@media (min-width: 769px) {
    .footer-menu {
        display: block !important;
        transform: translateY(0) !important;
    }
    
    /* Ensure navigation is visible on desktop */
    .app-nav {
        display: flex;
    }
    
    /* Maintain bottom padding for footer on desktop */
    .app-container {
        padding-bottom: 80px;
    }
    
    .app-main {
        padding-bottom: 80px;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    /* Hide regular navigation on mobile */
    .app-nav {
        display: none;
    }
    
    /* Show footer menu on mobile */
    .footer-menu {
        display: block !important;
        transform: translateY(0);
    }
    
    /* Add padding to prevent content from being hidden behind footer */
    .app-container {
        padding-bottom: 70px;
    }
    
    .header-content {
        padding: 0 16px;
    }
    
    .app-main {
        padding: 16px;
        padding-bottom: 80px; /* Extra padding for footer */
    }
    
    .balance-amount {
        font-size: 2rem;
    }
    
    .wallet-actions {
        flex-direction: column;
    }
    
    .wallet-actions .btn {
        width: 100%;
    }
    
    .transactions-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .transaction-filters {
        width: 100%;
    }
    
    .transaction-filters select {
        flex: 1;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 12px;
    }
    
    .app-main {
        padding: 12px;
    }
    
    .header-actions {
        gap: 6px;
    }
    
    .header-actions .btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .footer-btn {
        padding: 6px 2px;
        font-size: 9px;
        min-width: 50px;
    }
    
    .footer-icon {
        width: 18px;
        height: 18px;
    }
}

@media (min-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .balance-card {
        grid-column: 1 / -1;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .wallet-section {
        grid-template-columns: 1fr 1fr;
    }
    
    .manual-deposit {
        grid-column: 1 / -1;
    }
    
    .transaction-row {
        grid-template-columns: auto 1fr auto auto;
    }
}

@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .balance-card {
        grid-column: 1 / -1;
    }
    
    .recent-transactions {
        grid-column: 1;
        grid-row: 2;
    }
    
    .stats-card {
        grid-column: 2;
        grid-row: 2;
    }
}

/* Profile Page Styles */
.profile-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.profile-card {
    margin-bottom: 24px;
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.profile-header h2 {
    margin: 0 0 4px 0;
    color: #212529;
    font-size: 1.75rem;
    font-weight: 600;
}

.profile-header p {
    margin: 0;
    color: #6c757d;
    font-size: 0.95rem;
}

.profile-section {
    margin-bottom: 32px;
}

.profile-section h3 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.profile-info {
    display: grid;
    gap: 16px;
}

.profile-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.profile-field:last-child {
    border-bottom: none;
}

.profile-field label {
    font-weight: 500;
    color: #495057;
    margin: 0;
}

.profile-field span {
    color: #212529;
    font-weight: 400;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.security-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.security-actions .btn {
    display: flex;
    align-items: center;
    gap: 8px;
}

.wallet-summary {
    display: grid;
    gap: 16px;
    margin-bottom: 20px;
}

.wallet-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.wallet-stat:last-child {
    border-bottom: none;
}

.wallet-stat label {
    font-weight: 500;
    color: #495057;
    margin: 0;
}

.wallet-stat span {
    color: #212529;
    font-weight: 400;
}

.wallet-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.change-password-card {
    border-left: 4px solid #007bff;
}

.change-password-card .profile-header {
    margin-bottom: 20px;
}

.change-password-card .profile-header h3 {
    margin: 0;
    color: #007bff;
}

.password-requirements {
    margin-top: 4px;
}

.password-requirements small {
    color: #6c757d;
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

/* ========================================
   MAKE INVESTMENT PAGE STYLES
   ======================================== */

/* Make Investment Page Layout */
.make-investment-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.make-investment-page .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    text-align: center;
}

.make-investment-page .page-header h2 {
    margin: 0 0 8px 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.make-investment-page .page-header p {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Balance Overview */
.balance-overview {
    margin-bottom: 32px;
}

.balance-card {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(40, 167, 69, 0.2);
}

.balance-card h3 {
    margin: 0 0 16px 0;
    font-size: 1.2rem;
    font-weight: 600;
    opacity: 0.9;
}

.balance-amount {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.balance-currency {
    font-size: 1.1rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Investment Plans */
.investment-plans {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.plan-card {
    background: white;
    border-radius: 16px;
    padding: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.plan-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.plan-card.active {
    border-color: #667eea;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.plan-card.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    border-color: #dee2e6;
}

.plan-card.disabled .plan-header {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.plan-card.disabled .plan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(248, 249, 250, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #6c757d;
    z-index: 2;
    font-size: 1.1rem;
}

.plan-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px;
    position: relative;
}

.plan-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.plan-badge.coming-soon {
    background: rgba(255, 193, 7, 0.9);
    color: #212529;
}

.plan-name {
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0 0 8px 0;
}

.plan-rate {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 8px 0 4px 0;
    line-height: 1;
}

.plan-period {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0 0 8px 0;
}

.plan-duration {
    font-size: 1rem;
    opacity: 0.9;
    margin: 4px 0;
}

.plan-minimum-header {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 8px;
}

.plan-details {
    padding: 24px;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    padding: 8px 0;
    color: #495057;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    line-height: 1.4;
}

.plan-features li:before {
    content: "✅";
    font-size: 14px;
    flex-shrink: 0;
}

.plan-minimum {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    margin-top: 16px;
    text-align: center;
}

.plan-minimum strong {
    color: #667eea;
    font-size: 1.1rem;
}

/* Investment Form */
.investment-form-container {
    background: white;
    border-radius: 16px;
    padding: 24px;
    margin-top: 24px;
}

.selected-plan-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
}

.plan-summary-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.plan-summary-header h4 {
    margin: 0 0 4px 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.selected-plan-rate {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.plan-summary-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.detail-value {
    font-weight: 600;
}

/* Balance Information */
.balance-info {
    margin-bottom: 24px;
}

.balance-info-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.balance-info-header h4 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
}

.balance-info-amount {
    font-size: 2rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 8px;
}

.balance-status {
    font-size: 0.9rem;
    font-weight: 500;
}

.balance-status.sufficient {
    color: #28a745;
}

.balance-status.insufficient {
    color: #dc3545;
}

/* Form Groups */
.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.amount-validation {
    margin-top: 8px;
    font-size: 0.9rem;
}

.amount-validation.valid {
    color: #28a745;
}

.amount-validation.invalid {
    color: #dc3545;
}

/* Investment Summary */
.investment-summary {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
}

.investment-summary h4 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
}

.summary-details {
    display: grid;
    gap: 12px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.summary-row:last-child {
    border-bottom: none;
    font-weight: 600;
    color: #667eea;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Insufficient Balance Warning */
.insufficient-balance {
    background: white;
    border-radius: 16px;
    padding: 24px;
    text-align: center;
}

.warning-card {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 12px;
    padding: 24px;
    color: #856404;
}

.warning-card h4 {
    margin: 0 0 12px 0;
    color: #856404;
    font-size: 1.2rem;
    font-weight: 600;
}

.warning-card p {
    margin: 0 0 20px 0;
    line-height: 1.5;
}

.warning-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

/* ========================================
   MODAL SYSTEM STYLES
   ======================================== */

/* Base Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 24px 24px 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 0 24px 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Success Modal */
.success-modal .modal-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 16px 16px 0 0;
    padding: 24px;
}

.success-header h3 {
    color: white;
}

.success-icon {
    text-align: center;
    margin-bottom: 20px;
}

.success-message {
    text-align: center;
}

.success-message p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    color: #495057;
}

.investment-details {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}

/* Low Funds Modal */
.low-funds-content {
    text-align: center;
}

.low-funds-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #ffc107;
}

.balance-comparison {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.comparison-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.comparison-row:last-child {
    border-bottom: none;
    font-weight: 600;
}

.comparison-row.insufficient {
    color: #dc3545;
}

/* Loading Spinner */
.loading-spinner {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

/* Mobile First - Small Screens (up to 480px) */
@media (max-width: 480px) {
    .dashboard-content {
        padding: 12px 12px 80px 12px;
    }
    
    .page-header h2 {
        font-size: 1.3rem;
    }
    
    .page-header p {
        font-size: 0.9rem;
    }
    
    /* Stats Grid - Mobile Stack */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .stat-card {
        padding: 12px;
        gap: 10px;
    }
    
    .stat-icon {
        width: 36px;
        height: 36px;
    }
    
    .stat-icon svg {
        width: 18px;
        height: 18px;
    }
    
    .stat-info h3 {
        font-size: 1.2rem;
    }
    
    .stat-info p {
        font-size: 0.75rem;
    }
    
    /* Cards - Ultra Compact */
    .card {
        padding: 14px;
        margin-bottom: 14px;
        border-radius: 10px;
    }
    
    .card h3 {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }
    
    /* Investment Summary - Mobile Optimized */
    .investment-summary-card {
        padding: 10px;
        margin-bottom: 10px;
    }
    
    .investment-summary-card h4 {
        font-size: 0.95rem !important;
        margin-bottom: 8px !important;
    }
    
    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }
    
    .summary-stat {
        padding: 6px 4px;
    }
    
    .summary-stat-value {
        font-size: 0.8rem;
    }
    
    .summary-stat-label {
        font-size: 0.6rem;
    }
    
    /* Investment Items - Mobile Compact */
    .investment-item {
        padding: 10px;
        border-radius: 8px;
    }
    
    .investment-info {
        padding-right: 55px;
    }
    
    .investment-plan {
        font-size: 0.85rem;
    }
    
    .investment-amount {
        font-size: 0.8rem;
    }
    
    /* Fix potential overflow issues on small screens */
    .investment-item,
    .transaction-item {
        overflow: hidden;
        word-wrap: break-word;
    }
    
    /* Ensure text doesn't overflow in amount displays */
    .transaction-amount,
    .investment-amount,
    .summary-stat-value {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* Tablet - Medium Screens (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .dashboard-content {
        padding: 16px 16px 80px 16px;
    }
    
    /* Stats Grid - 2 Column Layout */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 14px;
    }
    
    .stat-card {
        padding: 16px;
    }
    
    /* Investment Grid - Single Column */
    .investment-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    /* Summary Stats - 4 Column Layout */
    .summary-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
    }
    
    /* Card Footer - Horizontal Layout */
    .card-footer {
        flex-direction: row;
        justify-content: center;
    }
    
    .card-footer .btn {
        flex: 1;
        max-width: 160px;
    }
}

/* Desktop - Large Screens (769px and up) */
@media (min-width: 769px) {
    .dashboard-content {
        padding: 24px 20px 80px 20px;
    }
    
    .page-header h2 {
        font-size: 1.8rem;
    }
    
    .page-header p {
        font-size: 1rem;
    }
    
    /* Stats Grid - 4 Column Layout */
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin-bottom: 24px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .stat-icon {
        width: 48px;
        height: 48px;
    }
    
    .stat-icon svg {
        width: 24px;
        height: 24px;
    }
    
    .stat-info h3 {
        font-size: 1.6rem;
    }
    
    .stat-info p {
        font-size: 0.85rem;
    }
    
    /* Cards - Full Desktop Layout */
    .card {
        padding: 24px;
        margin-bottom: 24px;
    }
    
    .card h3 {
        font-size: 1.3rem;
        margin-bottom: 20px;
    }
    
    /* Investment Grid - Responsive Columns */
    .investment-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 16px;
    }
    
    /* Summary Stats - Desktop Layout */
    .summary-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
    }
    
    .summary-stat {
        padding: 10px;
    }
    
    .summary-stat-value {
        font-size: 1rem;
    }
    
    .summary-stat-label {
        font-size: 0.75rem;
    }
    
    /* Investment Items - Desktop Spacing */
    .investment-item {
        padding: 16px;
    }
    
    .investment-info {
        padding-right: 70px;
    }
    
    .investment-plan {
        font-size: 1rem;
    }
    
    .investment-amount {
        font-size: 0.95rem;
    }
    
    .performance-indicator {
        font-size: 0.8rem;
    }
    
    .investment-earnings {
        font-size: 0.85rem;
    }
    
    /* Transaction Items - Desktop Layout */
    .transaction-item {
        padding: 14px 0;
    }
    
    .transaction-type {
        font-size: 0.95rem;
    }
    
    .transaction-date {
        font-size: 0.8rem;
    }
    
    .transaction-amount {
        font-size: 0.95rem;
    }
    
    /* Card Footer - Desktop Layout */
    .card-footer {
        padding-top: 20px;
        margin-top: 20px;
    }
    
    .card-footer .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Extra Large Screens (1200px and up) */
@media (min-width: 1200px) {
    .dashboard-content {
        padding: 32px 24px 80px 24px;
    }
    
    /* Investment Grid - Multi Column for Large Screens */
    .investment-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 20px;
    }
    
    /* Enhanced hover effects for desktop */
    .investment-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }
}

/* ========================================
   ENHANCED ANIMATIONS & MICRO-INTERACTIONS
   ======================================== */

/* Smooth entrance animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
    }
    50% {
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
    }
}

/* Apply entrance animations */
.card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card {
    animation: slideInRight 0.5s ease-out;
    animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.investment-item.pulse {
    animation: pulseGlow 2s infinite;
}

.investment-item.new-investment {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading state improvements */
.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* Enhanced hover effects with better performance */
.investment-item {
    will-change: transform, box-shadow;
}

.stat-card {
    will-change: transform, box-shadow;
}

.transaction-item {
    will-change: background-color, padding;
}

/* Focus states for accessibility */
.investment-item:focus,
.card:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Better visual feedback for loading states */
.investment-summary-card.loading {
    background: linear-gradient(90deg, #f8f9ff, #e9ecef, #f8f9ff);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Improved mobile touch interactions */
@media (max-width: 768px) {
    .investment-item:active,
    .stat-card:active,
    .btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
    
    /* Better touch targets */
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .transaction-item {
        min-height: 48px;
    }
}

/* Dark mode support (future-proofing) */
@media (prefers-color-scheme: dark) {
    .card {
        background: #1a1a1a;
        border-color: #333;
        color: #fff;
    }
    
    .investment-summary-card {
        background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
        border-color: #333;
    }
    
    .summary-stat {
        background: #2a2a2a;
        border-color: #333;
    }
    
    .transaction-item:hover {
        background-color: #2a2a2a;
    }
}

/* Print styles for better printing experience */
@media print {
    .card-footer,
    .btn,
    .footer-menu {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .investment-item,
    .transaction-item {
        break-inside: avoid;
    }
}

/* ========================================
   FINAL MOBILE OPTIMIZATIONS & FIXES
   ======================================== */

/* Ensure proper viewport handling */
.dashboard-content {
    min-height: calc(100vh - 140px);
    box-sizing: border-box;
}

/* Fix potential overflow issues on small screens */
.investment-item,
.transaction-item {
    overflow: hidden;
    word-wrap: break-word;
}

/* Ensure text doesn't overflow in amount displays */
.transaction-amount,
.investment-amount,
.summary-stat-value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Better spacing for very small screens */
@media (max-width: 360px) {
    .dashboard-content {
        padding: 8px 8px 80px 8px;
    }
    
    .card {
        padding: 12px;
        margin-bottom: 12px;
    }
    
    .card h3 {
        font-size: 1rem;
        margin-bottom: 10px;
    }
    
    .stats-grid {
        gap: 8px;
    }
    
    .stat-card {
        padding: 10px;
        gap: 8px;
    }
    
    .stat-icon {
        width: 32px;
        height: 32px;
    }
    
    .stat-icon svg {
        width: 16px;
        height: 16px;
    }
    
    .stat-info h3 {
        font-size: 1rem;
    }
    
    .stat-info p {
        font-size: 0.7rem;
    }
    
    .investment-summary-card {
        padding: 8px;
    }
    
    .summary-stats {
        gap: 4px;
    }
    
    .summary-stat {
        padding: 4px 2px;
    }
    
    .summary-stat-value {
        font-size: 0.75rem;
    }
    
    .summary-stat-label {
        font-size: 0.55rem;
    }
    
    .investment-item {
        padding: 8px;
    }
    
    .investment-info {
        padding-right: 45px;
    }
    
    .investment-plan {
        font-size: 0.8rem;
    }
    
    .investment-amount {
        font-size: 0.75rem;
    }
}

/* Improved contrast for better accessibility */
.investment-status.active {
    background: rgba(40, 167, 69, 0.2);
    color: #1e5b2a;
    border: 1px solid rgba(40, 167, 69, 0.4);
}

.investment-status.completed {
    background: rgba(102, 126, 234, 0.2);
    color: #4c5db7;
    border: 1px solid rgba(102, 126, 234, 0.4);
}

/* Better visual hierarchy */
.card h3::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
}

/* Smooth scroll behavior for better UX */
html {
    scroll-behavior: smooth;
}

/* Better focus management for keyboard navigation */
.investment-item[tabindex]:focus,
.transaction-item[tabindex]:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
    z-index: 1;
    position: relative;
}

/* Loading state for transaction items */
.transaction-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

.transaction-item.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 1.5s infinite;
}

/* Ensure proper stacking context */
.investment-item {
    position: relative;
    z-index: 1;
}

.investment-item:hover {
    z-index: 2;
}

/* Better visual feedback for empty states */
.no-data {
    border: 2px dashed #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.no-data::before {
    content: '📭';
    font-size: 2rem;
    display: block;
    margin-bottom: 8px;
}

/* Optimize for high-DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .progress-bar {
        height: 6px;
    }
    
    .investment-item::before {
        height: 4px;
    }
    
    .card h3::before {
        width: 5px;
        height: 22px;
    }
}

/* Final responsive cleanup */
@media (orientation: landscape) and (max-height: 500px) {
    .dashboard-content {
        padding-top: 8px;
        padding-bottom: 60px;
    }
    
    .page-header {
        margin-bottom: 12px;
    }
    
    .page-header h2 {
        font-size: 1.2rem;
        margin-bottom: 4px;
    }
    
    .page-header p {
        font-size: 0.8rem;
        margin-bottom: 8px;
    }
    
    .card {
        margin-bottom: 12px;
    }
}
