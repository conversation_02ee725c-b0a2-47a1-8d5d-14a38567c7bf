<?php
/**
 * Test Frontend Authentication Flow
 * Tests the actual frontend → ajax.php → backend API authentication chain
 */

echo "=== FRONTEND AUTHENTICATION FLOW TEST ===\n\n";

// Test 1: Verify frontend session authentication via ajax.php
echo "1. Testing Frontend Session Authentication via ajax.php...\n";

// Start session and simulate logged in user
session_start();
$_SESSION['user_id'] = 2; // Test user ID
$_SESSION['email'] = '<EMAIL>';
$_SESSION['token'] = 'test_token_123'; // Add session token

echo "Session started with user_id: " . $_SESSION['user_id'] . "\n";
echo "Session email: " . $_SESSION['email'] . "\n";
echo "Session token: " . $_SESSION['token'] . "\n\n";

// Test 2: Simulate investment creation through ajax.php
echo "2. Testing Investment Creation through ajax.php...\n";

$investmentData = [
    'plan' => 'basic',
    'amount' => 100
];

// Simulate POST request to ajax.php as frontend would do
$postData = json_encode([
    'action' => 'create_investment',
    'amount' => 100,
    'plan' => 'basic'
]);

// Test the ajax.php endpoint
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/frontend/ajax.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Cookie: PHPSESSID=' . session_id() // Pass session cookie
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: $httpCode\n";
echo "Response: $response\n\n";

// Test 3: Check if the response indicates successful authentication
if ($response) {
    $decoded = json_decode($response, true);
    if ($decoded) {
        echo "3. Response Analysis:\n";
        echo "Success: " . ($decoded['success'] ?? 'not set') . "\n";
        echo "Message: " . ($decoded['message'] ?? 'not set') . "\n";
        
        if (isset($decoded['error'])) {
            echo "Error: " . $decoded['error'] . "\n";
        }
        
        if (isset($decoded['data'])) {
            echo "Data: " . json_encode($decoded['data']) . "\n";
        }
    } else {
        echo "3. Failed to decode JSON response\n";
        echo "Raw response: $response\n";
    }
} else {
    echo "3. No response received\n";
}

echo "\n=== TEST COMPLETE ===\n";

// Test 4: Direct session verification
echo "\n4. Session Verification:\n";
echo "Current session ID: " . session_id() . "\n";
echo "Session data: " . json_encode($_SESSION) . "\n";

// Test 5: Verify session is accessible to ajax.php
echo "\n5. Testing Session Accessibility to ajax.php...\n";

// Make a simple request to check if session is readable
$testData = json_encode(['action' => 'get_profile']);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/frontend/ajax.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Cookie: PHPSESSID=' . session_id()
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$authCheckResponse = curl_exec($ch);
$authCheckHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Auth Check HTTP Status: $authCheckHttpCode\n";
echo "Auth Check Response: $authCheckResponse\n";

?>
