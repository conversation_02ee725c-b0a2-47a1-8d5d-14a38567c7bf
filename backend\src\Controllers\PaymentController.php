<?php

namespace Simbi\Tls\Controllers;

use Simbi\Tls\Services\TronGridService;
use Simbi\Tls\Services\TransactionService;
use Simbi\Tls\Services\WalletService;
use Simbi\Tls\Repositories\WalletRepository;
use Exception;

class PaymentController
{
    private TronGridService $tronGridService;
    private TransactionService $transactionService;
    private WalletService $walletService;
    private WalletRepository $walletRepository;

    public function __construct(
        TronGridService $tronGridService,
        TransactionService $transactionService,
        WalletService $walletService,
        WalletRepository $walletRepository
    ) {
        $this->tronGridService = $tronGridService;
        $this->transactionService = $transactionService;
        $this->walletService = $walletService;
        $this->walletRepository = $walletRepository;
    }

    /**
     * Confirm payment from TronGrid by wallet ID and amount
     */
    public function confirmPayment(array $data, array $user): array
    {
        try {
            // Validate input data
            $walletId = $data['wallet_id'] ?? null;
            $amount = $data['amount'] ?? null;
            $transactionHash = $data['transaction_hash'] ?? null;

            if (!$walletId || !$amount) {
                return [
                    'success' => false,
                    'message' => 'Wallet ID and amount are required',
                    'code' => 400
                ];
            }

            if (!is_numeric($amount) || (float)$amount <= 0) {
                return [
                    'success' => false,
                    'message' => 'Amount must be a positive number',
                    'code' => 400
                ];
            }

            $amount = (float)$amount;
            $userId = $user['id'];

            // Find wallet and verify ownership
            $wallet = $this->walletRepository->findById($walletId);
            if (!$wallet) {
                return [
                    'success' => false,
                    'message' => 'Wallet not found',
                    'code' => 404
                ];
            }

            if ($wallet['user_id'] != $userId) {
                return [
                    'success' => false,
                    'message' => 'Access denied: Wallet does not belong to user',
                    'code' => 403
                ];
            }

            // Verify transaction with TronGrid
            $verification = $this->tronGridService->verifyTransaction(
                $wallet['address'],
                $amount,
                $transactionHash
            );

            if (!$verification['verified']) {
                return [
                    'success' => false,
                    'message' => $verification['message'] ?? 'Transaction could not be verified',
                    'verification_details' => $verification,
                    'code' => 404
                ];
            }

            $verifiedTx = $verification['transaction'];

            // Check if transaction already exists in our database
            $existingTx = $this->transactionService->findByHash($verifiedTx['transaction_id']);
            if ($existingTx) {
                return [
                    'success' => false,
                    'message' => 'Transaction already recorded in system',
                    'existing_transaction' => $existingTx,
                    'code' => 409
                ];
            }

            // Create transaction record
            $transactionData = [
                'user_id' => $userId,
                'wallet_id' => $walletId,
                'transaction_hash' => $verifiedTx['transaction_id'],
                'type' => 'deposit',
                'amount' => $verifiedTx['amount'],
                'from_address' => $verifiedTx['from_address'],
                'to_address' => $verifiedTx['to_address'],
                'status' => 'confirmed',
                'confirmations' => $verifiedTx['confirmations'],
                'block_number' => $verifiedTx['block_number'],
                'block_timestamp' => date('Y-m-d H:i:s', $verifiedTx['block_timestamp'] / 1000)
            ];

            $transactionId = $this->transactionService->createTransaction($transactionData);

            if (!$transactionId) {
                return [
                    'success' => false,
                    'message' => 'Failed to record transaction',
                    'code' => 500
                ];
            }

            // Update wallet balance
            $success = $this->walletService->addBalance($userId, $verifiedTx['amount'], 'deposit');
            if (!$success) {
                // Log error but don't fail the request as transaction is already recorded
                error_log("Failed to update balance for user {$userId}, transaction {$transactionId}");
            }

            return [
                'success' => true,
                'message' => 'Payment confirmed and credited successfully',
                'transaction_id' => $transactionId,
                'verified_amount' => $verifiedTx['amount'],
                'transaction_hash' => $verifiedTx['transaction_id'],
                'confirmations' => $verifiedTx['confirmations'],
                'balance_updated' => $success,
                'deposit_details' => [
                    'from_address' => $verifiedTx['from_address'],
                    'to_address' => $verifiedTx['to_address'],
                    'block_number' => $verifiedTx['block_number'],
                    'block_timestamp' => $transactionData['block_timestamp']
                ]
            ];

        } catch (Exception $e) {
            error_log("Payment confirmation error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error confirming payment: ' . $e->getMessage(),
                'code' => 500
            ];
        }
    }

    /**
     * Get payment status by transaction hash
     */
    public function getPaymentStatus(array $data, array $user): array
    {
        try {
            $transactionHash = $data['transaction_hash'] ?? null;

            if (!$transactionHash) {
                return [
                    'success' => false,
                    'message' => 'Transaction hash is required',
                    'code' => 400
                ];
            }

            $userId = $user['id'];

            // Check if transaction exists in our database
            $transaction = $this->transactionService->findByHash($transactionHash);
            
            if ($transaction) {
                // Verify user owns this transaction
                if ($transaction['user_id'] != $userId) {
                    return [
                        'success' => false,
                        'message' => 'Access denied',
                        'code' => 403
                    ];
                }

                return [
                    'success' => true,
                    'status' => 'recorded',
                    'transaction' => $transaction,
                    'message' => 'Transaction found in system'
                ];
            }

            // Check TronGrid for transaction details
            $tronGridTx = $this->tronGridService->getTransactionByHash($transactionHash);
            
            if (!$tronGridTx) {
                return [
                    'success' => false,
                    'message' => 'Transaction not found on TRON network',
                    'status' => 'not_found',
                    'code' => 404
                ];
            }

            return [
                'success' => true,
                'status' => 'on_network',
                'message' => 'Transaction found on network but not recorded in system',
                'network_transaction' => $tronGridTx,
                'suggestion' => 'Use confirm-payment endpoint to record this transaction'
            ];

        } catch (Exception $e) {
            error_log("Payment status check error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error checking payment status: ' . $e->getMessage(),
                'code' => 500
            ];
        }
    }
}
