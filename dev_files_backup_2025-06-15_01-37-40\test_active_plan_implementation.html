<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Plan Page Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .link-test {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .link-test:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Active Plan Page Implementation Test</h1>
        <p>Test the complete implementation of the Active Plan page with earnings table and timer countdown functionality.</p>

        <!-- Navigation Test -->
        <div class="test-section">
            <h3>🔗 Navigation Test</h3>
            <p>Test navigation to the active plan page with different investment IDs:</p>
            <a href="frontend/user/active_plan.php?id=1" class="link-test" target="_blank">Active Plan - ID 1</a>
            <a href="frontend/user/active_plan.php?id=2" class="link-test" target="_blank">Active Plan - ID 2</a>
            <a href="frontend/user/active_plan.php?id=999" class="link-test" target="_blank">Active Plan - Invalid ID</a>
        </div>

        <!-- API Endpoint Test -->
        <div class="test-section">
            <h3>🔌 API Endpoints Test</h3>
            <p>Test the new API endpoints for investment details and earnings:</p>
            <button class="btn" onclick="testInvestmentDetails()">Test Investment Details API</button>
            <button class="btn" onclick="testInvestmentEarnings()">Test Investment Earnings API</button>
            <button class="btn" onclick="testActiveInvestments()">Test Active Investments API</button>
            <div id="apiResults"></div>
        </div>

        <!-- Backend Routes Test -->
        <div class="test-section">
            <h3>🛤️ Backend Routes Test</h3>
            <p>Test the backend API routes directly:</p>
            <button class="btn" onclick="testBackendRoutes()">Test Backend Routes</button>
            <div id="backendResults"></div>
        </div>

        <!-- Integration Test -->
        <div class="test-section">
            <h3>🔄 Integration Test</h3>
            <p>Test the complete flow from dashboard to active plan page:</p>
            <ol>
                <li>Go to <a href="frontend/user/dashboard.php" target="_blank">Dashboard</a></li>
                <li>Click on any active investment item</li>
                <li>Verify navigation to active_plan.php</li>
                <li>Check earnings table loads</li>
                <li>Verify countdown timer works</li>
            </ol>
        </div>

        <!-- Implementation Summary -->
        <div class="test-section">
            <h3>✅ Implementation Summary</h3>
            <ul>
                <li><strong>Frontend:</strong> 
                    <ul>
                        <li>✅ active_plan.php - Main page with earnings table and timer</li>
                        <li>✅ active_plan.js - JavaScript functionality for data loading and countdown</li>
                        <li>✅ Updated click handlers in dashboard-simple.js, make_investment.js, invest.js</li>
                    </ul>
                </li>
                <li><strong>Backend:</strong>
                    <ul>
                        <li>✅ New API endpoints: get_investment_details, get_investment_earnings</li>
                        <li>✅ Backend routes: /api/investments/{id}, /api/investments/{id}/earnings</li>
                        <li>✅ Controller methods: getInvestmentDetails(), getInvestmentEarnings()</li>
                        <li>✅ Service methods: getInvestmentDetails(), getInvestmentEarnings()</li>
                    </ul>
                </li>
                <li><strong>Features:</strong>
                    <ul>
                        <li>✅ Detailed investment overview with progress tracking</li>
                        <li>✅ Earnings history table showing daily payouts</li>
                        <li>✅ Live countdown timer for next payout</li>
                        <li>✅ Responsive design for mobile and desktop</li>
                        <li>✅ Navigation from investment items on all pages</li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <script>
        async function apiCall(endpoint, data = {}) {
            try {
                const response = await fetch('/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: endpoint,
                        ...data
                    })
                });

                const result = await response.json();
                return result;
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testInvestmentDetails() {
            const container = document.getElementById('apiResults');
            container.innerHTML = '<div class="info">Testing investment details API...</div>';
            
            try {
                const result = await apiCall('get_investment_details', { id: 1 });
                
                container.innerHTML = `
                    <div class="result ${result.success ? 'success' : 'error'}">
                        <h4>Investment Details API Result:</h4>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                container.innerHTML = `
                    <div class="result error">
                        <h4>Investment Details API Error:</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testInvestmentEarnings() {
            const container = document.getElementById('apiResults');
            container.innerHTML = '<div class="info">Testing investment earnings API...</div>';
            
            try {
                const result = await apiCall('get_investment_earnings', { id: 1 });
                
                container.innerHTML = `
                    <div class="result ${result.success ? 'success' : 'error'}">
                        <h4>Investment Earnings API Result:</h4>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                container.innerHTML = `
                    <div class="result error">
                        <h4>Investment Earnings API Error:</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testActiveInvestments() {
            const container = document.getElementById('apiResults');
            container.innerHTML = '<div class="info">Testing active investments API...</div>';
            
            try {
                const result = await apiCall('get_active_investments');
                
                container.innerHTML = `
                    <div class="result ${result.success ? 'success' : 'error'}">
                        <h4>Active Investments API Result:</h4>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                container.innerHTML = `
                    <div class="result error">
                        <h4>Active Investments API Error:</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testBackendRoutes() {
            const container = document.getElementById('backendResults');
            container.innerHTML = '<div class="info">Testing backend routes...</div>';
            
            const tests = [
                { url: '/api/investments/1', name: 'Investment Details' },
                { url: '/api/investments/1/earnings', name: 'Investment Earnings' },
                { url: '/api/investments/active', name: 'Active Investments' }
            ];
            
            let results = '';
            
            for (const test of tests) {
                try {
                    const response = await fetch(`http://localhost:8000${test.url}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const result = await response.json();
                    const status = response.ok ? 'success' : 'error';
                    
                    results += `
                        <div class="result ${status}">
                            <h4>${test.name} (${test.url}):</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } catch (error) {
                    results += `
                        <div class="result error">
                            <h4>${test.name} (${test.url}):</h4>
                            <p>Error: ${error.message}</p>
                        </div>
                    `;
                }
            }
            
            container.innerHTML = results;
        }

        // Auto-run a basic test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Active Plan Page Test loaded successfully');
        });
    </script>
</body>
</html>
