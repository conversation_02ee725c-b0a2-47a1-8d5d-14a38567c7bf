# TLS Wallet - Production Verification Script
# Verifies the codebase is ready for production deployment
# Date: June 15, 2025

Write-Host "🔍 TLS Wallet Production Verification" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Yellow

$verificationPassed = $true
$issues = @()

# Check directory structure
Write-Host "`n📁 Checking directory structure..." -ForegroundColor Cyan

$requiredDirs = @("backend", "frontend", "cronJob")
foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "✅ $dir directory exists" -ForegroundColor Green
    } else {
        Write-Host "❌ $dir directory missing" -ForegroundColor Red
        $issues += "$dir directory missing"
        $verificationPassed = $false
    }
}

# Check for remaining test files
Write-Host "`n🧹 Checking for leftover development files..." -ForegroundColor Cyan

$devPatterns = @("test_*", "debug_*", "*_test.*", "*_debug.*")
$foundDevFiles = @()

foreach ($pattern in $devPatterns) {
    $files = Get-ChildItem -Path . -Name $pattern -Recurse -ErrorAction SilentlyContinue
    if ($files) {
        $foundDevFiles += $files
    }
}

if ($foundDevFiles.Count -eq 0) {
    Write-Host "✅ No development files found" -ForegroundColor Green
} else {
    Write-Host "⚠️  Found $($foundDevFiles.Count) development files:" -ForegroundColor Yellow
    foreach ($file in $foundDevFiles) {
        Write-Host "  - $file" -ForegroundColor Yellow
    }
}

# Check essential files
Write-Host "`n📋 Checking essential files..." -ForegroundColor Cyan

$essentialFiles = @(
    "frontend/index.php",
    "frontend/config.php", 
    "frontend/ajax.php",
    "backend/index.php",
    "backend/composer.json",
    "backend/.env.example",
    "cronJob/cron_runner.php"
)

foreach ($file in $essentialFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file exists" -ForegroundColor Green
    } else {
        Write-Host "❌ $file missing" -ForegroundColor Red
        $issues += "$file missing"
        $verificationPassed = $false
    }
}

# Check for sensitive files
Write-Host "`n🔒 Checking for sensitive files..." -ForegroundColor Cyan

$sensitivePatterns = @("*.env", "config.dev.*", "*password*", "*secret*")
$foundSensitive = @()

foreach ($pattern in $sensitivePatterns) {
    $files = Get-ChildItem -Path . -Name $pattern -Recurse -ErrorAction SilentlyContinue | Where-Object { $_ -notmatch "\.env\.example$" }
    if ($files) {
        $foundSensitive += $files
    }
}

if ($foundSensitive.Count -eq 0) {
    Write-Host "✅ No sensitive files in repository" -ForegroundColor Green
} else {
    Write-Host "⚠️  Found sensitive files (review before deployment):" -ForegroundColor Yellow
    foreach ($file in $foundSensitive) {
        Write-Host "  - $file" -ForegroundColor Yellow
    }
}

# Check file permissions (basic check)
Write-Host "`n🔐 Checking file structure..." -ForegroundColor Cyan

try {
    $phpFiles = Get-ChildItem -Path . -Name "*.php" -Recurse -ErrorAction SilentlyContinue
    Write-Host "✅ Found $($phpFiles.Count) PHP files" -ForegroundColor Green
    
    $cssFiles = Get-ChildItem -Path . -Name "*.css" -Recurse -ErrorAction SilentlyContinue
    Write-Host "✅ Found $($cssFiles.Count) CSS files" -ForegroundColor Green
    
    $jsFiles = Get-ChildItem -Path . -Name "*.js" -Recurse -ErrorAction SilentlyContinue
    Write-Host "✅ Found $($jsFiles.Count) JavaScript files" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not scan file structure" -ForegroundColor Yellow
}

# Summary
Write-Host "`n📊 Verification Summary" -ForegroundColor Yellow
Write-Host "======================" -ForegroundColor Yellow

if ($verificationPassed -and $issues.Count -eq 0) {
    Write-Host "`n🎉 VERIFICATION PASSED!" -ForegroundColor Green
    Write-Host "✅ All checks completed successfully" -ForegroundColor Green
    Write-Host "✅ Codebase is ready for production deployment" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  ISSUES FOUND:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "  ❌ $issue" -ForegroundColor Red
    }
    Write-Host "`nPlease resolve these issues before deployment." -ForegroundColor Yellow
}

# Next steps
Write-Host "`n🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Review PRODUCTION_DEPLOYMENT_GUIDE.md" -ForegroundColor White
Write-Host "2. Configure production environment variables" -ForegroundColor White
Write-Host "3. Set up database and run schema scripts" -ForegroundColor White
Write-Host "4. Upload files to production server" -ForegroundColor White
Write-Host "5. Configure web server and SSL" -ForegroundColor White
Write-Host "6. Run post-deployment tests" -ForegroundColor White

Write-Host "`n✨ TLS Wallet is ready for production! ✨" -ForegroundColor Green
