<?php
/**
 * Navigation Helper Functions
 * 
 * This file contains helper functions for managing navigation
 * across the TRON Wallet user dashboard pages.
 */

/**
 * Generate page variables for header inclusion
 * 
 * @param string $title Page title
 * @param string $currentPage Current page identifier for active navigation
 * @param string $basePath Base path for navigation links (default: '.')
 * @param string $cssPath Path to CSS files (default: 'css')
 * @return array Page variables
 */
function getPageVars($title, $currentPage, $basePath = '.', $cssPath = 'css') {
    return [
        'pageTitle' => 'TRON Wallet - ' . $title,
        'currentPage' => $currentPage,
        'basePath' => $basePath,
        'cssPath' => $cssPath
    ];
}

/**
 * Include header with page variables
 * 
 * @param array $vars Page variables from getPageVars()
 */
function includeHeader($vars) {
    extract($vars);
    include '../includes/header.php';
}

/**
 * Include footer
 */
function includeFooter() {
    include '../includes/footer.php';
}

/**
 * Check if user is authenticated and redirect if not
 * 
 * @param string $redirectTo Where to redirect if not authenticated
 */
function requireAuth($redirectTo = '../index.php') {
    if (!FrontendConfig::isAuthenticated()) {
        header('Location: ' . $redirectTo);
        exit;
    }
}

/**
 * Get current user safely
 * 
 * @return array|null User data or null
 */
function getCurrentUser() {
    return FrontendConfig::getCurrentUser();
}

/**
 * Navigation menu items
 */
function getNavigationItems() {
    return [
        'dashboard' => ['label' => 'Dashboard', 'url' => 'dashboard.php'],
        'wallet' => ['label' => 'Wallet', 'url' => 'wallet.php'],
        'transactions' => ['label' => 'Transactions', 'url' => 'transactions.php'],
        'deposit' => ['label' => 'Deposit', 'url' => 'deposit.php'],
        'profile' => ['label' => 'Profile', 'url' => 'profile.php']
    ];
}
?>
