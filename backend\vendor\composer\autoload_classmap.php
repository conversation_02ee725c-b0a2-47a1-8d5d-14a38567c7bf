<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'BI\\BigInteger' => $vendorDir . '/simplito/bigint-wrapper-php/lib/BigInteger.php',
    'BN\\BN' => $vendorDir . '/simplito/bn-php/lib/BN.php',
    'BN\\Red' => $vendorDir . '/simplito/bn-php/lib/Red.php',
    'Comely\\DataTypes\\BcMath\\BaseConvert' => $vendorDir . '/comely-io/data-types/src/BcMath/BaseConvert.php',
    'Comely\\DataTypes\\BcMath\\BcMath' => $vendorDir . '/comely-io/data-types/src/BcMath/BcMath.php',
    'Comely\\DataTypes\\BcNumber' => $vendorDir . '/comely-io/data-types/src/BcNumber.php',
    'Comely\\DataTypes\\Buffer\\AbstractBuffer' => $vendorDir . '/comely-io/data-types/src/Buffer/AbstractBuffer.php',
    'Comely\\DataTypes\\Buffer\\Base16' => $vendorDir . '/comely-io/data-types/src/Buffer/Base16.php',
    'Comely\\DataTypes\\Buffer\\Base16\\Decoder' => $vendorDir . '/comely-io/data-types/src/Buffer/Base16/Decoder.php',
    'Comely\\DataTypes\\Buffer\\Base64' => $vendorDir . '/comely-io/data-types/src/Buffer/Base64.php',
    'Comely\\DataTypes\\Buffer\\Binary' => $vendorDir . '/comely-io/data-types/src/Buffer/Binary.php',
    'Comely\\DataTypes\\Buffer\\Binary\\ByteReader' => $vendorDir . '/comely-io/data-types/src/Buffer/Binary/ByteReader.php',
    'Comely\\DataTypes\\Buffer\\Binary\\Digest' => $vendorDir . '/comely-io/data-types/src/Buffer/Binary/Digest.php',
    'Comely\\DataTypes\\Buffer\\Binary\\LenSize' => $vendorDir . '/comely-io/data-types/src/Buffer/Binary/LenSize.php',
    'Comely\\DataTypes\\Buffer\\Bitwise' => $vendorDir . '/comely-io/data-types/src/Buffer/Bitwise.php',
    'Comely\\DataTypes\\DataTypes' => $vendorDir . '/comely-io/data-types/src/DataTypes.php',
    'Comely\\DataTypes\\Integers' => $vendorDir . '/comely-io/data-types/src/Integers.php',
    'Comely\\DataTypes\\Strings\\ASCII' => $vendorDir . '/comely-io/data-types/src/Strings/ASCII.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Elliptic\\Curve\\BaseCurve' => $vendorDir . '/simplito/elliptic-php/lib/Curve/BaseCurve.php',
    'Elliptic\\Curve\\BaseCurve\\Point' => $vendorDir . '/simplito/elliptic-php/lib/Curve/BaseCurve/Point.php',
    'Elliptic\\Curve\\EdwardsCurve' => $vendorDir . '/simplito/elliptic-php/lib/Curve/EdwardsCurve.php',
    'Elliptic\\Curve\\EdwardsCurve\\Point' => $vendorDir . '/simplito/elliptic-php/lib/Curve/EdwardsCurve/Point.php',
    'Elliptic\\Curve\\MontCurve' => $vendorDir . '/simplito/elliptic-php/lib/Curve/MontCurve.php',
    'Elliptic\\Curve\\MontCurve\\Point' => $vendorDir . '/simplito/elliptic-php/lib/Curve/MontCurve/Point.php',
    'Elliptic\\Curve\\PresetCurve' => $vendorDir . '/simplito/elliptic-php/lib/Curve/PresetCurve.php',
    'Elliptic\\Curve\\ShortCurve' => $vendorDir . '/simplito/elliptic-php/lib/Curve/ShortCurve.php',
    'Elliptic\\Curve\\ShortCurve\\JPoint' => $vendorDir . '/simplito/elliptic-php/lib/Curve/ShortCurve/JPoint.php',
    'Elliptic\\Curve\\ShortCurve\\Point' => $vendorDir . '/simplito/elliptic-php/lib/Curve/ShortCurve/Point.php',
    'Elliptic\\Curves' => $vendorDir . '/simplito/elliptic-php/lib/Curves.php',
    'Elliptic\\EC' => $vendorDir . '/simplito/elliptic-php/lib/EC.php',
    'Elliptic\\EC\\KeyPair' => $vendorDir . '/simplito/elliptic-php/lib/EC/KeyPair.php',
    'Elliptic\\EC\\Signature' => $vendorDir . '/simplito/elliptic-php/lib/EC/Signature.php',
    'Elliptic\\EdDSA' => $vendorDir . '/simplito/elliptic-php/lib/EdDSA.php',
    'Elliptic\\EdDSA\\KeyPair' => $vendorDir . '/simplito/elliptic-php/lib/EdDSA/KeyPair.php',
    'Elliptic\\EdDSA\\Signature' => $vendorDir . '/simplito/elliptic-php/lib/EdDSA/Signature.php',
    'Elliptic\\HmacDRBG' => $vendorDir . '/simplito/elliptic-php/lib/HmacDRBG.php',
    'Elliptic\\Utils' => $vendorDir . '/simplito/elliptic-php/lib/Utils.php',
    'FG\\ASN1\\ASNObject' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/ASNObject.php',
    'FG\\ASN1\\AbstractString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/AbstractString.php',
    'FG\\ASN1\\AbstractTime' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/AbstractTime.php',
    'FG\\ASN1\\Base128' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Base128.php',
    'FG\\ASN1\\Composite\\AttributeTypeAndValue' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Composite/AttributeTypeAndValue.php',
    'FG\\ASN1\\Composite\\RDNString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Composite/RDNString.php',
    'FG\\ASN1\\Composite\\RelativeDistinguishedName' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Composite/RelativeDistinguishedName.php',
    'FG\\ASN1\\Construct' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Construct.php',
    'FG\\ASN1\\Exception\\NotImplementedException' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Exception/NotImplementedException.php',
    'FG\\ASN1\\Exception\\ParserException' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Exception/ParserException.php',
    'FG\\ASN1\\ExplicitlyTaggedObject' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/ExplicitlyTaggedObject.php',
    'FG\\ASN1\\Identifier' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Identifier.php',
    'FG\\ASN1\\OID' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/OID.php',
    'FG\\ASN1\\Parsable' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Parsable.php',
    'FG\\ASN1\\TemplateParser' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/TemplateParser.php',
    'FG\\ASN1\\Universal\\BMPString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/BMPString.php',
    'FG\\ASN1\\Universal\\BitString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/BitString.php',
    'FG\\ASN1\\Universal\\Boolean' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/Boolean.php',
    'FG\\ASN1\\Universal\\CharacterString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/CharacterString.php',
    'FG\\ASN1\\Universal\\Enumerated' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/Enumerated.php',
    'FG\\ASN1\\Universal\\GeneralString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/GeneralString.php',
    'FG\\ASN1\\Universal\\GeneralizedTime' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/GeneralizedTime.php',
    'FG\\ASN1\\Universal\\GraphicString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/GraphicString.php',
    'FG\\ASN1\\Universal\\IA5String' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/IA5String.php',
    'FG\\ASN1\\Universal\\Integer' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/Integer.php',
    'FG\\ASN1\\Universal\\NullObject' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/NullObject.php',
    'FG\\ASN1\\Universal\\NumericString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/NumericString.php',
    'FG\\ASN1\\Universal\\ObjectDescriptor' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/ObjectDescriptor.php',
    'FG\\ASN1\\Universal\\ObjectIdentifier' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/ObjectIdentifier.php',
    'FG\\ASN1\\Universal\\OctetString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/OctetString.php',
    'FG\\ASN1\\Universal\\PrintableString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/PrintableString.php',
    'FG\\ASN1\\Universal\\RelativeObjectIdentifier' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/RelativeObjectIdentifier.php',
    'FG\\ASN1\\Universal\\Sequence' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/Sequence.php',
    'FG\\ASN1\\Universal\\Set' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/Set.php',
    'FG\\ASN1\\Universal\\T61String' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/T61String.php',
    'FG\\ASN1\\Universal\\UTCTime' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/UTCTime.php',
    'FG\\ASN1\\Universal\\UTF8String' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/UTF8String.php',
    'FG\\ASN1\\Universal\\UniversalString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/UniversalString.php',
    'FG\\ASN1\\Universal\\VisibleString' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/Universal/VisibleString.php',
    'FG\\ASN1\\UnknownConstructedObject' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/UnknownConstructedObject.php',
    'FG\\ASN1\\UnknownObject' => $vendorDir . '/fgrosse/phpasn1/lib/ASN1/UnknownObject.php',
    'FG\\Utility\\BigInteger' => $vendorDir . '/fgrosse/phpasn1/lib/Utility/BigInteger.php',
    'FG\\Utility\\BigIntegerBcmath' => $vendorDir . '/fgrosse/phpasn1/lib/Utility/BigIntegerBcmath.php',
    'FG\\Utility\\BigIntegerGmp' => $vendorDir . '/fgrosse/phpasn1/lib/Utility/BigIntegerGmp.php',
    'FG\\X509\\AlgorithmIdentifier' => $vendorDir . '/fgrosse/phpasn1/lib/X509/AlgorithmIdentifier.php',
    'FG\\X509\\CSR\\Attributes' => $vendorDir . '/fgrosse/phpasn1/lib/X509/CSR/Attributes.php',
    'FG\\X509\\CSR\\CSR' => $vendorDir . '/fgrosse/phpasn1/lib/X509/CSR/CSR.php',
    'FG\\X509\\CertificateExtensions' => $vendorDir . '/fgrosse/phpasn1/lib/X509/CertificateExtensions.php',
    'FG\\X509\\CertificateSubject' => $vendorDir . '/fgrosse/phpasn1/lib/X509/CertificateSubject.php',
    'FG\\X509\\PrivateKey' => $vendorDir . '/fgrosse/phpasn1/lib/X509/PrivateKey.php',
    'FG\\X509\\PublicKey' => $vendorDir . '/fgrosse/phpasn1/lib/X509/PublicKey.php',
    'FG\\X509\\SAN\\DNSName' => $vendorDir . '/fgrosse/phpasn1/lib/X509/SAN/DNSName.php',
    'FG\\X509\\SAN\\IPAddress' => $vendorDir . '/fgrosse/phpasn1/lib/X509/SAN/IPAddress.php',
    'FG\\X509\\SAN\\SubjectAlternativeNames' => $vendorDir . '/fgrosse/phpasn1/lib/X509/SAN/SubjectAlternativeNames.php',
    'GuzzleHttp\\BodySummarizer' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizer.php',
    'GuzzleHttp\\BodySummarizerInterface' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
    'GuzzleHttp\\Client' => $vendorDir . '/guzzlehttp/guzzle/src/Client.php',
    'GuzzleHttp\\ClientInterface' => $vendorDir . '/guzzlehttp/guzzle/src/ClientInterface.php',
    'GuzzleHttp\\ClientTrait' => $vendorDir . '/guzzlehttp/guzzle/src/ClientTrait.php',
    'GuzzleHttp\\Cookie\\CookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
    'GuzzleHttp\\Cookie\\CookieJarInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
    'GuzzleHttp\\Cookie\\FileCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
    'GuzzleHttp\\Cookie\\SessionCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
    'GuzzleHttp\\Cookie\\SetCookie' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
    'GuzzleHttp\\Exception\\BadResponseException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
    'GuzzleHttp\\Exception\\ClientException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ClientException.php',
    'GuzzleHttp\\Exception\\ConnectException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ConnectException.php',
    'GuzzleHttp\\Exception\\GuzzleException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
    'GuzzleHttp\\Exception\\InvalidArgumentException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
    'GuzzleHttp\\Exception\\RequestException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/RequestException.php',
    'GuzzleHttp\\Exception\\ServerException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ServerException.php',
    'GuzzleHttp\\Exception\\TooManyRedirectsException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
    'GuzzleHttp\\Exception\\TransferException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TransferException.php',
    'GuzzleHttp\\HandlerStack' => $vendorDir . '/guzzlehttp/guzzle/src/HandlerStack.php',
    'GuzzleHttp\\Handler\\CurlFactory' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
    'GuzzleHttp\\Handler\\CurlFactoryInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
    'GuzzleHttp\\Handler\\CurlHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
    'GuzzleHttp\\Handler\\CurlMultiHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
    'GuzzleHttp\\Handler\\EasyHandle' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
    'GuzzleHttp\\Handler\\HeaderProcessor' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
    'GuzzleHttp\\Handler\\MockHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/MockHandler.php',
    'GuzzleHttp\\Handler\\Proxy' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/Proxy.php',
    'GuzzleHttp\\Handler\\StreamHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
    'GuzzleHttp\\MessageFormatter' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatter.php',
    'GuzzleHttp\\MessageFormatterInterface' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
    'GuzzleHttp\\Middleware' => $vendorDir . '/guzzlehttp/guzzle/src/Middleware.php',
    'GuzzleHttp\\Pool' => $vendorDir . '/guzzlehttp/guzzle/src/Pool.php',
    'GuzzleHttp\\PrepareBodyMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
    'GuzzleHttp\\Promise\\AggregateException' => $vendorDir . '/guzzlehttp/promises/src/AggregateException.php',
    'GuzzleHttp\\Promise\\CancellationException' => $vendorDir . '/guzzlehttp/promises/src/CancellationException.php',
    'GuzzleHttp\\Promise\\Coroutine' => $vendorDir . '/guzzlehttp/promises/src/Coroutine.php',
    'GuzzleHttp\\Promise\\Create' => $vendorDir . '/guzzlehttp/promises/src/Create.php',
    'GuzzleHttp\\Promise\\Each' => $vendorDir . '/guzzlehttp/promises/src/Each.php',
    'GuzzleHttp\\Promise\\EachPromise' => $vendorDir . '/guzzlehttp/promises/src/EachPromise.php',
    'GuzzleHttp\\Promise\\FulfilledPromise' => $vendorDir . '/guzzlehttp/promises/src/FulfilledPromise.php',
    'GuzzleHttp\\Promise\\Is' => $vendorDir . '/guzzlehttp/promises/src/Is.php',
    'GuzzleHttp\\Promise\\Promise' => $vendorDir . '/guzzlehttp/promises/src/Promise.php',
    'GuzzleHttp\\Promise\\PromiseInterface' => $vendorDir . '/guzzlehttp/promises/src/PromiseInterface.php',
    'GuzzleHttp\\Promise\\PromisorInterface' => $vendorDir . '/guzzlehttp/promises/src/PromisorInterface.php',
    'GuzzleHttp\\Promise\\RejectedPromise' => $vendorDir . '/guzzlehttp/promises/src/RejectedPromise.php',
    'GuzzleHttp\\Promise\\RejectionException' => $vendorDir . '/guzzlehttp/promises/src/RejectionException.php',
    'GuzzleHttp\\Promise\\TaskQueue' => $vendorDir . '/guzzlehttp/promises/src/TaskQueue.php',
    'GuzzleHttp\\Promise\\TaskQueueInterface' => $vendorDir . '/guzzlehttp/promises/src/TaskQueueInterface.php',
    'GuzzleHttp\\Promise\\Utils' => $vendorDir . '/guzzlehttp/promises/src/Utils.php',
    'GuzzleHttp\\Psr7\\AppendStream' => $vendorDir . '/guzzlehttp/psr7/src/AppendStream.php',
    'GuzzleHttp\\Psr7\\BufferStream' => $vendorDir . '/guzzlehttp/psr7/src/BufferStream.php',
    'GuzzleHttp\\Psr7\\CachingStream' => $vendorDir . '/guzzlehttp/psr7/src/CachingStream.php',
    'GuzzleHttp\\Psr7\\DroppingStream' => $vendorDir . '/guzzlehttp/psr7/src/DroppingStream.php',
    'GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => $vendorDir . '/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
    'GuzzleHttp\\Psr7\\FnStream' => $vendorDir . '/guzzlehttp/psr7/src/FnStream.php',
    'GuzzleHttp\\Psr7\\Header' => $vendorDir . '/guzzlehttp/psr7/src/Header.php',
    'GuzzleHttp\\Psr7\\HttpFactory' => $vendorDir . '/guzzlehttp/psr7/src/HttpFactory.php',
    'GuzzleHttp\\Psr7\\InflateStream' => $vendorDir . '/guzzlehttp/psr7/src/InflateStream.php',
    'GuzzleHttp\\Psr7\\LazyOpenStream' => $vendorDir . '/guzzlehttp/psr7/src/LazyOpenStream.php',
    'GuzzleHttp\\Psr7\\LimitStream' => $vendorDir . '/guzzlehttp/psr7/src/LimitStream.php',
    'GuzzleHttp\\Psr7\\Message' => $vendorDir . '/guzzlehttp/psr7/src/Message.php',
    'GuzzleHttp\\Psr7\\MessageTrait' => $vendorDir . '/guzzlehttp/psr7/src/MessageTrait.php',
    'GuzzleHttp\\Psr7\\MimeType' => $vendorDir . '/guzzlehttp/psr7/src/MimeType.php',
    'GuzzleHttp\\Psr7\\MultipartStream' => $vendorDir . '/guzzlehttp/psr7/src/MultipartStream.php',
    'GuzzleHttp\\Psr7\\NoSeekStream' => $vendorDir . '/guzzlehttp/psr7/src/NoSeekStream.php',
    'GuzzleHttp\\Psr7\\PumpStream' => $vendorDir . '/guzzlehttp/psr7/src/PumpStream.php',
    'GuzzleHttp\\Psr7\\Query' => $vendorDir . '/guzzlehttp/psr7/src/Query.php',
    'GuzzleHttp\\Psr7\\Request' => $vendorDir . '/guzzlehttp/psr7/src/Request.php',
    'GuzzleHttp\\Psr7\\Response' => $vendorDir . '/guzzlehttp/psr7/src/Response.php',
    'GuzzleHttp\\Psr7\\Rfc7230' => $vendorDir . '/guzzlehttp/psr7/src/Rfc7230.php',
    'GuzzleHttp\\Psr7\\ServerRequest' => $vendorDir . '/guzzlehttp/psr7/src/ServerRequest.php',
    'GuzzleHttp\\Psr7\\Stream' => $vendorDir . '/guzzlehttp/psr7/src/Stream.php',
    'GuzzleHttp\\Psr7\\StreamDecoratorTrait' => $vendorDir . '/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
    'GuzzleHttp\\Psr7\\StreamWrapper' => $vendorDir . '/guzzlehttp/psr7/src/StreamWrapper.php',
    'GuzzleHttp\\Psr7\\UploadedFile' => $vendorDir . '/guzzlehttp/psr7/src/UploadedFile.php',
    'GuzzleHttp\\Psr7\\Uri' => $vendorDir . '/guzzlehttp/psr7/src/Uri.php',
    'GuzzleHttp\\Psr7\\UriComparator' => $vendorDir . '/guzzlehttp/psr7/src/UriComparator.php',
    'GuzzleHttp\\Psr7\\UriNormalizer' => $vendorDir . '/guzzlehttp/psr7/src/UriNormalizer.php',
    'GuzzleHttp\\Psr7\\UriResolver' => $vendorDir . '/guzzlehttp/psr7/src/UriResolver.php',
    'GuzzleHttp\\Psr7\\Utils' => $vendorDir . '/guzzlehttp/psr7/src/Utils.php',
    'GuzzleHttp\\RedirectMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RedirectMiddleware.php',
    'GuzzleHttp\\RequestOptions' => $vendorDir . '/guzzlehttp/guzzle/src/RequestOptions.php',
    'GuzzleHttp\\RetryMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RetryMiddleware.php',
    'GuzzleHttp\\TransferStats' => $vendorDir . '/guzzlehttp/guzzle/src/TransferStats.php',
    'GuzzleHttp\\Utils' => $vendorDir . '/guzzlehttp/guzzle/src/Utils.php',
    'IEXBase\\TronAPI\\Concerns\\ManagesTronscan' => $vendorDir . '/iexbase/tron-api/src/Concerns/ManagesTronscan.php',
    'IEXBase\\TronAPI\\Concerns\\ManagesUniversal' => $vendorDir . '/iexbase/tron-api/src/Concerns/ManagesUniversal.php',
    'IEXBase\\TronAPI\\Exception\\ErrorException' => $vendorDir . '/iexbase/tron-api/src/Exception/ErrorException.php',
    'IEXBase\\TronAPI\\Exception\\NotFoundException' => $vendorDir . '/iexbase/tron-api/src/Exception/NotFoundException.php',
    'IEXBase\\TronAPI\\Exception\\TRC20Exception' => $vendorDir . '/iexbase/tron-api/src/Exception/TRC20Exception.php',
    'IEXBase\\TronAPI\\Exception\\TronException' => $vendorDir . '/iexbase/tron-api/src/Exception/TronException.php',
    'IEXBase\\TronAPI\\Provider\\HttpProvider' => $vendorDir . '/iexbase/tron-api/src/Provider/HttpProvider.php',
    'IEXBase\\TronAPI\\Provider\\HttpProviderInterface' => $vendorDir . '/iexbase/tron-api/src/Provider/HttpProviderInterface.php',
    'IEXBase\\TronAPI\\Support\\Base58' => $vendorDir . '/iexbase/tron-api/src/Support/Base58.php',
    'IEXBase\\TronAPI\\Support\\Base58Check' => $vendorDir . '/iexbase/tron-api/src/Support/Base58Check.php',
    'IEXBase\\TronAPI\\Support\\BigInteger' => $vendorDir . '/iexbase/tron-api/src/Support/BigInteger.php',
    'IEXBase\\TronAPI\\Support\\Crypto' => $vendorDir . '/iexbase/tron-api/src/Support/Crypto.php',
    'IEXBase\\TronAPI\\Support\\Hash' => $vendorDir . '/iexbase/tron-api/src/Support/Hash.php',
    'IEXBase\\TronAPI\\Support\\Keccak' => $vendorDir . '/iexbase/tron-api/src/Support/Keccak.php',
    'IEXBase\\TronAPI\\Support\\Secp' => $vendorDir . '/iexbase/tron-api/src/Support/Secp.php',
    'IEXBase\\TronAPI\\Support\\Utils' => $vendorDir . '/iexbase/tron-api/src/Support/Utils.php',
    'IEXBase\\TronAPI\\TRC20Contract' => $vendorDir . '/iexbase/tron-api/src/TRC20Contract.php',
    'IEXBase\\TronAPI\\TransactionBuilder' => $vendorDir . '/iexbase/tron-api/src/TransactionBuilder.php',
    'IEXBase\\TronAPI\\Tron' => $vendorDir . '/iexbase/tron-api/src/Tron.php',
    'IEXBase\\TronAPI\\TronAddress' => $vendorDir . '/iexbase/tron-api/src/TronAddress.php',
    'IEXBase\\TronAPI\\TronAwareTrait' => $vendorDir . '/iexbase/tron-api/src/TronAwareTrait.php',
    'IEXBase\\TronAPI\\TronInterface' => $vendorDir . '/iexbase/tron-api/src/TronInterface.php',
    'IEXBase\\TronAPI\\TronManager' => $vendorDir . '/iexbase/tron-api/src/TronManager.php',
    'Mdanter\\Ecc\\Crypto\\EcDH\\EcDH' => $vendorDir . '/mdanter/ecc/src/Crypto/EcDH/EcDH.php',
    'Mdanter\\Ecc\\Crypto\\EcDH\\EcDHInterface' => $vendorDir . '/mdanter/ecc/src/Crypto/EcDH/EcDHInterface.php',
    'Mdanter\\Ecc\\Crypto\\Key\\PrivateKey' => $vendorDir . '/mdanter/ecc/src/Crypto/Key/PrivateKey.php',
    'Mdanter\\Ecc\\Crypto\\Key\\PrivateKeyInterface' => $vendorDir . '/mdanter/ecc/src/Crypto/Key/PrivateKeyInterface.php',
    'Mdanter\\Ecc\\Crypto\\Key\\PublicKey' => $vendorDir . '/mdanter/ecc/src/Crypto/Key/PublicKey.php',
    'Mdanter\\Ecc\\Crypto\\Key\\PublicKeyInterface' => $vendorDir . '/mdanter/ecc/src/Crypto/Key/PublicKeyInterface.php',
    'Mdanter\\Ecc\\Crypto\\Signature\\HasherInterface' => $vendorDir . '/mdanter/ecc/src/Crypto/Signature/HasherInterface.php',
    'Mdanter\\Ecc\\Crypto\\Signature\\SignHasher' => $vendorDir . '/mdanter/ecc/src/Crypto/Signature/SignHasher.php',
    'Mdanter\\Ecc\\Crypto\\Signature\\Signature' => $vendorDir . '/mdanter/ecc/src/Crypto/Signature/Signature.php',
    'Mdanter\\Ecc\\Crypto\\Signature\\SignatureInterface' => $vendorDir . '/mdanter/ecc/src/Crypto/Signature/SignatureInterface.php',
    'Mdanter\\Ecc\\Crypto\\Signature\\Signer' => $vendorDir . '/mdanter/ecc/src/Crypto/Signature/Signer.php',
    'Mdanter\\Ecc\\Curves\\CurveFactory' => $vendorDir . '/mdanter/ecc/src/Curves/CurveFactory.php',
    'Mdanter\\Ecc\\Curves\\NamedCurveFp' => $vendorDir . '/mdanter/ecc/src/Curves/NamedCurveFp.php',
    'Mdanter\\Ecc\\Curves\\NistCurve' => $vendorDir . '/mdanter/ecc/src/Curves/NistCurve.php',
    'Mdanter\\Ecc\\Curves\\SecgCurve' => $vendorDir . '/mdanter/ecc/src/Curves/SecgCurve.php',
    'Mdanter\\Ecc\\EccFactory' => $vendorDir . '/mdanter/ecc/src/EccFactory.php',
    'Mdanter\\Ecc\\Exception\\ExchangeException' => $vendorDir . '/mdanter/ecc/src/Exception/ExchangeException.php',
    'Mdanter\\Ecc\\Exception\\NumberTheoryException' => $vendorDir . '/mdanter/ecc/src/Exception/NumberTheoryException.php',
    'Mdanter\\Ecc\\Exception\\PointException' => $vendorDir . '/mdanter/ecc/src/Exception/PointException.php',
    'Mdanter\\Ecc\\Exception\\PointNotOnCurveException' => $vendorDir . '/mdanter/ecc/src/Exception/PointNotOnCurveException.php',
    'Mdanter\\Ecc\\Exception\\PointRecoveryException' => $vendorDir . '/mdanter/ecc/src/Exception/PointRecoveryException.php',
    'Mdanter\\Ecc\\Exception\\PublicKeyException' => $vendorDir . '/mdanter/ecc/src/Exception/PublicKeyException.php',
    'Mdanter\\Ecc\\Exception\\SignatureDecodeException' => $vendorDir . '/mdanter/ecc/src/Exception/SignatureDecodeException.php',
    'Mdanter\\Ecc\\Exception\\SquareRootException' => $vendorDir . '/mdanter/ecc/src/Exception/SquareRootException.php',
    'Mdanter\\Ecc\\Exception\\UnsupportedCurveException' => $vendorDir . '/mdanter/ecc/src/Exception/UnsupportedCurveException.php',
    'Mdanter\\Ecc\\Math\\DebugDecorator' => $vendorDir . '/mdanter/ecc/src/Math/DebugDecorator.php',
    'Mdanter\\Ecc\\Math\\GmpMath' => $vendorDir . '/mdanter/ecc/src/Math/GmpMath.php',
    'Mdanter\\Ecc\\Math\\GmpMathInterface' => $vendorDir . '/mdanter/ecc/src/Math/GmpMathInterface.php',
    'Mdanter\\Ecc\\Math\\MathAdapterFactory' => $vendorDir . '/mdanter/ecc/src/Math/MathAdapterFactory.php',
    'Mdanter\\Ecc\\Math\\ModularArithmetic' => $vendorDir . '/mdanter/ecc/src/Math/ModularArithmetic.php',
    'Mdanter\\Ecc\\Math\\NumberTheory' => $vendorDir . '/mdanter/ecc/src/Math/NumberTheory.php',
    'Mdanter\\Ecc\\Primitives\\CurveFp' => $vendorDir . '/mdanter/ecc/src/Primitives/CurveFp.php',
    'Mdanter\\Ecc\\Primitives\\CurveFpInterface' => $vendorDir . '/mdanter/ecc/src/Primitives/CurveFpInterface.php',
    'Mdanter\\Ecc\\Primitives\\CurveParameters' => $vendorDir . '/mdanter/ecc/src/Primitives/CurveParameters.php',
    'Mdanter\\Ecc\\Primitives\\GeneratorPoint' => $vendorDir . '/mdanter/ecc/src/Primitives/GeneratorPoint.php',
    'Mdanter\\Ecc\\Primitives\\Point' => $vendorDir . '/mdanter/ecc/src/Primitives/Point.php',
    'Mdanter\\Ecc\\Primitives\\PointInterface' => $vendorDir . '/mdanter/ecc/src/Primitives/PointInterface.php',
    'Mdanter\\Ecc\\Random\\DebugDecorator' => $vendorDir . '/mdanter/ecc/src/Random/DebugDecorator.php',
    'Mdanter\\Ecc\\Random\\HmacRandomNumberGenerator' => $vendorDir . '/mdanter/ecc/src/Random/HmacRandomNumberGenerator.php',
    'Mdanter\\Ecc\\Random\\RandomGeneratorFactory' => $vendorDir . '/mdanter/ecc/src/Random/RandomGeneratorFactory.php',
    'Mdanter\\Ecc\\Random\\RandomNumberGenerator' => $vendorDir . '/mdanter/ecc/src/Random/RandomNumberGenerator.php',
    'Mdanter\\Ecc\\Random\\RandomNumberGeneratorInterface' => $vendorDir . '/mdanter/ecc/src/Random/RandomNumberGeneratorInterface.php',
    'Mdanter\\Ecc\\Serializer\\Point\\CompressedPointSerializer' => $vendorDir . '/mdanter/ecc/src/Serializer/Point/CompressedPointSerializer.php',
    'Mdanter\\Ecc\\Serializer\\Point\\PointSerializerInterface' => $vendorDir . '/mdanter/ecc/src/Serializer/Point/PointSerializerInterface.php',
    'Mdanter\\Ecc\\Serializer\\Point\\UncompressedPointSerializer' => $vendorDir . '/mdanter/ecc/src/Serializer/Point/UncompressedPointSerializer.php',
    'Mdanter\\Ecc\\Serializer\\PrivateKey\\DerPrivateKeySerializer' => $vendorDir . '/mdanter/ecc/src/Serializer/PrivateKey/DerPrivateKeySerializer.php',
    'Mdanter\\Ecc\\Serializer\\PrivateKey\\PemPrivateKeySerializer' => $vendorDir . '/mdanter/ecc/src/Serializer/PrivateKey/PemPrivateKeySerializer.php',
    'Mdanter\\Ecc\\Serializer\\PrivateKey\\PrivateKeySerializerInterface' => $vendorDir . '/mdanter/ecc/src/Serializer/PrivateKey/PrivateKeySerializerInterface.php',
    'Mdanter\\Ecc\\Serializer\\PublicKey\\DerPublicKeySerializer' => $vendorDir . '/mdanter/ecc/src/Serializer/PublicKey/DerPublicKeySerializer.php',
    'Mdanter\\Ecc\\Serializer\\PublicKey\\Der\\Formatter' => $vendorDir . '/mdanter/ecc/src/Serializer/PublicKey/Der/Formatter.php',
    'Mdanter\\Ecc\\Serializer\\PublicKey\\Der\\Parser' => $vendorDir . '/mdanter/ecc/src/Serializer/PublicKey/Der/Parser.php',
    'Mdanter\\Ecc\\Serializer\\PublicKey\\PemPublicKeySerializer' => $vendorDir . '/mdanter/ecc/src/Serializer/PublicKey/PemPublicKeySerializer.php',
    'Mdanter\\Ecc\\Serializer\\PublicKey\\PublicKeySerializerInterface' => $vendorDir . '/mdanter/ecc/src/Serializer/PublicKey/PublicKeySerializerInterface.php',
    'Mdanter\\Ecc\\Serializer\\Signature\\DerSignatureSerializer' => $vendorDir . '/mdanter/ecc/src/Serializer/Signature/DerSignatureSerializer.php',
    'Mdanter\\Ecc\\Serializer\\Signature\\DerSignatureSerializerInterface' => $vendorDir . '/mdanter/ecc/src/Serializer/Signature/DerSignatureSerializerInterface.php',
    'Mdanter\\Ecc\\Serializer\\Signature\\Der\\Formatter' => $vendorDir . '/mdanter/ecc/src/Serializer/Signature/Der/Formatter.php',
    'Mdanter\\Ecc\\Serializer\\Signature\\Der\\Parser' => $vendorDir . '/mdanter/ecc/src/Serializer/Signature/Der/Parser.php',
    'Mdanter\\Ecc\\Serializer\\Util\\CurveOidMapper' => $vendorDir . '/mdanter/ecc/src/Serializer/Util/CurveOidMapper.php',
    'Mdanter\\Ecc\\Util\\BinaryString' => $vendorDir . '/mdanter/ecc/src/Util/BinaryString.php',
    'Mdanter\\Ecc\\Util\\NumberSize' => $vendorDir . '/mdanter/ecc/src/Util/NumberSize.php',
    'Psr\\Http\\Client\\ClientExceptionInterface' => $vendorDir . '/psr/http-client/src/ClientExceptionInterface.php',
    'Psr\\Http\\Client\\ClientInterface' => $vendorDir . '/psr/http-client/src/ClientInterface.php',
    'Psr\\Http\\Client\\NetworkExceptionInterface' => $vendorDir . '/psr/http-client/src/NetworkExceptionInterface.php',
    'Psr\\Http\\Client\\RequestExceptionInterface' => $vendorDir . '/psr/http-client/src/RequestExceptionInterface.php',
    'Psr\\Http\\Message\\MessageInterface' => $vendorDir . '/psr/http-message/src/MessageInterface.php',
    'Psr\\Http\\Message\\RequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/RequestFactoryInterface.php',
    'Psr\\Http\\Message\\RequestInterface' => $vendorDir . '/psr/http-message/src/RequestInterface.php',
    'Psr\\Http\\Message\\ResponseFactoryInterface' => $vendorDir . '/psr/http-factory/src/ResponseFactoryInterface.php',
    'Psr\\Http\\Message\\ResponseInterface' => $vendorDir . '/psr/http-message/src/ResponseInterface.php',
    'Psr\\Http\\Message\\ServerRequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
    'Psr\\Http\\Message\\ServerRequestInterface' => $vendorDir . '/psr/http-message/src/ServerRequestInterface.php',
    'Psr\\Http\\Message\\StreamFactoryInterface' => $vendorDir . '/psr/http-factory/src/StreamFactoryInterface.php',
    'Psr\\Http\\Message\\StreamInterface' => $vendorDir . '/psr/http-message/src/StreamInterface.php',
    'Psr\\Http\\Message\\UploadedFileFactoryInterface' => $vendorDir . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
    'Psr\\Http\\Message\\UploadedFileInterface' => $vendorDir . '/psr/http-message/src/UploadedFileInterface.php',
    'Psr\\Http\\Message\\UriFactoryInterface' => $vendorDir . '/psr/http-factory/src/UriFactoryInterface.php',
    'Psr\\Http\\Message\\UriInterface' => $vendorDir . '/psr/http-message/src/UriInterface.php',
    'Simbi\\Tls\\Config\\Config' => $baseDir . '/src/Config/Config.php',
    'Simbi\\Tls\\Config\\Database' => $baseDir . '/src/Config/Database.php',
    'Simbi\\Tls\\Controllers\\AdminController' => $baseDir . '/src/Controllers/AdminController.php',
    'Simbi\\Tls\\Controllers\\AuthController' => $baseDir . '/src/Controllers/AuthController.php',
    'Simbi\\Tls\\Controllers\\InvestmentController' => $baseDir . '/src/Controllers/InvestmentController.php',
    'Simbi\\Tls\\Controllers\\PaymentController' => $baseDir . '/src/Controllers/PaymentController.php',
    'Simbi\\Tls\\Controllers\\TransactionController' => $baseDir . '/src/Controllers/TransactionController.php',
    'Simbi\\Tls\\Controllers\\WalletController' => $baseDir . '/src/Controllers/WalletController.php',
    'Simbi\\Tls\\CronJob\\Config\\CronConfig' => $baseDir . '/../cronJob/src/Config/CronConfig.php',
    'Simbi\\Tls\\CronJob\\CronRunner' => $baseDir . '/../cronJob/src/CronRunner.php',
    'Simbi\\Tls\\CronJob\\Services\\PaymentConfirmationService' => $baseDir . '/../cronJob/src/Services/PaymentConfirmationService.php',
    'Simbi\\Tls\\CronJob\\Services\\TronGridService' => $baseDir . '/../cronJob/src/Services/TronGridService.php',
    'Simbi\\Tls\\Frontend\\Config\\FrontendConfig' => $baseDir . '/../frontend/src/Config/FrontendConfig.php',
    'Simbi\\Tls\\Frontend\\Services\\AjaxHandler' => $baseDir . '/../frontend/src/Services/AjaxHandler.php',
    'Simbi\\Tls\\Frontend\\Services\\ApiService' => $baseDir . '/../frontend/src/Services/ApiService.php',
    'Simbi\\Tls\\Frontend\\Services\\SessionService' => $baseDir . '/../frontend/src/Services/SessionService.php',
    'Simbi\\Tls\\Frontend\\Utils\\FormatUtils' => $baseDir . '/../frontend/src/Utils/FormatUtils.php',
    'Simbi\\Tls\\Frontend\\Utils\\ValidationUtils' => $baseDir . '/../frontend/src/Utils/ValidationUtils.php',
    'Simbi\\Tls\\Middleware\\AdminMiddleware' => $baseDir . '/src/Middleware/AdminMiddleware.php',
    'Simbi\\Tls\\Middleware\\AuthMiddleware' => $baseDir . '/src/Middleware/AuthMiddleware.php',
    'Simbi\\Tls\\Repositories\\AdminRepository' => $baseDir . '/src/Repositories/AdminRepository.php',
    'Simbi\\Tls\\Repositories\\TransactionRepository' => $baseDir . '/src/Repositories/TransactionRepository.php',
    'Simbi\\Tls\\Repositories\\UserRepository' => $baseDir . '/src/Repositories/UserRepository.php',
    'Simbi\\Tls\\Repositories\\WalletRepository' => $baseDir . '/src/Repositories/WalletRepository.php',
    'Simbi\\Tls\\Services\\AuthService' => $baseDir . '/src/Services/AuthService.php',
    'Simbi\\Tls\\Services\\ConfigurationService' => $baseDir . '/src/Services/ConfigurationService.php',
    'Simbi\\Tls\\Services\\InvestmentService' => $baseDir . '/src/Services/InvestmentService.php',
    'Simbi\\Tls\\Services\\Router' => $baseDir . '/src/Services/Router.php',
    'Simbi\\Tls\\Services\\TransactionService' => $baseDir . '/src/Services/TransactionService.php',
    'Simbi\\Tls\\Services\\TronGridService' => $baseDir . '/src/Services/TronGridService.php',
    'Simbi\\Tls\\Services\\TronService' => $baseDir . '/src/Services/TronService.php',
    'Simbi\\Tls\\Services\\WalletService' => $baseDir . '/src/Services/WalletService.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
    'Web3\\Contract' => $vendorDir . '/iexbase/web3.php/src/Contract.php',
    'Web3\\Contracts\\Ethabi' => $vendorDir . '/iexbase/web3.php/src/Contracts/Ethabi.php',
    'Web3\\Contracts\\SolidityType' => $vendorDir . '/iexbase/web3.php/src/Contracts/SolidityType.php',
    'Web3\\Contracts\\Types\\Address' => $vendorDir . '/iexbase/web3.php/src/Contracts/Types/Address.php',
    'Web3\\Contracts\\Types\\Boolean' => $vendorDir . '/iexbase/web3.php/src/Contracts/Types/Boolean.php',
    'Web3\\Contracts\\Types\\Bytes' => $vendorDir . '/iexbase/web3.php/src/Contracts/Types/Bytes.php',
    'Web3\\Contracts\\Types\\DynamicBytes' => $vendorDir . '/iexbase/web3.php/src/Contracts/Types/DynamicBytes.php',
    'Web3\\Contracts\\Types\\IType' => $vendorDir . '/iexbase/web3.php/src/Contracts/Types/IType.php',
    'Web3\\Contracts\\Types\\Integer' => $vendorDir . '/iexbase/web3.php/src/Contracts/Types/Integer.php',
    'Web3\\Contracts\\Types\\Str' => $vendorDir . '/iexbase/web3.php/src/Contracts/Types/Str.php',
    'Web3\\Contracts\\Types\\Uinteger' => $vendorDir . '/iexbase/web3.php/src/Contracts/Types/Uinteger.php',
    'Web3\\Eth' => $vendorDir . '/iexbase/web3.php/src/Eth.php',
    'Web3\\Formatters\\AddressFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/AddressFormatter.php',
    'Web3\\Formatters\\BigNumberFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/BigNumberFormatter.php',
    'Web3\\Formatters\\BooleanFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/BooleanFormatter.php',
    'Web3\\Formatters\\HexFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/HexFormatter.php',
    'Web3\\Formatters\\IFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/IFormatter.php',
    'Web3\\Formatters\\IntegerFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/IntegerFormatter.php',
    'Web3\\Formatters\\NumberFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/NumberFormatter.php',
    'Web3\\Formatters\\OptionalQuantityFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/OptionalQuantityFormatter.php',
    'Web3\\Formatters\\PostFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/PostFormatter.php',
    'Web3\\Formatters\\QuantityFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/QuantityFormatter.php',
    'Web3\\Formatters\\StringFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/StringFormatter.php',
    'Web3\\Formatters\\TransactionFormatter' => $vendorDir . '/iexbase/web3.php/src/Formatters/TransactionFormatter.php',
    'Web3\\Methods\\EthMethod' => $vendorDir . '/iexbase/web3.php/src/Methods/EthMethod.php',
    'Web3\\Methods\\Eth\\Accounts' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/Accounts.php',
    'Web3\\Methods\\Eth\\BlockNumber' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/BlockNumber.php',
    'Web3\\Methods\\Eth\\Call' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/Call.php',
    'Web3\\Methods\\Eth\\Coinbase' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/Coinbase.php',
    'Web3\\Methods\\Eth\\CompileLLL' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/CompileLLL.php',
    'Web3\\Methods\\Eth\\CompileSerpent' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/CompileSerpent.php',
    'Web3\\Methods\\Eth\\CompileSolidity' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/CompileSolidity.php',
    'Web3\\Methods\\Eth\\EstimateGas' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/EstimateGas.php',
    'Web3\\Methods\\Eth\\GasPrice' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GasPrice.php',
    'Web3\\Methods\\Eth\\GetBalance' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetBalance.php',
    'Web3\\Methods\\Eth\\GetBlockByHash' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetBlockByHash.php',
    'Web3\\Methods\\Eth\\GetBlockByNumber' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetBlockByNumber.php',
    'Web3\\Methods\\Eth\\GetBlockTransactionCountByHash' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetBlockTransactionCountByHash.php',
    'Web3\\Methods\\Eth\\GetBlockTransactionCountByNumber' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetBlockTransactionCountByNumber.php',
    'Web3\\Methods\\Eth\\GetCode' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetCode.php',
    'Web3\\Methods\\Eth\\GetFilterChanges' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetFilterChanges.php',
    'Web3\\Methods\\Eth\\GetFilterLogs' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetFilterLogs.php',
    'Web3\\Methods\\Eth\\GetLogs' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetLogs.php',
    'Web3\\Methods\\Eth\\GetStorageAt' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetStorageAt.php',
    'Web3\\Methods\\Eth\\GetTransactionByBlockHashAndIndex' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetTransactionByBlockHashAndIndex.php',
    'Web3\\Methods\\Eth\\GetTransactionByBlockNumberAndIndex' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetTransactionByBlockNumberAndIndex.php',
    'Web3\\Methods\\Eth\\GetTransactionByHash' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetTransactionByHash.php',
    'Web3\\Methods\\Eth\\GetTransactionCount' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetTransactionCount.php',
    'Web3\\Methods\\Eth\\GetTransactionReceipt' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetTransactionReceipt.php',
    'Web3\\Methods\\Eth\\GetUncleByBlockHashAndIndex' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetUncleByBlockHashAndIndex.php',
    'Web3\\Methods\\Eth\\GetUncleByBlockNumberAndIndex' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetUncleByBlockNumberAndIndex.php',
    'Web3\\Methods\\Eth\\GetUncleCountByBlockHash' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetUncleCountByBlockHash.php',
    'Web3\\Methods\\Eth\\GetUncleCountByBlockNumber' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetUncleCountByBlockNumber.php',
    'Web3\\Methods\\Eth\\GetWork' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/GetWork.php',
    'Web3\\Methods\\Eth\\Hashrate' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/Hashrate.php',
    'Web3\\Methods\\Eth\\Mining' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/Mining.php',
    'Web3\\Methods\\Eth\\NewBlockFilter' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/NewBlockFilter.php',
    'Web3\\Methods\\Eth\\NewFilter' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/NewFilter.php',
    'Web3\\Methods\\Eth\\NewPendingTransactionFilter' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/NewPendingTransactionFilter.php',
    'Web3\\Methods\\Eth\\ProtocolVersion' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/ProtocolVersion.php',
    'Web3\\Methods\\Eth\\SendRawTransaction' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/SendRawTransaction.php',
    'Web3\\Methods\\Eth\\SendTransaction' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/SendTransaction.php',
    'Web3\\Methods\\Eth\\Sign' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/Sign.php',
    'Web3\\Methods\\Eth\\SubmitHashrate' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/SubmitHashrate.php',
    'Web3\\Methods\\Eth\\SubmitWork' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/SubmitWork.php',
    'Web3\\Methods\\Eth\\Syncing' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/Syncing.php',
    'Web3\\Methods\\Eth\\UninstallFilter' => $vendorDir . '/iexbase/web3.php/src/Methods/Eth/UninstallFilter.php',
    'Web3\\Methods\\IMethod' => $vendorDir . '/iexbase/web3.php/src/Methods/IMethod.php',
    'Web3\\Methods\\IRPC' => $vendorDir . '/iexbase/web3.php/src/Methods/IRPC.php',
    'Web3\\Methods\\JSONRPC' => $vendorDir . '/iexbase/web3.php/src/Methods/JSONRPC.php',
    'Web3\\Methods\\Net\\Listening' => $vendorDir . '/iexbase/web3.php/src/Methods/Net/Listening.php',
    'Web3\\Methods\\Net\\PeerCount' => $vendorDir . '/iexbase/web3.php/src/Methods/Net/PeerCount.php',
    'Web3\\Methods\\Net\\Version' => $vendorDir . '/iexbase/web3.php/src/Methods/Net/Version.php',
    'Web3\\Methods\\Personal\\ListAccounts' => $vendorDir . '/iexbase/web3.php/src/Methods/Personal/ListAccounts.php',
    'Web3\\Methods\\Personal\\LockAccount' => $vendorDir . '/iexbase/web3.php/src/Methods/Personal/LockAccount.php',
    'Web3\\Methods\\Personal\\NewAccount' => $vendorDir . '/iexbase/web3.php/src/Methods/Personal/NewAccount.php',
    'Web3\\Methods\\Personal\\SendTransaction' => $vendorDir . '/iexbase/web3.php/src/Methods/Personal/SendTransaction.php',
    'Web3\\Methods\\Personal\\UnlockAccount' => $vendorDir . '/iexbase/web3.php/src/Methods/Personal/UnlockAccount.php',
    'Web3\\Methods\\Shh\\AddToGroup' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/AddToGroup.php',
    'Web3\\Methods\\Shh\\GetFilterChanges' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/GetFilterChanges.php',
    'Web3\\Methods\\Shh\\GetMessages' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/GetMessages.php',
    'Web3\\Methods\\Shh\\HasIdentity' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/HasIdentity.php',
    'Web3\\Methods\\Shh\\NewFilter' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/NewFilter.php',
    'Web3\\Methods\\Shh\\NewGroup' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/NewGroup.php',
    'Web3\\Methods\\Shh\\NewIdentity' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/NewIdentity.php',
    'Web3\\Methods\\Shh\\Post' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/Post.php',
    'Web3\\Methods\\Shh\\UninstallFilter' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/UninstallFilter.php',
    'Web3\\Methods\\Shh\\Version' => $vendorDir . '/iexbase/web3.php/src/Methods/Shh/Version.php',
    'Web3\\Methods\\Web3\\ClientVersion' => $vendorDir . '/iexbase/web3.php/src/Methods/Web3/ClientVersion.php',
    'Web3\\Methods\\Web3\\Sha3' => $vendorDir . '/iexbase/web3.php/src/Methods/Web3/Sha3.php',
    'Web3\\Net' => $vendorDir . '/iexbase/web3.php/src/Net.php',
    'Web3\\Personal' => $vendorDir . '/iexbase/web3.php/src/Personal.php',
    'Web3\\Providers\\HttpProvider' => $vendorDir . '/iexbase/web3.php/src/Providers/HttpProvider.php',
    'Web3\\Providers\\IProvider' => $vendorDir . '/iexbase/web3.php/src/Providers/IProvider.php',
    'Web3\\Providers\\Provider' => $vendorDir . '/iexbase/web3.php/src/Providers/Provider.php',
    'Web3\\RequestManagers\\HttpRequestManager' => $vendorDir . '/iexbase/web3.php/src/RequestManagers/HttpRequestManager.php',
    'Web3\\RequestManagers\\IRequestManager' => $vendorDir . '/iexbase/web3.php/src/RequestManagers/IRequestManager.php',
    'Web3\\RequestManagers\\RequestManager' => $vendorDir . '/iexbase/web3.php/src/RequestManagers/RequestManager.php',
    'Web3\\Shh' => $vendorDir . '/iexbase/web3.php/src/Shh.php',
    'Web3\\Utils' => $vendorDir . '/iexbase/web3.php/src/Utils.php',
    'Web3\\Validators\\AddressValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/AddressValidator.php',
    'Web3\\Validators\\BlockHashValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/BlockHashValidator.php',
    'Web3\\Validators\\BooleanValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/BooleanValidator.php',
    'Web3\\Validators\\CallValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/CallValidator.php',
    'Web3\\Validators\\FilterValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/FilterValidator.php',
    'Web3\\Validators\\HexValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/HexValidator.php',
    'Web3\\Validators\\IValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/IValidator.php',
    'Web3\\Validators\\IdentityValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/IdentityValidator.php',
    'Web3\\Validators\\NonceValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/NonceValidator.php',
    'Web3\\Validators\\PostValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/PostValidator.php',
    'Web3\\Validators\\QuantityValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/QuantityValidator.php',
    'Web3\\Validators\\ShhFilterValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/ShhFilterValidator.php',
    'Web3\\Validators\\StringValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/StringValidator.php',
    'Web3\\Validators\\TagValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/TagValidator.php',
    'Web3\\Validators\\TransactionValidator' => $vendorDir . '/iexbase/web3.php/src/Validators/TransactionValidator.php',
    'Web3\\Web3' => $vendorDir . '/iexbase/web3.php/src/Web3.php',
    'kornrunner\\Keccak' => $vendorDir . '/kornrunner/keccak/src/Keccak.php',
    'kornrunner\\Secp256k1' => $vendorDir . '/kornrunner/secp256k1/src/Secp256k1.php',
    'kornrunner\\Serializer\\HexPrivateKeySerializer' => $vendorDir . '/kornrunner/secp256k1/src/Serializer/HexPrivateKeySerializer.php',
    'kornrunner\\Serializer\\HexSignatureSerializer' => $vendorDir . '/kornrunner/secp256k1/src/Serializer/HexSignatureSerializer.php',
    'kornrunner\\Signature\\Signature' => $vendorDir . '/kornrunner/secp256k1/src/Signature/Signature.php',
    'kornrunner\\Signature\\SignatureInterface' => $vendorDir . '/kornrunner/secp256k1/src/Signature/SignatureInterface.php',
    'kornrunner\\Signature\\Signer' => $vendorDir . '/kornrunner/secp256k1/src/Signature/Signer.php',
    'phpseclib\\Crypt\\AES' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/AES.php',
    'phpseclib\\Crypt\\Base' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/Base.php',
    'phpseclib\\Crypt\\Blowfish' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/Blowfish.php',
    'phpseclib\\Crypt\\DES' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/DES.php',
    'phpseclib\\Crypt\\Hash' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/Hash.php',
    'phpseclib\\Crypt\\RC2' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/RC2.php',
    'phpseclib\\Crypt\\RC4' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/RC4.php',
    'phpseclib\\Crypt\\RSA' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/RSA.php',
    'phpseclib\\Crypt\\Random' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/Random.php',
    'phpseclib\\Crypt\\Rijndael' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/Rijndael.php',
    'phpseclib\\Crypt\\TripleDES' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/TripleDES.php',
    'phpseclib\\Crypt\\Twofish' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Crypt/Twofish.php',
    'phpseclib\\File\\ANSI' => $vendorDir . '/phpseclib/phpseclib/phpseclib/File/ANSI.php',
    'phpseclib\\File\\ASN1' => $vendorDir . '/phpseclib/phpseclib/phpseclib/File/ASN1.php',
    'phpseclib\\File\\ASN1\\Element' => $vendorDir . '/phpseclib/phpseclib/phpseclib/File/ASN1/Element.php',
    'phpseclib\\File\\X509' => $vendorDir . '/phpseclib/phpseclib/phpseclib/File/X509.php',
    'phpseclib\\Math\\BigInteger' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Math/BigInteger.php',
    'phpseclib\\Net\\SCP' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Net/SCP.php',
    'phpseclib\\Net\\SFTP' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Net/SFTP.php',
    'phpseclib\\Net\\SFTP\\Stream' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Net/SFTP/Stream.php',
    'phpseclib\\Net\\SSH1' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Net/SSH1.php',
    'phpseclib\\Net\\SSH2' => $vendorDir . '/phpseclib/phpseclib/phpseclib/Net/SSH2.php',
    'phpseclib\\System\\SSH\\Agent' => $vendorDir . '/phpseclib/phpseclib/phpseclib/System/SSH/Agent.php',
    'phpseclib\\System\\SSH\\Agent\\Identity' => $vendorDir . '/phpseclib/phpseclib/phpseclib/System/SSH/Agent/Identity.php',
);
