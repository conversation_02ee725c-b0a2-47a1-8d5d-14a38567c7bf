<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Success Modal Implementation</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f8f9fa;
        }
        .test-container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { 
            padding: 10px 15px; 
            margin: 5px; 
            cursor: pointer; 
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        .log { 
            white-space: pre-wrap; 
            font-family: monospace; 
            background-color: #f8f9fa; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Success Modal Implementation Test</h1>
        <p>Testing the new success modal and balance update functionality for investment creation.</p>
        
        <div class="test-section info">
            <h2>Test Configuration</h2>
            <p><strong>Frontend URL:</strong> http://localhost:8080</p>
            <p><strong>Test User:</strong> <EMAIL></p>
            <p><strong>Test Investment:</strong> 630 USDT (Basic Plan)</p>
        </div>

        <div class="test-section">
            <h2>Step 1: Authentication Test</h2>
            <button onclick="testAuthentication()">Test Login & Authentication</button>
            <div id="authResults" class="log"></div>
        </div>

        <div class="test-section">
            <h2>Step 2: Pre-Investment Balance Check</h2>
            <button onclick="checkInitialBalance()" disabled id="balanceBtn">Check Current Balance</button>
            <div id="balanceResults" class="log"></div>
        </div>

        <div class="test-section">
            <h2>Step 3: Investment Creation with Modal Test</h2>
            <button onclick="testInvestmentWithModal()" disabled id="investBtn">Create Test Investment</button>
            <div id="investResults" class="log"></div>
        </div>

        <div class="test-section">
            <h2>Step 4: Post-Investment Balance Verification</h2>
            <button onclick="verifyBalanceUpdate()" disabled id="verifyBtn">Verify Balance Update</button>
            <div id="verifyResults" class="log"></div>
        </div>

        <div class="test-section">
            <h2>Step 5: Frontend Modal UI Test</h2>
            <button onclick="testFrontendModal()" class="btn-warning">Open Frontend Test Page</button>
            <div id="frontendResults" class="log"></div>
        </div>

        <div class="test-section success" id="summarySection" style="display: none;">
            <h2>📊 Test Summary</h2>
            <div id="testSummary"></div>
        </div>
    </div>

    <script>
        let testResults = {
            auth: false,
            initialBalance: null,
            investment: false,
            balanceUpdate: false,
            modal: false
        };

        let authCookies = '';

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            element.textContent += `[${timestamp}] ${statusIcon} ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        async function testAuthentication() {
            log('authResults', 'Starting authentication test...');
            
            try {
                const response = await fetch('http://localhost:8080/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    testResults.auth = true;
                    log('authResults', 'Authentication successful!', 'success');
                    log('authResults', `User ID: ${result.user?.id || 'Not provided'}`);
                    log('authResults', `Token present: ${result.token ? 'Yes' : 'No'}`);
                    
                    // Enable next test
                    document.getElementById('balanceBtn').disabled = false;
                } else {
                    log('authResults', `Authentication failed: ${result.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                log('authResults', `Authentication error: ${error.message}`, 'error');
            }
        }

        async function checkInitialBalance() {
            log('balanceResults', 'Checking initial balance...');
            
            try {
                const response = await fetch('http://localhost:8080/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        action: 'get_balance'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    testResults.initialBalance = parseFloat(result.balance);
                    log('balanceResults', `Initial balance: ${result.balance} USDT`, 'success');
                    log('balanceResults', `Balance sufficient for 630 USDT investment: ${testResults.initialBalance >= 630 ? 'Yes' : 'No'}`);
                    
                    // Enable investment test
                    document.getElementById('investBtn').disabled = false;
                } else {
                    log('balanceResults', `Balance check failed: ${result.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                log('balanceResults', `Balance check error: ${error.message}`, 'error');
            }
        }

        async function testInvestmentWithModal() {
            log('investResults', 'Testing investment creation (this should trigger success modal)...');
            
            try {
                const response = await fetch('http://localhost:8080/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        action: 'create_investment',
                        plan: 'basic',
                        amount: 630
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    testResults.investment = true;
                    log('investResults', 'Investment created successfully!', 'success');
                    log('investResults', `Investment ID: ${result.investment_id || 'Not provided'}`);
                    log('investResults', `Message: ${result.message || 'Not provided'}`);
                    log('investResults', 'Note: Success modal should appear on the actual frontend page');
                    
                    // Enable balance verification
                    document.getElementById('verifyBtn').disabled = false;
                } else {
                    log('investResults', `Investment failed: ${result.error || result.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                log('investResults', `Investment error: ${error.message}`, 'error');
            }
        }

        async function verifyBalanceUpdate() {
            log('verifyResults', 'Verifying balance after investment...');
            
            try {
                const response = await fetch('http://localhost:8080/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        action: 'get_balance'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    const newBalance = parseFloat(result.balance);
                    const expectedBalance = testResults.initialBalance - 630;
                    const difference = Math.abs(newBalance - expectedBalance);
                    
                    log('verifyResults', `New balance: ${result.balance} USDT`);
                    log('verifyResults', `Expected balance: ${expectedBalance.toFixed(6)} USDT`);
                    log('verifyResults', `Difference: ${difference.toFixed(6)} USDT`);
                    
                    if (difference < 0.01) { // Allow small floating point differences
                        testResults.balanceUpdate = true;
                        log('verifyResults', 'Balance updated correctly! ✅', 'success');
                    } else {
                        log('verifyResults', 'Balance update mismatch! ❌', 'error');
                    }
                    
                    showTestSummary();
                } else {
                    log('verifyResults', `Balance verification failed: ${result.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                log('verifyResults', `Balance verification error: ${error.message}`, 'error');
            }
        }

        function testFrontendModal() {
            log('frontendResults', 'Opening frontend test page...');
            log('frontendResults', 'This will open the actual make_investment.php page where you can test the modal');
            log('frontendResults', 'Steps to test:');
            log('frontendResults', '1. Select Basic Plan');
            log('frontendResults', '2. Enter amount (e.g., 600)');
            log('frontendResults', '3. Click Calculate Returns');
            log('frontendResults', '4. Click Confirm Investment');
            log('frontendResults', '5. Verify success modal appears');
            log('frontendResults', '6. Check balance updates in real-time');
            
            // Open the actual frontend page
            window.open('http://localhost:8080/user/make_investment.php', '_blank');
        }

        function showTestSummary() {
            const summarySection = document.getElementById('summarySection');
            const summaryDiv = document.getElementById('testSummary');
            
            let summary = '';
            summary += `<div><span class="status-indicator ${testResults.auth ? 'status-success' : 'status-error'}"></span>Authentication: ${testResults.auth ? 'PASS' : 'FAIL'}</div>`;
            summary += `<div><span class="status-indicator status-success"></span>Initial Balance: ${testResults.initialBalance || 'N/A'} USDT</div>`;
            summary += `<div><span class="status-indicator ${testResults.investment ? 'status-success' : 'status-error'}"></span>Investment Creation: ${testResults.investment ? 'PASS' : 'FAIL'}</div>`;
            summary += `<div><span class="status-indicator ${testResults.balanceUpdate ? 'status-success' : 'status-error'}"></span>Balance Update: ${testResults.balanceUpdate ? 'PASS' : 'FAIL'}</div>`;
            
            const allPassed = testResults.auth && testResults.investment && testResults.balanceUpdate;
            summary += `<hr><div><strong>Overall Result: ${allPassed ? '✅ SUCCESS' : '❌ NEEDS ATTENTION'}</strong></div>`;
            
            if (allPassed) {
                summary += `<div style="margin-top: 10px; color: #28a745;">🎉 All backend tests passed! Now test the frontend modal UI manually.</div>`;
            }
            
            summaryDiv.innerHTML = summary;
            summarySection.style.display = 'block';
        }

        // Auto-start first test
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.querySelector('button').focus();
            }, 500);
        });
    </script>
</body>
</html>
