<?php
/**
 * Debug the exact balance calculation that the backend is seeing
 * This will help us understand why 630 balance shows as "insufficient" for 600 minimum
 */

echo "=== DEBUGGING INSUFFICIENT BALANCE ISSUE ===\n\n";

// Test Configuration
$frontendUrl = 'http://localhost:8080';
$testEmail = '<EMAIL>';
$testPassword = 'password123';

// Cookie jar to maintain session across requests
$cookieJar = tempnam(sys_get_temp_dir(), 'debug_cookies');

// Function to make HTTP requests with session maintenance
function makeRequest($url, $data = null, $cookieJar = null, $method = 'GET') {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if ($cookieJar) {
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieJar);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieJar);
    }
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error
    ];
}

// Step 1: Login
echo "STEP 1: Login\n";
echo "=============\n";

$loginData = json_encode([
    'action' => 'login',
    'email' => $testEmail,
    'password' => $testPassword
]);

$loginResult = makeRequest("$frontendUrl/frontend/ajax.php", $loginData, $cookieJar, 'POST');
$loginResponse = json_decode($loginResult['response'], true);

if (!isset($loginResponse['success']) || !$loginResponse['success']) {
    echo "❌ Login failed: " . ($loginResponse['error'] ?? 'Unknown error') . "\n";
    exit(1);
}

echo "✅ Login successful\n\n";

// Step 2: Check balance via frontend get_balance API
echo "STEP 2: Check Balance via Frontend API\n";
echo "======================================\n";

$balanceData = json_encode(['action' => 'get_balance']);
$balanceResult = makeRequest("$frontendUrl/frontend/ajax.php", $balanceData, $cookieJar, 'POST');
$balanceResponse = json_decode($balanceResult['response'], true);

echo "Balance API Response:\n";
echo json_encode($balanceResponse, JSON_PRETTY_PRINT) . "\n\n";

if (isset($balanceResponse['success']) && $balanceResponse['success']) {
    $frontendBalance = floatval($balanceResponse['balance']);
    echo "✅ Frontend sees balance: $frontendBalance USDT\n\n";
} else {
    echo "❌ Balance API failed: " . ($balanceResponse['error'] ?? 'Unknown error') . "\n\n";
}

// Step 3: Try investment with different amounts to see threshold
echo "STEP 3: Testing Investment Amounts\n";
echo "==================================\n";

$testAmounts = [500, 600, 630, 650];

foreach ($testAmounts as $amount) {
    echo "Testing amount: $amount\n";
    
    $investmentData = json_encode([
        'action' => 'create_investment',
        'plan' => 'basic',
        'amount' => $amount
    ]);
    
    $investmentResult = makeRequest("$frontendUrl/frontend/ajax.php", $investmentData, $cookieJar, 'POST');
    $investmentResponse = json_decode($investmentResult['response'], true);
    
    echo "  Response: " . ($investmentResponse['message'] ?? $investmentResponse['error'] ?? 'Unknown') . "\n";
    
    if (isset($investmentResponse['success']) && $investmentResponse['success']) {
        echo "  ✅ SUCCESS!\n";
        break;
    }
    
    echo "\n";
}

// Step 4: Check if there's a currency conversion issue by checking database directly
echo "\nSTEP 4: Direct Database Check\n";
echo "=============================\n";

// Include backend database connection
require_once 'backend/vendor/autoload.php';

try {
    $pdo = \Simbi\Tls\Config\Database::getConnection();
    
    // Get user wallet balance directly from database
    $stmt = $pdo->prepare('SELECT * FROM wallets WHERE user_id = ?');
    $stmt->execute([2]); // Test user ID
    $wallet = $stmt->fetch();
    
    if ($wallet) {
        echo "Database wallet balance: {$wallet['balance']}\n";
        echo "Wallet currency implied: USDT (based on schema)\n";
    } else {
        echo "❌ No wallet found for user\n";
    }
    
    // Get investment plan requirements directly from database
    $stmt = $pdo->prepare("SELECT * FROM investment_plans WHERE plan_code = 'basic'");
    $stmt->execute();
    $plan = $stmt->fetch();
    
    if ($plan) {
        echo "Database plan minimum: {$plan['min_amount']}\n";
        echo "Plan active: {$plan['is_active']}\n";
        
        // Calculate the exact comparison that backend is doing
        $dbBalance = floatval($wallet['balance']);
        $dbMinAmount = floatval($plan['min_amount']);
        
        echo "\nBackend Validation Logic:\n";
        echo "User balance: $dbBalance\n";
        echo "Plan minimum: $dbMinAmount\n";
        echo "Test amount: 630\n";
        echo "Balance >= 630? " . ($dbBalance >= 630 ? 'YES' : 'NO') . "\n";
        echo "630 >= minimum? " . (630 >= $dbMinAmount ? 'YES' : 'NO') . "\n";
        
        if ($dbBalance >= 630 && 630 >= $dbMinAmount) {
            echo "✅ Should be valid according to database values\n";
        } else {
            echo "❌ Invalid according to database values\n";
            echo "Possible causes:\n";
            echo "- Balance: $dbBalance < required: 630\n";
            echo "- Amount: 630 < minimum: $dbMinAmount\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Database check failed: " . $e->getMessage() . "\n";
}

// Cleanup
if (file_exists($cookieJar)) {
    unlink($cookieJar);
}

echo "\n=== DEBUG COMPLETE ===\n";

?>
