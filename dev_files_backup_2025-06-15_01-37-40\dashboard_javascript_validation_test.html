<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard JavaScript Validation Test</title>
    <link rel="stylesheet" href="frontend/user/css/dashboard.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f7fa;
            color: #333;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .status {
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 600;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .console-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .dashboard-mockup {
            background: white;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 1.5rem;
        }
        .stat-card p {
            margin: 0;
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Dashboard JavaScript Validation Test</h1>
            <p>Testing the fixed dashboard-simple.js with enhanced Active Investments styling</p>
        </div>

        <div class="test-section">
            <h3>📋 Test Dashboard Elements</h3>
            <p>Mock dashboard elements that the JavaScript expects to find:</p>
            
            <div class="dashboard-mockup">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3 id="totalBalance">Loading...</h3>
                        <p>Total Balance</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="totalTransactions">Loading...</h3>
                        <p>Total Transactions</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="totalDeposits">Loading...</h3>
                        <p>Total Deposits</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="totalInvested">Loading...</h3>
                        <p>Total Invested</p>
                    </div>
                </div>
                
                <div style="margin: 20px 0;">
                    <h4>Active Investments</h4>
                    <div id="activeInvestmentsList">Loading...</div>
                </div>
                
                <div style="margin: 20px 0;">
                    <h4>Recent Transactions</h4>
                    <div id="recentTransactionsList">Loading...</div>
                </div>
                
                <div id="message" style="display: none;"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Test Controls</h3>
            <button class="btn" onclick="testJavaScriptLoad()">Load Dashboard JavaScript</button>
            <button class="btn success" onclick="testActiveInvestments()">Test Active Investments</button>
            <button class="btn" onclick="simulateAPIResponses()">Simulate API Responses</button>
            <button class="btn danger" onclick="clearConsole()">Clear Console</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="testResults">
                <div class="status info">Ready to run tests...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🖥️ Console Output</h3>
            <div class="console-output" id="consoleOutput">Console ready...\n</div>
        </div>
    </div>

    <script>
        // Capture console output
        const consoleOutput = document.getElementById('consoleOutput');
        const originalLog = console.log;
        const originalError = console.error;
        
        function logToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌ ERROR' : type === 'warn' ? '⚠️ WARN' : '📝 LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole(args.join(' '), 'error');
        };

        // Mock API responses
        const mockAPIResponses = {
            get_balance: {
                success: true,
                balance_formatted: '5.00'
            },
            get_transaction_statistics: {
                success: true,
                statistics: {
                    total_transactions: 5,
                    total_deposits: '0.000000'
                }
            },
            get_investment_statistics: {
                success: true,
                statistics: {
                    total_invested: 0
                }
            },
            get_transactions: {
                success: true,
                transactions: [
                    {
                        type: 'deposit',
                        amount_formatted: '5.00',
                        created_at: new Date().toISOString()
                    }
                ]
            },
            get_active_investments: {
                success: true,
                investments: [
                    {
                        id: 1,
                        plan_name: 'Basic Plan',
                        amount: '1500.00',
                        daily_return: '75.00',
                        total_earned: '450.00',
                        days_elapsed: 6,
                        duration: 30
                    },
                    {
                        id: 2,
                        plan_name: 'Premium Plan',
                        amount: '3000.00',
                        daily_return: '210.00',
                        total_earned: '840.00',
                        days_elapsed: 4,
                        duration: 30
                    }
                ]
            }
        };

        // Mock API call function
        async function apiCall(endpoint, data = null) {
            console.log(`API Call: ${endpoint}`, data);
            
            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 100));
            
            if (mockAPIResponses[endpoint]) {
                console.log(`API Response: ${endpoint}`, mockAPIResponses[endpoint]);
                return mockAPIResponses[endpoint];
            } else {
                console.error(`Unknown API endpoint: ${endpoint}`);
                return { success: false, error: 'Unknown endpoint' };
            }
        }

        function updateTestResults(message, type = 'info') {
            const results = document.getElementById('testResults');
            const statusClass = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            results.innerHTML += `<div class="status ${statusClass}">${message}</div>`;
        }

        function clearConsole() {
            consoleOutput.textContent = 'Console cleared...\n';
        }

        function testJavaScriptLoad() {
            console.log('Testing JavaScript loading...');
            updateTestResults('JavaScript load test started', 'info');
            
            try {
                // Test element access (the fixed version)
                const totalBalance = document.getElementById('totalBalance');
                if (totalBalance) {
                    totalBalance.textContent = '5.00';
                    console.log('✅ totalBalance access successful');
                } else {
                    console.error('❌ totalBalance element not found');
                }

                const totalTransactions = document.getElementById('totalTransactions');
                if (totalTransactions) {
                    totalTransactions.textContent = '5';
                    console.log('✅ totalTransactions access successful');
                } else {
                    console.error('❌ totalTransactions element not found');
                }

                const totalDeposits = document.getElementById('totalDeposits');
                if (totalDeposits) {
                    totalDeposits.textContent = '0.000000';
                    console.log('✅ totalDeposits access successful');
                } else {
                    console.error('❌ totalDeposits element not found');
                }

                const totalInvested = document.getElementById('totalInvested');
                if (totalInvested) {
                    totalInvested.textContent = '0 USDT';
                    console.log('✅ totalInvested access successful');
                } else {
                    console.error('❌ totalInvested element not found');
                }

                updateTestResults('All element access tests passed! No TypeError occurred.', 'success');
                
            } catch (error) {
                console.error('JavaScript test failed:', error.message);
                updateTestResults(`JavaScript test failed: ${error.message}`, 'error');
            }
        }

        async function simulateAPIResponses() {
            console.log('Simulating dashboard data loading...');
            updateTestResults('Simulating API responses...', 'info');
            
            try {
                // Simulate loadDashboardData function
                console.log('Loading balance...');
                const balanceResponse = await apiCall('get_balance');
                const totalBalanceElement = document.getElementById('totalBalance');
                if (totalBalanceElement) {
                    if (balanceResponse.success) {
                        totalBalanceElement.textContent = balanceResponse.balance_formatted || '0.000000';
                    } else {
                        totalBalanceElement.textContent = '0.000000';
                    }
                }

                console.log('Loading transaction statistics...');
                const statsResponse = await apiCall('get_transaction_statistics');
                if (statsResponse.success) {
                    const stats = statsResponse.statistics;
                    const totalTransactionsElement = document.getElementById('totalTransactions');
                    const totalDepositsElement = document.getElementById('totalDeposits');
                    
                    if (totalTransactionsElement) {
                        totalTransactionsElement.textContent = stats.total_transactions || '0';
                    }
                    if (totalDepositsElement) {
                        totalDepositsElement.textContent = stats.total_deposits || '0';
                    }
                }

                console.log('Loading investment statistics...');
                const investmentResponse = await apiCall('get_investment_statistics');
                if (investmentResponse.success) {
                    const stats = investmentResponse.statistics;
                    const totalInvestedElement = document.getElementById('totalInvested');
                    if (totalInvestedElement) {
                        totalInvestedElement.textContent = `${parseFloat(stats.total_invested || 0).toFixed(0)} USDT`;
                    }
                }

                updateTestResults('API simulation completed successfully!', 'success');
                
            } catch (error) {
                console.error('API simulation failed:', error.message);
                updateTestResults(`API simulation failed: ${error.message}`, 'error');
            }
        }

        async function testActiveInvestments() {
            console.log('Testing Active Investments functionality...');
            updateTestResults('Testing Active Investments...', 'info');
            
            try {
                const response = await apiCall('get_active_investments');
                const container = document.getElementById('activeInvestmentsList');
                
                if (!container) {
                    console.error('activeInvestmentsList container not found');
                    return;
                }
                
                // Show enhanced loading state
                container.innerHTML = `
                    <div class="loading-investments">
                        <div class="loading-spinner-container">
                            <div class="loading-spinner-ring"></div>
                        </div>
                        <p class="loading-text">Loading your investments<span class="loading-dots"></span></p>
                    </div>
                `;
                
                // Simulate delay
                await new Promise(resolve => setTimeout(resolve, 500));
                
                if (response.success && response.investments && response.investments.length > 0) {
                    const totalInvested = response.investments.reduce((sum, inv) => sum + parseFloat(inv.amount), 0);
                    const totalEarned = response.investments.reduce((sum, inv) => sum + parseFloat(inv.total_earned), 0);
                    const dailyEarnings = response.investments.reduce((sum, inv) => sum + parseFloat(inv.daily_return), 0);
                    
                    container.innerHTML = `
                        <div class="investment-summary-card">
                            <h4 style="margin: 0 0 16px 0; font-size: 1.1rem; font-weight: 600;">💼 Portfolio Overview</h4>
                            <div class="summary-stats">
                                <div class="summary-stat">
                                    <div class="summary-stat-value">${response.investments.length}</div>
                                    <div class="summary-stat-label">Active</div>
                                </div>
                                <div class="summary-stat">
                                    <div class="summary-stat-value">${totalInvested.toFixed(2)}</div>
                                    <div class="summary-stat-label">Invested</div>
                                </div>
                                <div class="summary-stat">
                                    <div class="summary-stat-value">+${dailyEarnings.toFixed(2)}</div>
                                    <div class="summary-stat-label">Daily</div>
                                </div>
                                <div class="summary-stat">
                                    <div class="summary-stat-value">${totalEarned.toFixed(2)}</div>
                                    <div class="summary-stat-label">Total Earned</div>
                                </div>
                            </div>
                        </div>
                        <div class="investment-grid">
                            ${response.investments.slice(0, 2).map((investment, index) => {
                                const progressPercentage = Math.round((investment.days_elapsed / investment.duration) * 100);
                                const isCompleted = investment.days_elapsed >= investment.duration;
                                const statusClass = isCompleted ? 'completed' : 'active';
                                const daysRemaining = Math.max(0, investment.duration - investment.days_elapsed);
                                
                                return `
                                    <div class="investment-item ${index === 0 ? 'pulse' : ''}" data-investment-id="${investment.id}">
                                        <div class="investment-status ${statusClass}">
                                            ${isCompleted ? '✅ Done' : '🔄 Active'}
                                        </div>
                                        <div class="investment-info">
                                            <div class="investment-details">
                                                <div class="investment-plan">${investment.plan_name}</div>
                                                <div class="investment-amount">${parseFloat(investment.amount).toFixed(2)} USDT</div>
                                                <div class="performance-indicator positive">
                                                    Performing Well
                                                </div>
                                            </div>
                                            <div class="investment-earnings">
                                                <div class="daily-return">+${parseFloat(investment.daily_return).toFixed(2)} USDT/day</div>
                                                <div class="total-earned">${parseFloat(investment.total_earned).toFixed(2)} USDT earned</div>
                                            </div>
                                        </div>
                                        <div class="investment-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                                            </div>
                                            <div class="progress-text" data-percentage="${progressPercentage}%">
                                                <span>Day ${investment.days_elapsed}/${investment.duration}</span>
                                                <span style="font-size: 0.8rem; color: #6c757d;">
                                                    ${isCompleted ? 'Complete!' : `${daysRemaining} days left`}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    `;
                    
                    // Add entrance animation
                    setTimeout(() => {
                        const investmentItems = container.querySelectorAll('.investment-item');
                        investmentItems.forEach((item, index) => {
                            setTimeout(() => {
                                item.classList.add('new-investment');
                            }, index * 100);
                        });
                    }, 100);
                    
                    console.log('✅ Active Investments rendered successfully with enhanced styling');
                    updateTestResults('Active Investments styled successfully!', 'success');
                    
                } else {
                    container.innerHTML = `
                        <div class="no-data">
                            <h4>No Active Investments</h4>
                            <p>Start investing today to see your portfolio here!</p>
                        </div>
                    `;
                    console.log('✅ Empty state rendered successfully');
                    updateTestResults('Empty state rendered successfully', 'success');
                }
                
            } catch (error) {
                console.error('Active Investments test failed:', error.message);
                updateTestResults(`Active Investments test failed: ${error.message}`, 'error');
            }
        }

        // Auto-run initial test
        window.addEventListener('load', function() {
            console.log('Dashboard JavaScript Test Page Loaded');
            updateTestResults('Test page loaded successfully', 'success');
        });
    </script>
</body>
</html>
