<?php
// Test backend investment creation endpoint directly
echo "Testing Backend Investment Creation Endpoint\n";
echo "==========================================\n\n";

// Test data
$testData = [
    'amount' => 700,
    'plan' => 'basic'
];

echo "Sending data to backend: " . json_encode($testData) . "\n\n";

// Test the backend endpoint
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/invest');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer test_token'  // This will fail auth, but we can see if the endpoint responds
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Status: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: $response\n\n";

// Parse the response
$responseData = json_decode($response, true);
if ($responseData) {
    echo "Parsed Response:\n";
    print_r($responseData);
} else {
    echo "Failed to parse JSON response\n";
    
    // Check if the response contains HTML (PHP error)
    if (strpos($response, '<') !== false) {
        echo "Response appears to contain HTML/PHP error page\n";
        echo "First 500 characters:\n";
        echo substr($response, 0, 500) . "\n";
    }
}

echo "\nTest complete!\n";
?>
