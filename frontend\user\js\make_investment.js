﻿// Make Investment Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initMakeInvestmentPage();
});

let userBalance = 0;
let selectedPlan = 'basic';
let investmentPlans = {}; // Will be loaded from database

function initMakeInvestmentPage() {
    loadInvestmentPlans();
    loadUserBalance();
    loadActiveInvestments();
    loadInvestmentHistory();
    initFooterMenu();
    initSuccessModalHandlers();
}

function setupInvestmentForm() {
    const investForm = document.getElementById('investmentForm');
    const calculateBtn = document.getElementById('calculateBtn');
    const investAmountInput = document.getElementById('investAmount');
    
    // Set up plan selection after a short delay to ensure DOM is ready
    setTimeout(() => {
        const planCards = document.querySelectorAll('.plan-card:not(.disabled)');
          // Plan selection (only for enabled plans)
        if (planCards.length > 0) {
            planCards.forEach(card => {
                card.addEventListener('click', function() {
                    if (this.classList.contains('disabled')) return;
                    
                    // Remove active class from all cards
                    planCards.forEach(c => c.classList.remove('active'));
                    // Add active class to clicked card
                    this.classList.add('active');
                    
                    selectedPlan = this.dataset.plan;
                    const selectedPlanInput = document.getElementById('selectedPlan');
                    if (selectedPlanInput) {
                        selectedPlanInput.value = selectedPlan;
                    }
                    
                    // Show the investment form
                    showInvestmentForm(selectedPlan);
                    
                    // Update minimum amount
                    if (investmentPlans[selectedPlan]) {
                        const minAmount = investmentPlans[selectedPlan].minAmount;
                        if (investAmountInput) {
                            investAmountInput.min = minAmount;
                            investAmountInput.placeholder = `Enter amount (minimum ${minAmount} USDT)`;
                        }
                    }
                    
                    // Clear previous calculations
                    const investmentSummary = document.getElementById('investmentSummary');
                    const investBtn = document.getElementById('investBtn');
                    if (investmentSummary) investmentSummary.style.display = 'none';
                    if (investBtn) investBtn.disabled = true;
                });
            });

            // Set default selected plan to basic
            const basicPlanCard = document.querySelector('[data-plan="basic"]:not(.disabled)');
            if (basicPlanCard) {
                basicPlanCard.classList.add('active');
                selectedPlan = 'basic';
            }
        }
    }, 100);

    // Calculate button
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateReturns);
    }    // Form submission
    if (investForm) {
        investForm.addEventListener('submit', handleInvestment);
    }    // Amount input validation
    if (investAmountInput) {
        investAmountInput.addEventListener('input', function() {
            const investmentSummary = document.getElementById('investmentSummary');
            const investBtn = document.getElementById('investBtn');
            
            // Hide summary and disable button when user changes amount
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (investBtn) investBtn.disabled = true;
            
            validateAmount();
        });
    }

    // Initialize modal handlers
    initModalHandlers();
}

function showInvestmentForm(planCode) {
    const formCard = document.getElementById('investmentFormCard');
    const plan = investmentPlans[planCode];
    
    if (!formCard || !plan) return;
    
    // Update plan summary in form
    const planNameElement = document.getElementById('selectedPlanName');
    const planRateElement = document.getElementById('selectedPlanRate');
    const planDurationElement = document.getElementById('selectedPlanDuration');
    const planMinimumElement = document.getElementById('selectedPlanMinimum');
    const amountHelpElement = document.getElementById('amountHelp');
    
    if (planNameElement) planNameElement.textContent = plan.name;
    if (planRateElement) planRateElement.textContent = `${(plan.dailyRate * 100).toFixed(1)}% Daily`;
    if (planDurationElement) planDurationElement.textContent = `${plan.duration} days`;
    if (planMinimumElement) planMinimumElement.textContent = `${plan.minAmount} USDT`;
    if (amountHelpElement) amountHelpElement.textContent = `Minimum investment: ${plan.minAmount} USDT`;
    
    // Update balance in form
    const formBalanceElement = document.getElementById('formUserBalance');
    if (formBalanceElement) {
        formBalanceElement.textContent = userBalance.toFixed(2);
    }
    
    // Update balance status
    updateBalanceStatus();
    
    // Show the form with animation
    formCard.style.display = 'block';
    
    // Scroll to form
    setTimeout(() => {
        formCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 100);
}

function updateBalanceStatus() {
    const balanceStatusElement = document.getElementById('balanceStatus');
    const plan = investmentPlans[selectedPlan];
    
    if (!balanceStatusElement || !plan) return;
    
    if (userBalance >= plan.minAmount) {
        balanceStatusElement.textContent = '✅ Sufficient balance for investment';
        balanceStatusElement.className = 'balance-status sufficient';
    } else {
        const needed = plan.minAmount - userBalance;
        balanceStatusElement.textContent = `❌ Need ${needed.toFixed(2)} USDT more`;
        balanceStatusElement.className = 'balance-status insufficient';
    }
}

function validateAmount() {
    const amountInput = document.getElementById('investAmount');
    const validationElement = document.getElementById('amountValidation');
    const plan = investmentPlans[selectedPlan];
    
    if (!amountInput || !validationElement || !plan) return;
    
    const amount = parseFloat(amountInput.value);
    
    if (!amount || isNaN(amount)) {
        validationElement.style.display = 'none';
        return;
    }
    
    if (amount < plan.minAmount) {
        validationElement.textContent = `Amount must be at least ${plan.minAmount} USDT`;
        validationElement.className = 'amount-validation invalid';
    } else if (amount > userBalance) {
        validationElement.textContent = `Insufficient balance. You need ${(amount - userBalance).toFixed(2)} USDT more.`;
        validationElement.className = 'amount-validation invalid';
    } else {
        validationElement.textContent = `✅ Valid amount. You can proceed with this investment.`;
        validationElement.className = 'amount-validation valid';
    }
}

function initModalHandlers() {
    // Low funds modal handlers
    const closeLowFundsModal = document.getElementById('closeLowFundsModal');
    const addFundsBtn = document.getElementById('addFundsBtn');
    const cancelModalBtn = document.getElementById('cancelModalBtn');
    const lowFundsModal = document.getElementById('lowFundsModal');
    
    if (closeLowFundsModal) {
        closeLowFundsModal.addEventListener('click', hideLowFundsModal);
    }
    
    if (cancelModalBtn) {
        cancelModalBtn.addEventListener('click', hideLowFundsModal);
    }
    
    if (addFundsBtn) {
        addFundsBtn.addEventListener('click', function() {
            window.location.href = 'deposit.php';
        });
    }
    
    // Close modal when clicking outside
    if (lowFundsModal) {
        lowFundsModal.addEventListener('click', function(e) {
            if (e.target === lowFundsModal) {
                hideLowFundsModal();
            }
        });
    }
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideLowFundsModal();
        }
    });
}

function showLowFundsModal(requiredAmount) {
    const modal = document.getElementById('lowFundsModal');
    const modalRequiredAmount = document.getElementById('modalRequiredAmount');
    const modalUserBalance = document.getElementById('modalUserBalance');
    const modalDeficitAmount = document.getElementById('modalDeficitAmount');
    
    if (!modal) return;
    
    const deficit = requiredAmount - userBalance;
    
    if (modalRequiredAmount) modalRequiredAmount.textContent = `${requiredAmount.toFixed(2)} USDT`;
    if (modalUserBalance) modalUserBalance.textContent = `${userBalance.toFixed(2)} USDT`;
    if (modalDeficitAmount) modalDeficitAmount.textContent = `${deficit.toFixed(2)} USDT`;
    
    modal.style.display = 'flex';
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function hideLowFundsModal() {
    const modal = document.getElementById('lowFundsModal');
    if (modal) {
        modal.style.display = 'none';
    }
    
    // Restore body scroll
    document.body.style.overflow = '';
}

async function loadUserBalance() {
    try {
        const response = await apiCall('get_balance');
        if (response.success) {
            userBalance = parseFloat(response.balance) || 0;
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) {
                balanceElement.textContent = userBalance.toFixed(2);
            }
            
            // Update form balance display if visible
            const formBalanceElement = document.getElementById('formUserBalance');
            if (formBalanceElement) {
                formBalanceElement.textContent = userBalance.toFixed(2);
            }
            
            // Update balance status if form is visible
            if (selectedPlan && investmentPlans[selectedPlan]) {
                updateBalanceStatus();
            }
            
            console.log('User balance loaded successfully:', userBalance);
        } else {
            console.error('Error loading balance:', response.message);
            
            // Handle authentication error specifically
            if (response.error && response.error.includes('authenticated')) {
                userBalance = 0;
                showAuthenticationError();
            } else {
                userBalance = 0;
                const balanceElement = document.getElementById('availableBalance');
                if (balanceElement) balanceElement.textContent = 'Error';
                showMessage('Failed to load balance. Please refresh the page.', 'error');
            }
        }
    } catch (error) {
        console.error('Error loading balance:', error);
        userBalance = 0;
        const balanceElement = document.getElementById('availableBalance');
        if (balanceElement) balanceElement.textContent = 'Error';
        showMessage('Network error loading balance. Please refresh the page.', 'error');
    }
}

function showInsufficientBalanceMessage() {
    const insufficientMsg = document.getElementById('insufficientBalanceMsg');
    const investForm = document.getElementById('investmentForm');
    const planCards = document.querySelector('.investment-plans');
    
    if (insufficientMsg) insufficientMsg.style.display = 'block';
    if (investForm) investForm.style.display = 'none';
    if (planCards) planCards.style.display = 'none';
}

function showAuthenticationError() {
    const balanceElement = document.getElementById('availableBalance');
    const formBalanceElement = document.getElementById('formUserBalance');
    
    if (balanceElement) {
        balanceElement.textContent = 'Please log in';
        balanceElement.style.color = '#dc3545';
    }
    if (formBalanceElement) {
        formBalanceElement.textContent = '0.00';
        formBalanceElement.style.color = '#dc3545';
    }
    
    showMessage('Please log in to view your balance and make investments.', 'error');
}

function calculateReturns() {
    console.log('calculateReturns() called');
    const amountInput = document.getElementById('investAmount');
    if (!amountInput) {
        console.log('No amount input found');
        return;
    }
    
    const amount = parseFloat(amountInput.value);
    console.log('Amount entered:', amount);
    console.log('Selected plan:', selectedPlan);
    console.log('Investment plans:', investmentPlans);
    console.log('User balance:', userBalance);
    
    // Check if selected plan exists
    if (!selectedPlan || !investmentPlans[selectedPlan]) {
        console.log('Selected plan not found or plans not loaded');
        showMessage('Investment plan not available. Please refresh the page.', 'error');
        return;
    }
    
    const plan = investmentPlans[selectedPlan];
    console.log('Plan details:', plan);
      if (!amount || amount < plan.minAmount) {
        console.log('Amount validation failed - too low');
        showMessage(`Minimum investment for ${plan.name} is ${plan.minAmount} USDT`, 'error');
        return;
    }
    
    // Special handling for zero balance (likely authentication issue)
    if (userBalance === 0) {
        console.log('User balance is 0 - likely authentication issue');
        showMessage('Unable to verify your balance. Please log in again or refresh the page.', 'error');
        return;
    }
    
    if (amount > userBalance) {
        console.log('Amount validation failed - insufficient balance');
        // Show the low funds modal instead of just a message
        showLowFundsModal(amount);
        return;
    }
    
    const dailyReturn = amount * plan.dailyRate;
    const totalReturn = dailyReturn * plan.duration;
    
    console.log('Calculations:', { dailyReturn, totalReturn });
    
    // Update summary
    const summaryAmount = document.getElementById('summaryAmount');
    const summaryDailyReturn = document.getElementById('summaryDailyReturn');
    const summaryTotalReturn = document.getElementById('summaryTotalReturn');
    const summaryDuration = document.getElementById('summaryDuration');
    
    if (summaryAmount) summaryAmount.textContent = `${amount.toFixed(2)} USDT`;
    if (summaryDailyReturn) summaryDailyReturn.textContent = `${dailyReturn.toFixed(2)} USDT`;
    if (summaryTotalReturn) summaryTotalReturn.textContent = `${totalReturn.toFixed(2)} USDT`;
    if (summaryDuration) summaryDuration.textContent = `${plan.duration} days`;
    
    const investmentSummary = document.getElementById('investmentSummary');
    const investBtn = document.getElementById('investBtn');
      console.log('Summary element found:', !!investmentSummary);
    console.log('Button element found:', !!investBtn);
    console.log('Button disabled state before:', investBtn ? investBtn.disabled : 'N/A');
    
    if (investmentSummary) {
        investmentSummary.style.display = 'block';
        console.log('Summary shown');
    }
    if (investBtn) {
        investBtn.disabled = false;
        console.log('Button enabled successfully');
        console.log('Button disabled state after:', investBtn.disabled);
        showMessage('Investment calculation complete! You can now confirm your investment.', 'success');
    } else {
        console.log('Button not found!');
    }
}

async function handleInvestment(e) {
    e.preventDefault();
    
    const investAmountInput = document.getElementById('investAmount');
    if (!investAmountInput) return;
    
    const amount = parseFloat(investAmountInput.value);
    
    if (!amount || amount < investmentPlans[selectedPlan].minAmount) {
        showMessage(`Minimum investment for ${investmentPlans[selectedPlan].name} is ${investmentPlans[selectedPlan].minAmount} USDT`, 'error');
        return;
    }
      if (amount > userBalance) {
        // Show the low funds modal instead of just a message
        showLowFundsModal(amount);
        return;
    }
    
    // Disable button to prevent double submission
    const investBtn = document.getElementById('investBtn');
    if (!investBtn) return;
    
    const originalText = investBtn.textContent;
    investBtn.disabled = true;
    investBtn.textContent = 'Processing...';
    
    try {
        const response = await apiCall('create_investment', {
            amount: amount,
            plan: selectedPlan
        });
          if (response.success) {
            // Show success modal instead of alert
            showSuccessModal(amount, selectedPlan, response.investment_id);
            
            // Reset form
            const investmentForm = document.getElementById('investmentForm');
            const investmentSummary = document.getElementById('investmentSummary');
            const basicPlanCard = document.querySelector('[data-plan="basic"]');
            const otherPlanCards = document.querySelectorAll('.plan-card:not([data-plan="basic"])');
            
            if (investmentForm) investmentForm.reset();
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (basicPlanCard) basicPlanCard.classList.add('active');
            if (otherPlanCards.length > 0) {
                otherPlanCards.forEach(card => {
                    card.classList.remove('active');
                });
            }
            
            // Reload data to update balance and investments
            loadUserBalance();
            loadActiveInvestments();
            loadInvestmentHistory();
            
        } else {
            showMessage(response.message || 'Failed to create investment', 'error');
        }
    } catch (error) {
        console.error('Error creating investment:', error);
        showMessage('Network error. Please try again.', 'error');
    } finally {
        investBtn.disabled = false;
        investBtn.textContent = originalText;
    }
}

async function loadActiveInvestments() {
    try {
        const response = await apiCall('get_active_investments');
        const container = document.getElementById('activeInvestmentsList');
        
        if (!container) return;
          if (response.success && response.investments && response.investments.length > 0) {
            container.innerHTML = response.investments.map(investment => `
                <div class="investment-item" data-investment-id="${investment.id}" style="cursor: pointer;">
                    <div class="investment-info">
                        <div class="investment-plan">${investment.plan_name}</div>
                        <div class="investment-amount">${parseFloat(investment.amount).toFixed(2)} USDT</div>
                        <div class="investment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${(investment.days_elapsed / investment.duration * 100)}%"></div>
                            </div>
                            <span class="progress-text">${investment.days_elapsed}/${investment.duration} days</span>
                        </div>
                    </div>
                    <div class="investment-earnings">
                        <div class="daily-return">+${parseFloat(investment.daily_return).toFixed(2)} USDT/day</div>
                        <div class="total-earned">${parseFloat(investment.total_earned).toFixed(2)} USDT earned</div>
                    </div>
                </div>
            `).join('');
            
            // Add click event listeners to investment items
            container.addEventListener('click', (e) => {
                const investmentItem = e.target.closest('.investment-item');
                if (investmentItem) {
                    const investmentId = investmentItem.dataset.investmentId;
                    if (investmentId) {
                        // Add click animation
                        investmentItem.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            investmentItem.style.transform = '';
                            // Navigate to active plan page
                            window.location.href = `active_plan.php?id=${investmentId}`;
                        }, 150);
                    }
                }
            });
        } else {
            container.innerHTML = '<div class="no-data">No active investments found</div>';
        }
    } catch (error) {
        console.error('Error loading active investments:', error);
        const container = document.getElementById('activeInvestmentsList');
        if (container) {
            container.innerHTML = '<div class="error">Error loading investments</div>';
        }
    }
}

async function loadInvestmentHistory() {
    try {
        const response = await apiCall('get_investment_history');
        const container = document.getElementById('investmentHistoryList');
        
        if (!container) return;
        
        if (response.success && response.history && response.history.length > 0) {
            container.innerHTML = response.history.map(investment => `
                <div class="history-item">
                    <div class="history-info">
                        <div class="history-plan">${investment.plan_name}</div>
                        <div class="history-amount">${parseFloat(investment.amount).toFixed(2)} USDT</div>
                        <div class="history-date">${new Date(investment.created_at).toLocaleDateString()}</div>
                    </div>
                    <div class="history-status">
                        <span class="status-badge ${investment.status}">${investment.status}</span>
                        <div class="history-return">${parseFloat(investment.total_return).toFixed(2)} USDT</div>
                    </div>
                </div>
            `).join('');
        } else {
            container.innerHTML = '<div class="no-data">No investment history found</div>';
        }
    } catch (error) {
        console.error('Error loading investment history:', error);
        const container = document.getElementById('investmentHistoryList');
        if (container) {
            container.innerHTML = '<div class="error-message">Error loading investment history</div>';
        }
    }
}

async function loadInvestmentPlans() {
    try {
        const response = await apiCall('get_investment_plans');
        
        if (response.success) {
            const plans = response.plans;
            
            // Convert plans array to object for easy access
            investmentPlans = {};
            plans.forEach(plan => {
                investmentPlans[plan.plan_code] = {
                    id: plan.id,
                    name: plan.plan_name,
                    dailyRate: plan.daily_rate,
                    duration: plan.duration,
                    minAmount: plan.min_amount,
                    maxAmount: plan.max_amount,
                    description: plan.description,
                    features: plan.features || [],
                    isActive: plan.is_active,
                    isFeatured: plan.is_featured
                };
            });
            
            renderInvestmentPlans(plans);
            setupInvestmentForm();
        } else {
            showMessage('Failed to load investment plans: ' + (response.message || response.error), 'error');
            renderDefaultPlans();
        }
    } catch (error) {
        console.error('Error loading investment plans:', error);
        showMessage('Failed to load investment plans. Please try again.', 'error');
        renderDefaultPlans();
    }
}

function renderInvestmentPlans(plans) {
    const container = document.getElementById('investmentPlansContainer');
    if (!container) return;
    
    container.innerHTML = '';
    
    plans.forEach(plan => {
        const isBasic = plan.plan_code === 'basic';
        const isDisabled = !plan.is_active || !isBasic; // Only enable basic plan
        
        const planCard = document.createElement('div');
        planCard.className = `plan-card ${isDisabled ? 'disabled' : ''}`;
        planCard.dataset.plan = plan.plan_code;
        
        if (isDisabled) {
            planCard.style.opacity = '0.5';
            planCard.style.pointerEvents = 'none';
        }
          planCard.innerHTML = `
            <div class="plan-header">
                <div class="plan-name">${plan.plan_name}</div>
                ${plan.is_featured ? '<div class="plan-badge">Most Popular</div>' : ''}
                ${isDisabled && !isBasic ? '<div class="plan-badge coming-soon">Coming Soon</div>' : ''}
                <div class="plan-rate">${(plan.daily_rate * 100).toFixed(1)}%</div>
                <div class="plan-period">Daily</div>
                <div class="plan-duration">${plan.duration} Days</div>
                <div class="plan-minimum-header">Min: ${plan.min_amount} USDT</div>
            </div>
            <div class="plan-details">
                <div class="plan-features">
                    ${plan.features.map(feature => `<li>✅ ${feature}</li>`).join('')}
                </div>
            </div>
            ${isDisabled && !isBasic ? '<div class="plan-overlay"><span>Coming Soon</span></div>' : ''}
        `;
        
        container.appendChild(planCard);
    });
}

function renderDefaultPlans() {
    // Fallback: render basic plan only if API fails
    const container = document.getElementById('investmentPlansContainer');
    if (!container) return;
    
    investmentPlans = {
        basic: {
            name: 'Basic Plan',
            dailyRate: 0.0167,
            duration: 30,
            minAmount: 600,
            features: ['1.67% daily simple interest', 'Non-compounding', '30-day duration', 'Low risk', 'Beginner friendly']
        }
    };
    
    container.innerHTML = `
        <div class="plan-card active" data-plan="basic">
            <div class="plan-header">
                <h4>Basic Plan</h4>
                <div class="plan-badge">Most Popular</div>
            </div>
            <div class="plan-details">
                <div class="plan-return">1.67% Daily Simple Interest</div>
                <div class="plan-duration">30 Days</div>
                <div class="plan-minimum">Min: 600 USDT</div>
            </div>
            <div class="plan-features">
                <ul>
                    <li>✅ 1.67% daily simple interest</li>
                    <li>✅ Non-compounding returns</li>
                    <li>✅ 30-day duration</li>
                    <li>✅ Low risk</li>
                    <li>✅ Beginner friendly</li>
                </ul>
            </div>
        </div>
    `;
    
    setupInvestmentForm();
}

// API call function
async function apiCall(endpoint, data = null) {
    const url = `../ajax.php`;
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: endpoint,
            ...data
        })
    };

    const response = await fetch(url, options);
    return await response.json();
}

function showMessage(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">Ã—</button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize footer menu (assuming this function exists globally)
function initFooterMenu() {
    // Footer menu initialization if needed
    console.log('Footer menu initialized for make investment page');
}

// Success Modal Functions
function showSuccessModal(amount, plan, investmentId) {
    const modal = document.getElementById('successModal');
    const successAmount = document.getElementById('successAmount');
    const successPlan = document.getElementById('successPlan');
    const successInvestmentId = document.getElementById('successInvestmentId');
    const successNewBalance = document.getElementById('successNewBalance');
    
    if (!modal) return;
    
    // Populate modal data
    if (successAmount) successAmount.textContent = `${amount.toFixed(2)} USDT`;
    if (successPlan) successPlan.textContent = investmentPlans[plan]?.name || 'Basic Plan';
    if (successInvestmentId) successInvestmentId.textContent = `#${investmentId}`;
    
    // Calculate and display new balance
    const newBalance = userBalance - amount;
    if (successNewBalance) successNewBalance.textContent = `${newBalance.toFixed(2)} USDT`;
    
    // Update global userBalance for immediate UI updates
    userBalance = newBalance;
    
    // Update balance display in the page
    const balanceElement = document.getElementById('availableBalance');
    if (balanceElement) {
        balanceElement.textContent = newBalance.toFixed(2);
    }
    
    // Show modal
    modal.style.display = 'flex';
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function hideSuccessModal() {
    const modal = document.getElementById('successModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

// Initialize Success Modal Event Handlers
function initSuccessModalHandlers() {
    const closeSuccessModal = document.getElementById('closeSuccessModal');
    const makeAnotherInvestmentBtn = document.getElementById('makeAnotherInvestmentBtn');
    const viewDashboardBtn = document.getElementById('viewDashboardBtn');
    const successModal = document.getElementById('successModal');
    
    if (closeSuccessModal) {
        closeSuccessModal.addEventListener('click', hideSuccessModal);
    }
    
    if (makeAnotherInvestmentBtn) {
        makeAnotherInvestmentBtn.addEventListener('click', function() {
            hideSuccessModal();
            // Reload the page to reset form and start fresh
            window.location.reload();
        });
    }
    
    if (viewDashboardBtn) {
        viewDashboardBtn.addEventListener('click', function() {
            window.location.href = 'dashboard.php';
        });
    }
    
    // Close modal when clicking outside
    if (successModal) {
        successModal.addEventListener('click', function(e) {
            if (e.target === successModal) {
                hideSuccessModal();
            }
        });
    }
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideSuccessModal();
        }
    });
}

