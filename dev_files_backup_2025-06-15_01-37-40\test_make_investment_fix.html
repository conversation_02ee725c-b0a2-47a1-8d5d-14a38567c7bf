<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Make Investment Page Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-section h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #212529; }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb; 
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border: 1px solid #bee5eb; 
        }
        .loading {
            color: #6c757d;
            font-style: italic;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Make Investment Page Fix Test</h1>
        <p>Testing the fixed make_investment page styling and functionality.</p>

        <!-- API Tests -->
        <div class="test-section">
            <h3>🔍 API Tests</h3>
            <button class="btn" onclick="testInvestmentPlansAPI()">Test Investment Plans API</button>
            <button class="btn" onclick="testBalanceAPI()">Test Balance API</button>
            <button class="btn" onclick="testCreateInvestmentAPI()">Test Create Investment API</button>
            <div id="apiResults"></div>
        </div>

        <!-- CSS Styling Test -->
        <div class="test-section">
            <h3>🎨 CSS Styling Test</h3>
            <button class="btn btn-success" onclick="testCSSStyles()">Test CSS Classes</button>
            <div id="cssResults"></div>
        </div>

        <!-- JavaScript Functionality Test -->
        <div class="test-section">
            <h3>⚡ JavaScript Functionality Test</h3>
            <button class="btn btn-warning" onclick="testJavaScriptFunctions()">Test JS Functions</button>
            <div id="jsResults"></div>
        </div>

        <!-- Page Load Test -->
        <div class="test-section">
            <h3>📄 Page Load Test</h3>
            <button class="btn btn-danger" onclick="loadMakeInvestmentPage()">Load Make Investment Page</button>
            <div id="pageLoadResults"></div>
            <div id="pageFrame"></div>
        </div>

        <!-- Authentication Test -->
        <div class="test-section">
            <h3>🔐 Authentication Test</h3>
            <button class="btn" onclick="testAuthentication()">Test User Authentication</button>
            <div id="authResults"></div>
        </div>
    </div>

    <script>
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        async function testInvestmentPlansAPI() {
            const container = document.getElementById('apiResults');
            container.innerHTML = '';
            
            try {
                showResult('apiResults', '🔄 Testing Investment Plans API...', 'loading');
                
                const response = await fetch('http://localhost:8000/api/investment-plans', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                if (result.success && result.data) {
                    showResult('apiResults', `✅ Investment Plans API: SUCCESS - Found ${result.data.length} plans`, 'success');
                    
                    // Show plan details
                    result.data.forEach(plan => {
                        const status = plan.is_active ? 'ENABLED' : 'DISABLED';
                        showResult('apiResults', `📋 ${plan.plan_name}: ${plan.daily_rate*100}% daily, ${plan.duration} days, Min: ${plan.min_amount} USDT - ${status}`, 'info');
                    });
                } else {
                    throw new Error('API returned unsuccessful response');
                }
            } catch (error) {
                showResult('apiResults', `❌ Investment Plans API Error: ${error.message}`, 'error');
            }
        }

        async function testBalanceAPI() {
            const container = document.getElementById('apiResults');
            
            try {
                showResult('apiResults', '🔄 Testing Balance API...', 'loading');
                
                const response = await fetch('/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_balance'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('apiResults', `✅ Balance API: SUCCESS - Balance: ${result.balance} USDT`, 'success');
                } else {
                    showResult('apiResults', `⚠️ Balance API: ${result.message || result.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('apiResults', `❌ Balance API Error: ${error.message}`, 'error');
            }
        }

        async function testCreateInvestmentAPI() {
            const container = document.getElementById('apiResults');
            
            try {
                showResult('apiResults', '🔄 Testing Create Investment API...', 'loading');
                
                const response = await fetch('/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'create_investment',
                        amount: 600,
                        plan: 'basic'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('apiResults', `✅ Create Investment API: SUCCESS - Investment ID: ${result.investment_id}`, 'success');
                } else {
                    showResult('apiResults', `ℹ️ Create Investment API: ${result.message || result.error || 'Test response received'}`, 'info');
                }
            } catch (error) {
                showResult('apiResults', `❌ Create Investment API Error: ${error.message}`, 'error');
            }
        }

        function testCSSStyles() {
            const container = document.getElementById('cssResults');
            container.innerHTML = '';
            
            const cssClasses = [
                'make-investment-page',
                'balance-overview',
                'investment-plans',
                'plan-card',
                'plan-header',
                'plan-details',
                'plan-features',
                'investment-form-container',
                'selected-plan-summary',
                'balance-info',
                'investment-summary',
                'modal',
                'modal-content',
                'notification'
            ];

            let testsPassed = 0;
            let totalTests = cssClasses.length;

            cssClasses.forEach(className => {
                // Create a test element with the class
                const testElement = document.createElement('div');
                testElement.className = className;
                document.body.appendChild(testElement);

                // Get computed styles
                const styles = window.getComputedStyle(testElement);
                
                // Check if styles are applied (basic check)
                const hasStyles = (
                    styles.display !== 'inline' ||
                    styles.padding !== '0px' ||
                    styles.margin !== '0px' ||
                    styles.backgroundColor !== 'rgba(0, 0, 0, 0)' ||
                    styles.borderRadius !== '0px'
                );

                if (hasStyles) {
                    showResult('cssResults', `✅ CSS Class .${className}: Styles applied`, 'success');
                    testsPassed++;
                } else {
                    showResult('cssResults', `❌ CSS Class .${className}: No styles found`, 'error');
                }

                // Clean up
                document.body.removeChild(testElement);
            });

            const percentage = Math.round((testsPassed / totalTests) * 100);
            showResult('cssResults', `📊 CSS Test Summary: ${testsPassed}/${totalTests} classes have styles (${percentage}%)`, percentage >= 80 ? 'success' : 'error');
        }

        function testJavaScriptFunctions() {
            const container = document.getElementById('jsResults');
            container.innerHTML = '';

            // Test if we can access the make_investment.js functions
            const iframe = document.createElement('iframe');
            iframe.src = '/frontend/user/make_investment.php';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);

            iframe.onload = function() {
                try {
                    const iframeWindow = iframe.contentWindow;
                    const functionTests = [
                        'loadInvestmentPlans',
                        'loadUserBalance',
                        'calculateReturns',
                        'showInvestmentForm',
                        'updateBalanceStatus',
                        'showLowFundsModal',
                        'showSuccessModal'
                    ];

                    let functionsFound = 0;
                    let totalFunctions = functionTests.length;

                    functionTests.forEach(funcName => {
                        if (typeof iframeWindow[funcName] === 'function') {
                            showResult('jsResults', `✅ Function ${funcName}: Found and callable`, 'success');
                            functionsFound++;
                        } else {
                            showResult('jsResults', `❌ Function ${funcName}: Not found or not callable`, 'error');
                        }
                    });

                    const percentage = Math.round((functionsFound / totalFunctions) * 100);
                    showResult('jsResults', `📊 JavaScript Test Summary: ${functionsFound}/${totalFunctions} functions available (${percentage}%)`, percentage >= 80 ? 'success' : 'error');

                } catch (error) {
                    showResult('jsResults', `❌ JavaScript Test Error: ${error.message}`, 'error');
                }

                document.body.removeChild(iframe);
            };

            iframe.onerror = function() {
                showResult('jsResults', '❌ Failed to load page for JavaScript testing', 'error');
                document.body.removeChild(iframe);
            };
        }

        function loadMakeInvestmentPage() {
            const container = document.getElementById('pageLoadResults');
            const frameContainer = document.getElementById('pageFrame');
            container.innerHTML = '';
            frameContainer.innerHTML = '';

            showResult('pageLoadResults', '🔄 Loading Make Investment Page...', 'loading');

            const iframe = document.createElement('iframe');
            iframe.src = '/frontend/user/make_investment.php';
            
            iframe.onload = function() {
                showResult('pageLoadResults', '✅ Make Investment Page: Loaded successfully', 'success');
                
                // Test if key elements are present
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        
                        const keyElements = [
                            { selector: '#availableBalance', name: 'Balance Display' },
                            { selector: '#investmentPlansContainer', name: 'Investment Plans Container' },
                            { selector: '#investmentFormCard', name: 'Investment Form Card' },
                            { selector: '#lowFundsModal', name: 'Low Funds Modal' },
                            { selector: '#successModal', name: 'Success Modal' }
                        ];

                        let elementsFound = 0;
                        keyElements.forEach(element => {
                            const el = iframeDoc.querySelector(element.selector);
                            if (el) {
                                showResult('pageLoadResults', `✅ Element ${element.name}: Found`, 'success');
                                elementsFound++;
                            } else {
                                showResult('pageLoadResults', `❌ Element ${element.name}: Not found`, 'error');
                            }
                        });

                        const percentage = Math.round((elementsFound / keyElements.length) * 100);
                        showResult('pageLoadResults', `📊 Page Elements: ${elementsFound}/${keyElements.length} found (${percentage}%)`, percentage >= 80 ? 'success' : 'error');

                    } catch (error) {
                        showResult('pageLoadResults', `⚠️ Could not inspect page elements: ${error.message}`, 'error');
                    }
                }, 1000);
            };

            iframe.onerror = function() {
                showResult('pageLoadResults', '❌ Failed to load Make Investment Page', 'error');
            };

            frameContainer.appendChild(iframe);
        }

        async function testAuthentication() {
            const container = document.getElementById('authResults');
            container.innerHTML = '';

            try {
                showResult('authResults', '🔄 Testing Authentication Status...', 'loading');

                // Test session check
                const response = await fetch('/frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_balance'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showResult('authResults', '✅ Authentication: User is logged in and authenticated', 'success');
                    showResult('authResults', `ℹ️ User Balance: ${result.balance} USDT`, 'info');
                } else if (result.error && result.error.includes('authenticated')) {
                    showResult('authResults', '⚠️ Authentication: User is not authenticated', 'error');
                    showResult('authResults', 'ℹ️ Suggestion: Please log in to test the make investment page', 'info');
                } else {
                    showResult('authResults', `⚠️ Authentication: ${result.message || result.error}`, 'error');
                }

            } catch (error) {
                showResult('authResults', `❌ Authentication Test Error: ${error.message}`, 'error');
            }
        }

        // Auto-run some tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Make Investment Fix Test Page Loaded');
            
            // Run API tests automatically
            setTimeout(() => {
                testInvestmentPlansAPI();
            }, 500);
            
            setTimeout(() => {
                testAuthentication();
            }, 1000);
        });
    </script>
</body>
</html>
