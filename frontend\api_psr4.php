<?php

declare(strict_types=1);

/**
 * PSR-4 Compliant API Wrapper Entry Point
 * 
 * This file provides backward compatibility while using the new PSR-4 structure
 */

// Include composer autoloader
require_once __DIR__ . '/../backend/vendor/autoload.php';

use Simbi\Tls\Frontend\Services\ApiService;
use Simbi\Tls\Frontend\Services\SessionService;
use Simbi\Tls\Frontend\Config\FrontendConfig;

// Initialize frontend configuration
FrontendConfig::init();

/**
 * Backward compatible API wrapper class
 * Provides the same interface as the old APIWrapper class
 */
class APIWrapper
{
    private ApiService $apiService;

    public function __construct(?string $baseUrl = null)
    {
        $this->apiService = new ApiService($baseUrl);
    }

    public function setToken(?string $token): void
    {
        $this->apiService->setToken($token);
    }

    // Authentication methods
    public function register(string $email, string $password): array
    {
        return $this->apiService->register($email, $password);
    }

    public function login(string $email, string $password): array
    {
        return $this->apiService->login($email, $password);
    }

    public function passwordResetRequest(string $email): array
    {
        return $this->apiService->passwordResetRequest($email);
    }

    public function passwordReset(string $token, string $newPassword): array
    {
        return $this->apiService->passwordReset($token, $newPassword);
    }

    // User methods
    public function getUserProfile(): array
    {
        return $this->apiService->getUserProfile();
    }

    public function changePassword(string $currentPassword, string $newPassword): array
    {
        return $this->apiService->changePassword($currentPassword, $newPassword);
    }

    // Wallet methods
    public function createWallet(): array
    {
        return $this->apiService->createWallet();
    }

    public function getBalance(): array
    {
        return $this->apiService->getBalance();
    }

    public function getWallet(): array
    {
        return $this->apiService->getWallet();
    }

    public function getLiveWalletBalance(string $address): array
    {
        return $this->apiService->getLiveWalletBalance($address);
    }

    public function withdraw(string $to, float $amount): array
    {
        return $this->apiService->withdraw($to, $amount);
    }

    public function sweepFunds(): array
    {
        return $this->apiService->sweepFunds();
    }

    // Transaction methods
    public function getTransactions(?string $type = null, int $limit = 10, int $page = 0): array
    {
        return $this->apiService->getTransactions($type, $limit, $page);
    }

    public function getTransactionStatistics(): array
    {
        return $this->apiService->getTransactionStatistics();
    }

    // Investment methods
    public function getInvestmentPlans(): array
    {
        return $this->apiService->getInvestmentPlans();
    }

    public function createInvestment(float $amount, string $plan): array
    {
        return $this->apiService->createInvestment($amount, $plan);
    }

    public function getInvestments(): array
    {
        return $this->apiService->getInvestments();
    }

    public function getActiveInvestments(): array
    {
        return $this->apiService->getActiveInvestments();
    }

    // Payment methods
    public function confirmPayment(int $walletId, float $amount): array
    {
        return $this->apiService->confirmPayment($walletId, $amount);
    }

    public function getPaymentStatus(string $transactionHash): array
    {
        return $this->apiService->getPaymentStatus($transactionHash);
    }

    // Admin methods
    public function getSystemStatistics(): array
    {
        return $this->apiService->getSystemStatistics();
    }

    public function getUserList(int $limit = 20, int $offset = 0, ?string $status = null): array
    {
        return $this->apiService->getUserList($limit, $offset, $status);
    }

    public function getAllTransactions(int $limit = 100, int $offset = 0): array
    {
        return $this->apiService->getAllTransactions($limit, $offset);
    }

    public function updateUserStatus(int $userId, string $status): array
    {
        return $this->apiService->updateUserStatus($userId, $status);
    }

    public function promoteUser(int $userId): array
    {
        return $this->apiService->promoteUser($userId);
    }

    public function demoteUser(int $userId): array
    {
        return $this->apiService->demoteUser($userId);
    }

    public function getActivityLogs(int $limit = 50, int $offset = 0): array
    {
        return $this->apiService->getActivityLogs($limit, $offset);
    }

    public function getAdminTransactionStatistics(): array
    {
        return $this->apiService->getAdminTransactionStatistics();
    }

    public function getSystemHealth(): array
    {
        return $this->apiService->getSystemHealth();
    }
}

// Handle direct API calls via query string (for frontend JavaScript)
if (isset($_GET['action'])) {
    SessionService::init();
    header('Content-Type: application/json');
    
    $action = $_GET['action'];
    $api = new APIWrapper();
    
    // Get request data for POST requests
    $data = null;
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
    }
    
    try {
        switch ($action) {
            case 'get_balance':
                $result = $api->getBalance();
                break;
                
            case 'get_transaction_statistics':
                $result = $api->getTransactionStatistics();
                break;
                
            case 'get_transactions':
                $limit = $_GET['limit'] ?? $data['limit'] ?? 10;
                $page = $_GET['page'] ?? $data['page'] ?? 0;
                $type = $_GET['type'] ?? $data['type'] ?? null;
                $result = $api->getTransactions($type, (int)$limit, (int)$page);
                break;
                
            case 'get_investment_plans':
                $result = $api->getInvestmentPlans();
                break;
                
            case 'get_investments':
                $result = $api->getInvestments();
                break;
                
            case 'get_active_investments':
                $result = $api->getActiveInvestments();
                break;
                
            default:
                $result = ['error' => 'Unknown action: ' . $action];
                break;
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        error_log('API Error: ' . $e->getMessage());
        echo json_encode(['error' => 'Internal server error']);
    }
}
