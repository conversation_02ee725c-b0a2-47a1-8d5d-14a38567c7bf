-- =================================================================
-- TRON Wallet System - Comprehensive Database Schema (Production)
-- =================================================================
-- This file consolidates all database table definitions from across
-- the entire TRON Wallet system into a single, comprehensive schema.
--
-- Combined from:
-- - backend/database.sql (core tables)
-- - create_investment_tables.php (investment system)
-- - update_db_schema.php (additional features)
-- - PaymentConfirmationService.php (payment system tables)
-- - Various PHP setup files
--
-- Production Database: MySQL 5.7+ / MariaDB 10.3+
-- Character Set: utf8mb4 (full Unicode support)
-- =================================================================

-- Create database with proper character set
CREATE DATABASE IF NOT EXISTS tlssc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE tlssc;

-- Disable foreign key checks for clean creation
SET FOREIGN_KEY_CHECKS = 0;

-- Drop tables in correct order (dependencies first)
DROP TABLE IF EXISTS investment_payouts;
DROP TABLE IF EXISTS investments;
DROP TABLE IF EXISTS deposit_logs;
DROP TABLE IF EXISTS balance_history;
DROP TABLE IF EXISTS webhooks;
DROP TABLE IF EXISTS admin_logs;
DROP TABLE IF EXISTS password_resets;
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS sweeps;
DROP TABLE IF EXISTS wallets;
DROP TABLE IF EXISTS users;

-- =================================================================
-- CORE SYSTEM TABLES
-- =================================================================

-- Users table - Authentication and user management
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(20, 8) DEFAULT 0.00000000,
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    password_reset_token VARCHAR(255) NULL,
    password_reset_expires TIMESTAMP NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_reset_token (password_reset_token),
    INDEX idx_verification_token (email_verification_token),
    INDEX idx_admin (is_admin),
    INDEX idx_active (is_active),
    INDEX idx_created (created_at)
) ENGINE=InnoDB COMMENT='User accounts and authentication data';

-- Wallets table - TRON wallet management
CREATE TABLE wallets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    address VARCHAR(255) NOT NULL UNIQUE,
    hex_address VARCHAR(255) NOT NULL,
    private_key TEXT NOT NULL,
    balance DECIMAL(20, 6) DEFAULT 0.000000,
    total_deposits DECIMAL(20, 6) DEFAULT 0.000000,
    total_withdrawals DECIMAL(20, 6) DEFAULT 0.000000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_address (address),
    INDEX idx_hex_address (hex_address),
    INDEX idx_created (created_at)
) ENGINE=InnoDB COMMENT='TRON wallet addresses and private keys';

-- Transactions table - Complete transaction tracking
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    wallet_id INT NOT NULL,
    transaction_hash VARCHAR(255) NOT NULL,
    type ENUM('deposit', 'withdrawal', 'sweep', 'transfer', 'investment', 'payout') NOT NULL,
    amount DECIMAL(20, 6) NOT NULL,
    fee DECIMAL(20, 6) DEFAULT 0.000000,
    from_address VARCHAR(255) NULL,
    to_address VARCHAR(255) NULL,
    status ENUM('pending', 'confirmed', 'failed', 'cancelled') DEFAULT 'pending',
    block_number BIGINT NULL,
    confirmations INT DEFAULT 0,
    notes TEXT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (wallet_id) REFERENCES wallets(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_wallet_id (wallet_id),
    INDEX idx_hash (transaction_hash),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created (created_at),
    INDEX idx_block (block_number),
    INDEX idx_amount (amount)
) ENGINE=InnoDB COMMENT='All transaction records and history';

-- Sweeps table - Wallet fund sweep operations
CREATE TABLE sweeps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    wallet_id INT NOT NULL,
    amount DECIMAL(20, 6) NOT NULL,
    destination_address VARCHAR(255) NULL,
    transaction_hash VARCHAR(255) NULL,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (wallet_id) REFERENCES wallets(id) ON DELETE CASCADE,
    INDEX idx_wallet_id (wallet_id),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
) ENGINE=InnoDB COMMENT='Wallet sweep operations for fund management';

-- =================================================================
-- INVESTMENT SYSTEM TABLES
-- =================================================================

-- Investments table - Investment management system
CREATE TABLE investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    plan_type VARCHAR(50) NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    daily_rate DECIMAL(5, 4) NOT NULL,
    duration INT NOT NULL,
    daily_return DECIMAL(20, 8) NOT NULL,
    total_return DECIMAL(20, 8) DEFAULT 0.00000000,
    total_earned DECIMAL(20, 8) DEFAULT 0.00000000,
    days_elapsed INT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    next_payout DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at DATETIME NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_plan_type (plan_type),
    INDEX idx_next_payout (next_payout),
    INDEX idx_created (created_at)
) ENGINE=InnoDB COMMENT='Investment plans and tracking';

-- Investment Payouts table - Daily payout tracking
CREATE TABLE investment_payouts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    investment_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    payout_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'completed',
    transaction_hash VARCHAR(255) NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_investment_id (investment_id),
    INDEX idx_user_id (user_id),
    INDEX idx_payout_date (payout_date),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
) ENGINE=InnoDB COMMENT='Investment payout records';

-- =================================================================
-- ADMINISTRATIVE TABLES
-- =================================================================

-- Admin Logs table - Administrative audit trail
CREATE TABLE admin_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    action VARCHAR(255) NOT NULL,
    target_type VARCHAR(100) NULL,
    target_id INT NULL,
    details JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_target_type (target_type),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB COMMENT='Administrative activity logs';

-- Password Resets table - Secure password recovery
CREATE TABLE password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_token (token),
    INDEX idx_expires (expires_at),
    INDEX idx_used (used),
    INDEX idx_created (created_at)
) ENGINE=InnoDB COMMENT='Password reset tokens';

-- =================================================================
-- PAYMENT SYSTEM TABLES
-- =================================================================

-- Balance History table - User balance change tracking
CREATE TABLE balance_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    previous_balance DECIMAL(20, 8) NOT NULL,
    new_balance DECIMAL(20, 8) NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    type ENUM('deposit', 'withdrawal', 'investment', 'payout', 'admin_adjustment') NOT NULL,
    description TEXT NULL,
    transaction_hash VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_created (created_at),
    INDEX idx_amount (amount)
) ENGINE=InnoDB COMMENT='User balance change history';

-- Deposit Logs table - Deposit activity tracking
CREATE TABLE deposit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    wallet_address VARCHAR(255) NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    transaction_hash VARCHAR(255) NOT NULL,
    status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
    payment_method VARCHAR(50) DEFAULT 'USDT',
    block_number BIGINT NULL,
    confirmations INT DEFAULT 0,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_wallet_address (wallet_address),
    INDEX idx_transaction_hash (transaction_hash),
    INDEX idx_status (status),
    INDEX idx_created (created_at),
    INDEX idx_amount (amount)
) ENGINE=InnoDB COMMENT='Deposit confirmation logs';

-- Webhooks table - TronGrid webhook logs
CREATE TABLE webhooks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    webhook_type VARCHAR(100) NOT NULL,
    transaction_hash VARCHAR(255) NOT NULL,
    contract_address VARCHAR(255) NULL,
    from_address VARCHAR(255) NULL,
    to_address VARCHAR(255) NULL,
    amount DECIMAL(20, 8) NULL,
    block_number BIGINT NULL,
    confirmations INT DEFAULT 0,
    status ENUM('received', 'processed', 'ignored', 'failed') DEFAULT 'received',
    payload JSON NOT NULL,
    processed_at TIMESTAMP NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_type (webhook_type),
    INDEX idx_hash (transaction_hash),
    INDEX idx_status (status),
    INDEX idx_created (created_at),
    INDEX idx_processed (processed_at),
    INDEX idx_to_address (to_address)
) ENGINE=InnoDB COMMENT='TronGrid webhook event logs';

-- =================================================================
-- ADDITIONAL PERFORMANCE INDEXES
-- =================================================================

-- Composite indexes for common queries
ALTER TABLE transactions ADD INDEX idx_user_status_created (user_id, status, created_at);
ALTER TABLE transactions ADD INDEX idx_wallet_type_created (wallet_id, type, created_at);
ALTER TABLE investments ADD INDEX idx_user_status_next (user_id, status, next_payout);
ALTER TABLE investment_payouts ADD INDEX idx_user_date_status (user_id, payout_date, status);
ALTER TABLE deposit_logs ADD INDEX idx_status_created (status, created_at);
ALTER TABLE webhooks ADD INDEX idx_status_type_created (status, webhook_type, created_at);

-- =================================================================
-- DATABASE OPTIMIZATION SETTINGS
-- =================================================================

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Analyze tables for better query performance
ANALYZE TABLE users, wallets, transactions, sweeps, investments, investment_payouts, 
             admin_logs, password_resets, balance_history, deposit_logs, webhooks;

-- =================================================================
-- SCHEMA VERIFICATION QUERIES
-- =================================================================

-- Show all tables
-- SELECT TABLE_NAME, TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'tlssc';

-- Show table sizes
-- SELECT TABLE_NAME, ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)' 
-- FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'tlssc' ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- Show foreign key relationships
-- SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME 
-- FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE REFERENCED_TABLE_SCHEMA = 'tlssc';

-- =================================================================
-- PRODUCTION NOTES
-- =================================================================
-- 
-- 1. This schema supports the complete TRON Wallet system including:
--    - User authentication and management
--    - TRON wallet operations
--    - Transaction tracking and history
--    - Investment management system
--    - Administrative audit trails
--    - Payment confirmation system
--    - TronGrid webhook processing
--
-- 2. All tables use InnoDB engine for:
--    - ACID compliance
--    - Foreign key constraints
--    - Row-level locking
--    - Crash recovery
--
-- 3. Indexes are optimized for:
--    - User lookup queries
--    - Transaction history retrieval
--    - Investment payout processing
--    - Administrative reporting
--    - Payment confirmation workflows
--
-- 4. Decimal precision for financial data:
--    - DECIMAL(20, 8) for cryptocurrency amounts (supports up to 12 digits + 8 decimals)
--    - DECIMAL(20, 6) for TRX amounts (standard TRX precision)
--    - DECIMAL(5, 4) for percentage rates (e.g., 0.0500 = 5%)
--
-- 5. Character sets:
--    - utf8mb4 for full Unicode support including emojis
--    - Proper collation for international users
--
-- =================================================================
-- END OF COMPREHENSIVE SCHEMA
-- =================================================================
