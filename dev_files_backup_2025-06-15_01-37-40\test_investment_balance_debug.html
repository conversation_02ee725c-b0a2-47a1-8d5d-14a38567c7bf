<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Investment Page Balance Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Investment Page Balance Debug</h1>
    <p>Testing the exact same API calls that the investment page makes.</p>
    
    <div class="section">
        <h2>Step 1: Authentication Test</h2>
        <button onclick="testAuth()">Test Authentication</button>
        <div id="authResult"></div>
    </div>

    <div class="section">
        <h2>Step 2: Balance API Test</h2>
        <button onclick="testBalanceAPI()" id="balanceBtn" disabled>Test Balance API</button>
        <div id="balanceResult"></div>
    </div>

    <div class="section">
        <h2>Step 3: Simulate Frontend Load</h2>
        <button onclick="simulateFrontendLoad()" id="frontendBtn" disabled>Simulate invest.js Load</button>
        <div id="frontendResult"></div>
    </div>

    <script>
        // Copy the exact apiCall function from invest.js
        async function apiCall(endpoint, data = {}) {
            try {
                const response = await fetch('frontend/ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: endpoint,
                        ...data
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        }

        function addResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const div = document.createElement('div');
            div.className = `section ${type}`;
            div.innerHTML = message;
            element.appendChild(div);
        }

        async function testAuth() {
            try {
                addResult('authResult', 'Testing authentication...', 'info');
                
                const result = await apiCall('login', {
                    email: '<EMAIL>',
                    password: 'password123'
                });
                
                addResult('authResult', `<h3>Login Response:</h3><pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                
                if (result.success) {
                    addResult('authResult', '✅ Authentication successful!', 'success');
                    document.getElementById('balanceBtn').disabled = false;
                    document.getElementById('frontendBtn').disabled = false;
                } else {
                    addResult('authResult', `❌ Authentication failed: ${result.error || result.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                addResult('authResult', `❌ Authentication error: ${error.message}`, 'error');
            }
        }

        async function testBalanceAPI() {
            try {
                addResult('balanceResult', 'Testing balance API...', 'info');
                
                const result = await apiCall('get_balance');
                
                addResult('balanceResult', `<h3>Balance API Response:</h3><pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                
                if (result.success) {
                    const balance = parseFloat(result.balance) || 0;
                    addResult('balanceResult', `✅ Balance API successful! Balance: ${balance} USDT`, 'success');
                    
                    if (balance === 30) {
                        addResult('balanceResult', '🎯 Balance is correct! (630 wallet - 600 investments = 30)', 'success');
                    } else {
                        addResult('balanceResult', `⚠️ Balance might be incorrect. Expected: 30, Got: ${balance}`, 'error');
                    }
                } else {
                    addResult('balanceResult', `❌ Balance API failed: ${result.error || result.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                addResult('balanceResult', `❌ Balance API error: ${error.message}`, 'error');
            }
        }

        async function simulateFrontendLoad() {
            try {
                addResult('frontendResult', 'Simulating loadUserBalance() from invest.js...', 'info');
                
                // Copy the exact logic from invest.js loadUserBalance function
                const response = await apiCall('get_balance');
                
                addResult('frontendResult', `<h3>Frontend Logic Response:</h3><pre>${JSON.stringify(response, null, 2)}</pre>`, 'info');
                
                if (response.success) {
                    const userBalance = parseFloat(response.balance) || 0;
                    addResult('frontendResult', `✅ Frontend logic successful! Parsed balance: ${userBalance}`, 'success');
                    
                    // Simulate updating the balance element
                    addResult('frontendResult', `Balance would be displayed as: ${userBalance.toFixed(2)} USDT`, 'success');
                } else {
                    const errorMsg = response.error || response.message || 'Unknown error';
                    addResult('frontendResult', `❌ Frontend logic error: ${errorMsg}`, 'error');
                    addResult('frontendResult', 'This would cause the "undefined" error you saw', 'error');
                }
            } catch (error) {
                addResult('frontendResult', `❌ Frontend simulation error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
