<?php
/**
 * TronGrid Service for Payment Confirmation API
 * Handles interaction with TronGrid API for transaction verification
 */

class TronGridService {
    private $baseUrl;
    private $apiKey;
    private $usdtContract;
    
    public function __construct() {
        $this->baseUrl = TRONGRID_BASE_URL;
        $this->apiKey = TRON_GRID_API_KEY;
        $this->usdtContract = USDT_CONTRACT;
    }
    
    /**
     * Verify USDT transaction for a wallet address
     */
    public function verifyTransaction($walletAddress, $expectedAmount, $transactionHash = null) {
        try {
            // Get recent TRC20 transactions for the wallet
            $transactions = $this->getWalletTransactions($walletAddress);
            
            if (empty($transactions)) {
                return [
                    'success' => false,
                    'verified' => false,
                    'message' => 'No transactions found for wallet address'
                ];
            }
            
            // Find matching transaction
            $matchingTx = $this->findMatchingTransaction($transactions, $expectedAmount, $transactionHash);
            
            if ($matchingTx) {
                return [
                    'success' => true,
                    'verified' => true,
                    'transaction' => $matchingTx,
                    'message' => 'Transaction verified successfully'
                ];
            }
            
            return [
                'success' => false,
                'verified' => false,
                'message' => 'No matching transaction found',
                'searched_amount' => $expectedAmount,
                'searched_hash' => $transactionHash
            ];
            
        } catch (Exception $e) {
            error_log("TronGrid verification error: " . $e->getMessage());
            return [
                'success' => false,
                'verified' => false,
                'message' => 'Error verifying transaction: ' . $e->getMessage()
            ];
        }
    }
      /**
     * Get TRC20 transactions for a wallet address
     */
    public function getWalletTransactions($walletAddress, $limit = 50) {
        $url = $this->baseUrl . "/v1/accounts/{$walletAddress}/transactions/trc20";
        $params = [
            'limit' => $limit,
            'contract_address' => $this->usdtContract,
            'order_by' => 'block_timestamp,desc'
        ];
        
        return $this->makeApiCall($url, $params);
    }
    
    /**
     * Find matching transaction in the list
     */
    private function findMatchingTransaction($transactions, $expectedAmount, $expectedHash = null) {
        $expectedAmountRaw = $this->convertToRawAmount($expectedAmount);
        $cutoffTime = (time() - (TRANSACTION_TIMEOUT_HOURS * 3600)) * 1000; // Convert to milliseconds
        
        foreach ($transactions as $tx) {
            // Skip old transactions
            if (isset($tx['block_timestamp']) && $tx['block_timestamp'] < $cutoffTime) {
                continue;
            }
            
            // If hash provided, match exactly
            if ($expectedHash && isset($tx['transaction_id'])) {
                if ($tx['transaction_id'] === $expectedHash && $this->matchesAmount($tx, $expectedAmountRaw)) {
                    return $this->formatTransaction($tx);
                }
                continue;
            }
            
            // Match by amount and validation criteria
            if ($this->matchesAmount($tx, $expectedAmountRaw) && $this->isValidDeposit($tx)) {
                return $this->formatTransaction($tx);
            }
        }
        
        return null;
    }
    
    /**
     * Check if transaction amount matches expected amount
     */
    private function matchesAmount($tx, $expectedAmountRaw) {
        if (!isset($tx['value'])) {
            return false;
        }
        
        $txAmount = (float)$tx['value'];
        $tolerance = AMOUNT_TOLERANCE * 1000000; // Convert tolerance to raw amount
        
        return abs($txAmount - (float)$expectedAmountRaw) <= $tolerance;
    }
    
    /**
     * Validate if transaction is a valid USDT deposit
     */
    private function isValidDeposit($tx) {
        // Check if confirmed
        if (!isset($tx['confirmed']) || !$tx['confirmed']) {
            return false;
        }
        
        // Check if it's USDT
        if (!isset($tx['token_info']['symbol']) || $tx['token_info']['symbol'] !== 'USDT') {
            return false;
        }
        
        // Check minimum confirmations
        $confirmations = $this->calculateConfirmations($tx);
        if ($confirmations < MIN_CONFIRMATIONS) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Convert USDT amount to raw format (6 decimals)
     */
    private function convertToRawAmount($amount) {
        return (string)($amount * 1000000);
    }
    
    /**
     * Format transaction for response
     */
    private function formatTransaction($tx) {
        $amount = isset($tx['value']) ? (float)$tx['value'] / 1000000 : 0;
        
        return [
            'transaction_id' => $tx['transaction_id'] ?? '',
            'from_address' => $tx['from'] ?? '',
            'to_address' => $tx['to'] ?? '',
            'amount' => $amount,
            'amount_raw' => $tx['value'] ?? '0',
            'block_number' => $tx['block'] ?? 0,
            'block_timestamp' => $tx['block_timestamp'] ?? 0,
            'confirmed' => $tx['confirmed'] ?? false,
            'confirmations' => $this->calculateConfirmations($tx),
            'token_symbol' => $tx['token_info']['symbol'] ?? 'USDT',
            'contract_address' => $tx['token_info']['address'] ?? $this->usdtContract
        ];
    }
    
    /**
     * Calculate confirmation count based on time
     */
    private function calculateConfirmations($tx) {
        if (!isset($tx['block_timestamp']) || !$tx['confirmed']) {
            return 0;
        }
        
        // TRON blocks are approximately 3 seconds
        $blockTime = 3;
        $timeDiff = time() - ($tx['block_timestamp'] / 1000);
        
        return max(0, (int)($timeDiff / $blockTime));
    }
    
    /**
     * Get transaction by hash
     */
    public function getTransactionByHash($txHash) {
        try {
            $url = $this->baseUrl . "/v1/transactions/{$txHash}";
            $response = $this->makeApiCall($url);
            
            if ($response && isset($response[0])) {
                return $response[0];
            }
            
            return null;
        } catch (Exception $e) {
            error_log("TronGrid transaction lookup error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Make API call to TronGrid
     */
    private function makeApiCall($url, $params = []) {
        $fullUrl = $url;
        if (!empty($params)) {
            $fullUrl .= '?' . http_build_query($params);
        }
        
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json'
        ];
        
        // Add API key if available
        if (!empty($this->apiKey)) {
            $headers[] = 'TRON-PRO-API-KEY: ' . $this->apiKey;
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $fullUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => TRONGRID_TIMEOUT,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_USERAGENT => 'TLS-Payment-Confirmation/1.0'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("cURL error: " . $error);
        }
        
        if ($httpCode !== 200) {
            throw new Exception("TronGrid API returned HTTP {$httpCode}: " . $response);
        }
        
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response from TronGrid");
        }
        
        return $data['data'] ?? $data;
    }    /**
     * Health check for TronGrid API
     */
    public function healthCheck() {
        try {
            // Use a simple API call to test connectivity with a known valid TRON address
            $url = $this->baseUrl . "/v1/accounts/TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t";
            $this->makeApiCall($url);
            
            return [
                'status' => 'healthy',
                'api_accessible' => true,
                'timestamp' => date('c')
            ];
        } catch (Exception $e) {
            // If we get a response (even error), API is accessible
            if (strpos($e->getMessage(), 'HTTP') !== false) {
                return [
                    'status' => 'healthy',
                    'api_accessible' => true,
                    'timestamp' => date('c'),
                    'note' => 'API accessible but returned error (expected for health check)'
                ];
            }
            
            return [
                'status' => 'error',
                'api_accessible' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('c')
            ];
        }
    }
}
