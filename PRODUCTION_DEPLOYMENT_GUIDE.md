# 🚀 TLS Wallet - Production Deployment Guide

## ✅ Cleanup Status: COMPLETE
**Date:** June 15, 2025  
**Files Removed:** 90+ development/test files  
**Backup Location:** `dev_files_backup_2025-06-15_01-37-40/`

---

## 📁 Production File Structure

```
tls/
├── .git/                    # Version control
├── .gitignore              # Git ignore rules
├── backend/                # API and business logic
├── cronJob/                # Scheduled tasks
├── frontend/               # User interface
├── deploy-production.ps1   # Deployment script
└── PRODUCTION_READY_*.md   # This guide
```

---

## 🔧 Pre-Deployment Configuration

### 1. Database Configuration
- [ ] Update database connection strings in `backend/config/`
- [ ] Verify database schema is up to date
- [ ] Test database connectivity
- [ ] Set up database backups

### 2. Environment Variables
- [ ] Set `ENVIRONMENT=production`
- [ ] Configure API keys and secrets
- [ ] Update CORS settings
- [ ] Set proper error reporting levels

### 3. Security Settings
- [ ] Enable HTTPS/SSL certificates
- [ ] Set secure session configurations
- [ ] Configure proper file permissions (644 for files, 755 for directories)
- [ ] Review and secure sensitive configuration files

### 4. Frontend Configuration
- [ ] Update API endpoints in frontend files
- [ ] Minify CSS and JavaScript files (optional)
- [ ] Configure CDN settings if applicable
- [ ] Test responsive design on various devices

---

## 🌐 Web Server Configuration

### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=63072000"
```

### Nginx (example)
```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    root /path/to/tls/frontend;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        try_files $uri $uri/ /index.php;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
    }
}
```

---

## 🧪 Production Testing Checklist

### Functionality Tests
- [ ] User registration and login
- [ ] Dashboard displays correctly
- [ ] Investment plan selection
- [ ] Investment creation and processing
- [ ] Balance calculations
- [ ] Investment history and active investments
- [ ] User profile management
- [ ] Logout functionality

### Security Tests
- [ ] SQL injection protection
- [ ] XSS prevention
- [ ] CSRF protection
- [ ] Session security
- [ ] File upload restrictions
- [ ] Rate limiting

### Performance Tests
- [ ] Page load times < 3 seconds
- [ ] Database query optimization
- [ ] Cache implementation (if applicable)
- [ ] Mobile responsiveness
- [ ] Cross-browser compatibility

---

## 📊 Monitoring Setup

### Error Logging
- [ ] Configure PHP error logs
- [ ] Set up application error tracking
- [ ] Monitor database errors
- [ ] Set up email alerts for critical errors

### Performance Monitoring
- [ ] Server resource usage
- [ ] Database performance
- [ ] User activity tracking
- [ ] Uptime monitoring

---

## 🔄 Backup Strategy

### Daily Backups
- [ ] Database backups
- [ ] File system backups
- [ ] Configuration backups

### Recovery Plan
- [ ] Test backup restoration
- [ ] Document recovery procedures
- [ ] Set up redundancy

---

## 🚀 Deployment Steps

### 1. Pre-deployment
```bash
# Backup current production (if updating)
cp -r /var/www/html /var/www/html_backup_$(date +%Y%m%d)

# Upload files to server
rsync -avz --exclude '.git' ./tls/ user@server:/var/www/html/
```

### 2. Configuration
```bash
# Set file permissions
find /var/www/html -type f -exec chmod 644 {} \;
find /var/www/html -type d -exec chmod 755 {} \;

# Secure sensitive files
chmod 600 /var/www/html/backend/config/database.php
```

### 3. Database Setup
```sql
-- Run any pending migrations
-- Update configuration tables
-- Test connectivity
```

### 4. Service Restart
```bash
# Restart web server
sudo systemctl restart apache2  # or nginx
sudo systemctl restart php-fpm  # if using php-fpm

# Restart any background services
sudo systemctl restart cron
```

---

## ✅ Post-Deployment Verification

### Immediate Checks (0-30 minutes)
- [ ] Site loads without errors
- [ ] All pages accessible
- [ ] Database connectivity working
- [ ] SSL certificate valid
- [ ] Basic functionality working

### Extended Monitoring (24-48 hours)
- [ ] Monitor error logs
- [ ] Check performance metrics
- [ ] Verify scheduled tasks (cron jobs)
- [ ] Test with real users
- [ ] Monitor server resources

---

## 🆘 Rollback Plan

If issues occur:
1. **Immediate:** Switch to maintenance mode
2. **Database:** Restore from latest backup
3. **Files:** Restore from backup directory
4. **Services:** Restart all services
5. **Verify:** Test core functionality
6. **Monitor:** Watch error logs for 24 hours

---

## 📞 Support Information

**Emergency Contacts:**
- Technical Lead: [Contact Info]
- Database Admin: [Contact Info]
- Server Admin: [Contact Info]

**Important URLs:**
- Production Site: [URL]
- Admin Panel: [URL]
- Monitoring Dashboard: [URL]

---

## 📝 Change Log

| Date | Version | Changes | Deployed By |
|------|---------|---------|-------------|
| 2025-06-15 | 1.0.0 | Initial production release | [Name] |

---

**🎉 Ready for Production Deployment!**

*Last Updated: June 15, 2025*
