-- Investment Plans Table Creation
-- This creates a dedicated table for managing investment plans

CREATE TABLE IF NOT EXISTS investment_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    plan_code VARCHAR(50) NOT NULL UNIQUE,
    plan_name VARCHAR(100) NOT NULL,
    daily_rate DECIMAL(5, 4) NOT NULL,
    duration INT NOT NULL DEFAULT 30,
    min_amount DECIMAL(20, 8) NOT NULL,
    max_amount DECIMAL(20, 8) NULL,
    description TEXT NULL,
    features JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_plan_code (plan_code),
    INDEX idx_is_active (is_active),
    INDEX idx_display_order (display_order)
) ENGINE=InnoDB COMMENT='Investment plan configurations';

-- Insert default investment plans
INSERT INTO investment_plans (plan_code, plan_name, daily_rate, duration, min_amount, max_amount, description, features, is_active, is_featured, display_order) VALUES
('basic', 'Basic Plan', 0.0500, 30, 600.00000000, NULL, 'Perfect for beginners with steady daily returns', 
 JSON_ARRAY('Daily returns', '30-day duration', 'Low risk', 'Beginner friendly'), TRUE, TRUE, 1),
('premium', 'Premium Plan', 0.0700, 30, 2000.00000000, NULL, 'Enhanced returns for experienced investors', 
 JSON_ARRAY('Higher daily returns', '30-day duration', 'Medium risk', 'Advanced features'), FALSE, FALSE, 2),
('vip', 'VIP Plan', 0.1000, 30, 5000.00000000, NULL, 'Maximum returns for high-value investors', 
 JSON_ARRAY('Highest daily returns', '30-day duration', 'Premium service', 'Maximum profits'), FALSE, FALSE, 3);

-- Update existing investments to use plan_code if needed
-- ALTER TABLE investments ADD COLUMN plan_code VARCHAR(50) AFTER plan_type;
-- UPDATE investments SET plan_code = plan_type WHERE plan_code IS NULL;
