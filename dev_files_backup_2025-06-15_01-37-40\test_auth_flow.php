<?php
// Simple test to check session persistence and API authentication
require_once 'frontend/config.php';
require_once 'frontend/api.php';

// Initialize session
FrontendConfig::initSession();

echo "<h2>Authentication Flow Test</h2>";

// Step 1: Check current session
echo "<h3>Step 1: Current Session State</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Token: " . ($_SESSION['token'] ?? 'NOT SET') . "<br>";
echo "User ID: " . ($_SESSION['user_id'] ?? 'NOT SET') . "<br>";
echo "Email: " . ($_SESSION['email'] ?? 'NOT SET') . "<br>";

// Step 2: Test API authentication
echo "<h3>Step 2: API Authentication Test</h3>";
$api = new APIWrapper();

if (isset($_SESSION['token'])) {
    echo "Token is set, testing API call...<br>";
    
    // Test get balance API call
    $result = $api->getBalance();
    echo "Balance API Result: " . json_encode($result, JSON_PRETTY_PRINT) . "<br>";
} else {
    echo "No token found - user needs to login first<br>";
}

// Step 3: Test investment creation if authenticated
if (isset($_SESSION['token'])) {
    echo "<h3>Step 3: Investment Creation Test</h3>";
    
    // Test with small investment
    $investmentResult = $api->createInvestment(10, 'basic');
    echo "Investment API Result: " . json_encode($investmentResult, JSON_PRETTY_PRINT) . "<br>";
}

echo "<h3>Debug Info</h3>";
echo "API Base URL: " . FrontendConfig::getApiUrl() . "<br>";
echo "Session Path: " . session_save_path() . "<br>";
echo "Session Cookie Params: " . json_encode(session_get_cookie_params()) . "<br>";
?>
