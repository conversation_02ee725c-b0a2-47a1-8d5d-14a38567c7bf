<?php

namespace Simbi\Tls\Services;

use Simbi\Tls\Repositories\TransactionRepository;
use Simbi\Tls\Repositories\WalletRepository;
use Exception;

class TransactionService
{
    private TransactionRepository $transactionRepository;
    private WalletRepository $walletRepository;

    public function __construct(
        TransactionRepository $transactionRepository,
        WalletRepository $walletRepository
    ) {
        $this->transactionRepository = $transactionRepository;
        $this->walletRepository = $walletRepository;
    }

    public function findByHash(string $transactionHash): ?array
    {
        try {
            return $this->transactionRepository->findByHash($transactionHash);
        } catch (Exception $e) {
            error_log("Find transaction by hash error: " . $e->getMessage());
            return null;
        }
    }

    public function createTransaction(array $data): ?int
    {
        try {
            // Validate required fields
            if (!isset($data['user_id'], $data['wallet_id'], $data['transaction_hash'], $data['type'], $data['amount'])) {
                throw new Exception("Missing required transaction fields");
            }            // Create transaction
            $transactionId = $this->transactionRepository->create($data);

            return $transactionId;
        } catch (Exception $e) {
            error_log("Transaction creation error: " . $e->getMessage());
            return null;
        }
    }

    public function getUserTransactionHistory(int $userId, array $filters = []): array
    {
        try {
            $limit = $filters['limit'] ?? 50;
            $offset = $filters['offset'] ?? 0;
            $type = $filters['type'] ?? null;
            $status = $filters['status'] ?? null;
            $dateFrom = $filters['date_from'] ?? null;
            $dateTo = $filters['date_to'] ?? null;

            // Validate limits
            $limit = min(max($limit, 1), 100); // Between 1 and 100
            $offset = max($offset, 0);

            // Get transactions
            $transactions = $this->transactionRepository->findByUserId($userId, $limit, $offset);

            // Apply additional filters if needed
            if ($type || $status || $dateFrom || $dateTo) {
                $transactions = $this->applyFilters($transactions, [
                    'type' => $type,
                    'status' => $status,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo
                ]);
            }

            // Get statistics
            $stats = $this->transactionRepository->getStatistics($userId);

            return [
                'success' => true,
                'transactions' => $transactions,
                'statistics' => $stats,
                'pagination' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'has_more' => count($transactions) === $limit
                ]
            ];
        } catch (Exception $e) {
            error_log("Transaction history error: " . $e->getMessage());
            return ['error' => 'Failed to fetch transaction history', 'code' => 500];
        }
    }

    public function getTransactionDetails(int $transactionId, int $userId): array
    {
        try {
            $transaction = $this->transactionRepository->findById($transactionId);

            if (!$transaction) {
                return ['error' => 'Transaction not found', 'code' => 404];
            }

            // Verify transaction belongs to user
            if ($transaction['user_id'] != $userId) {
                return ['error' => 'Access denied', 'code' => 403];
            }

            return [
                'success' => true,
                'transaction' => $transaction
            ];
        } catch (Exception $e) {
            error_log("Transaction details error: " . $e->getMessage());
            return ['error' => 'Failed to fetch transaction details', 'code' => 500];
        }
    }

    public function updateTransactionStatus(int $transactionId, string $status, array $updates = []): bool
    {
        try {
            $validStatuses = ['pending', 'confirmed', 'failed'];
            if (!in_array($status, $validStatuses)) {
                throw new Exception("Invalid transaction status: $status");
            }            // Update transaction
            $success = $this->transactionRepository->updateStatus($transactionId, $status, $updates);

            return $success;
        } catch (Exception $e) {
            error_log("Transaction status update error: " . $e->getMessage());
            return false;
        }
    }

    public function recordDeposit(int $userId, string $walletAddress, string $txHash, 
                                 float $amount, string $fromAddress = null): array
    {
        try {
            // Find wallet
            $wallet = $this->walletRepository->findByAddress($walletAddress);
            if (!$wallet) {
                return ['error' => 'Wallet not found', 'code' => 404];
            }

            // Verify wallet belongs to user
            if ($wallet['user_id'] != $userId) {
                return ['error' => 'Wallet access denied', 'code' => 403];
            }

            // Check if transaction already exists
            $existingTx = $this->transactionRepository->findByHash($txHash);
            if ($existingTx) {
                return ['error' => 'Transaction already recorded', 'code' => 409];
            }

            // Create deposit transaction
            $transactionId = $this->createTransaction([
                'user_id' => $userId,
                'wallet_id' => $wallet['id'],
                'transaction_hash' => $txHash,
                'type' => 'deposit',
                'amount' => $amount,
                'from_address' => $fromAddress,
                'to_address' => $walletAddress,
                'status' => 'confirmed'
            ]);

            if (!$transactionId) {
                return ['error' => 'Failed to record deposit', 'code' => 500];
            }

            // Update wallet balance
            $newBalance = $wallet['balance'] + $amount;
            $this->walletRepository->updateBalance($wallet['id'], $newBalance);

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'message' => 'Deposit recorded successfully'
            ];
        } catch (Exception $e) {
            error_log("Deposit recording error: " . $e->getMessage());
            return ['error' => 'Failed to record deposit', 'code' => 500];
        }
    }

    public function getTransactionsByWallet(int $walletId, int $userId, int $limit = 50, int $offset = 0): array
    {
        try {
            // Verify wallet belongs to user
            $wallet = $this->walletRepository->findById($walletId);
            if (!$wallet || $wallet['user_id'] != $userId) {
                return ['error' => 'Wallet not found or access denied', 'code' => 404];
            }

            $transactions = $this->transactionRepository->findByWalletId($walletId, $limit, $offset);

            return [
                'success' => true,
                'transactions' => $transactions,
                'wallet' => [
                    'id' => $wallet['id'],
                    'address' => $wallet['address'],
                    'balance' => $wallet['balance']
                ],
                'pagination' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'has_more' => count($transactions) === $limit
                ]
            ];
        } catch (Exception $e) {
            error_log("Wallet transactions error: " . $e->getMessage());
            return ['error' => 'Failed to fetch wallet transactions', 'code' => 500];
        }
    }

    private function applyFilters(array $transactions, array $filters): array
    {
        return array_filter($transactions, function($transaction) use ($filters) {
            // Filter by type
            if ($filters['type'] && $transaction['type'] !== $filters['type']) {
                return false;
            }

            // Filter by status
            if ($filters['status'] && $transaction['status'] !== $filters['status']) {
                return false;
            }

            // Filter by date range
            if ($filters['date_from']) {
                $txDate = strtotime($transaction['created_at']);
                $fromDate = strtotime($filters['date_from']);
                if ($txDate < $fromDate) {
                    return false;
                }
            }

            if ($filters['date_to']) {
                $txDate = strtotime($transaction['created_at']);
                $toDate = strtotime($filters['date_to'] . ' 23:59:59');
                if ($txDate > $toDate) {
                    return false;
                }
            }

            return true;
        });
    }

    public function getSystemTransactionStats(): array
    {
        try {
            return [
                'success' => true,
                'statistics' => $this->transactionRepository->getStatistics()
            ];
        } catch (Exception $e) {
            error_log("System transaction stats error: " . $e->getMessage());
            return ['error' => 'Failed to fetch system statistics', 'code' => 500];
        }
    }
}
