<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Plan Authentication Fix Verification</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f8f9fa;
            max-width: 1200px;
            margin: 0 auto;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .btn { 
            padding: 12px 24px; 
            margin: 8px; 
            border: none; 
            border-radius: 6px; 
            cursor: pointer; 
            background: #007bff; 
            color: white;
            font-weight: 500;
        }
        .btn:hover { background: #0056b3; }
        .btn:disabled { 
            background: #ccc; 
            cursor: not-allowed; 
        }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .result { 
            margin: 15px 0; 
            padding: 15px; 
            border-radius: 6px; 
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .loading { background: #e2e3e5; color: #495057; border: 1px solid #ced4da; }
        .highlight { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0;
            text-align: center;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        .status-card.success { border-color: #28a745; }
        .status-card.error { border-color: #dc3545; }
        .status-icon { font-size: 24px; margin-bottom: 8px; }
        .status-text { font-weight: 600; }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="highlight">
            <h1>🛠️ Active Plan Authentication Fix Verification</h1>
            <p>Complete end-to-end testing of the active plan page authentication fix</p>
        </div>

        <div class="test-section">
            <h3>📋 Fix Summary</h3>
            <div class="result info">WHAT WAS FIXED:
❌ Problem: Active plan page was redirecting to login instead of showing content
🔧 Root Cause: Page wasn't using proper header include pattern like other pages
✅ Solution: Restructured active_plan.php to use include '../includes/header.php'

CHANGES MADE:
1. Fixed header variable setup ($pageTitle, $currentPage, $basePath, $cssPath)
2. Replaced custom HTML structure with proper header include
3. Removed duplicate HTML tags and header includes
4. Ensured consistent authentication pattern with dashboard.php and other pages</div>
        </div>

        <div class="test-section">
            <h3>Step 1: Authentication Test</h3>
            <button class="btn" onclick="testAuthentication()">🔐 Test Login & Session</button>
            <div id="authResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 2: Active Plan Page Access Test</h3>
            <div>
                <input type="text" id="investmentId" placeholder="Investment ID" value="1" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <button class="btn" onclick="testPageAccess()">📄 Test Page Access</button>
                <button class="btn btn-warning" onclick="openPageDirect()">🔗 Open Page Directly</button>
            </div>
            <div id="pageResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 3: API Functionality Test</h3>
            <button class="btn" onclick="testAPIs()">🔌 Test Investment APIs</button>
            <div id="apiResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 4: Navigation Test</h3>
            <button class="btn" onclick="testNavigation()">🧭 Test Dashboard → Active Plan Navigation</button>
            <div id="navResult"></div>
        </div>

        <div class="test-section">
            <h3>Step 5: Page Preview</h3>
            <button class="btn btn-success" onclick="showPagePreview()">👁️ Preview Active Plan Page</button>
            <div id="previewContainer"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Test Results Summary</h3>
            <div class="status-grid" id="statusGrid">
                <div class="status-card" id="authStatus">
                    <div class="status-icon">⏳</div>
                    <div class="status-text">Authentication</div>
                    <div>Not Tested</div>
                </div>
                <div class="status-card" id="pageStatus">
                    <div class="status-icon">⏳</div>
                    <div class="status-text">Page Access</div>
                    <div>Not Tested</div>
                </div>
                <div class="status-card" id="apiStatus">
                    <div class="status-icon">⏳</div>
                    <div class="status-text">APIs</div>
                    <div>Not Tested</div>
                </div>
                <div class="status-card" id="navStatus">
                    <div class="status-icon">⏳</div>
                    <div class="status-text">Navigation</div>
                    <div>Not Tested</div>
                </div>
            </div>
            <div id="overallResult"></div>
        </div>
    </div>

    <script>
        let testResults = {
            auth: false,
            page: false,
            apis: false,
            navigation: false
        };

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function updateStatus(testName, success, details = '') {
            const statusCard = document.getElementById(testName + 'Status');
            if (statusCard) {
                statusCard.className = `status-card ${success ? 'success' : 'error'}`;
                statusCard.innerHTML = `
                    <div class="status-icon">${success ? '✅' : '❌'}</div>
                    <div class="status-text">${testName.charAt(0).toUpperCase() + testName.slice(1)}</div>
                    <div>${success ? 'Working' : 'Failed'}</div>
                `;
            }
            
            testResults[testName] = success;
            updateOverallResult();
        }

        function updateOverallResult() {
            const allPassed = Object.values(testResults).every(result => result === true);
            const anyTested = Object.values(testResults).some(result => result !== false);
            
            if (!anyTested) return;
            
            if (allPassed) {
                showResult('overallResult', 
                    `🎉 ALL TESTS PASSED!

The active plan page authentication fix is working correctly:
✅ Users can login and establish sessions
✅ Active plan page loads without redirecting to login  
✅ Investment APIs work and return data
✅ Navigation from dashboard works seamlessly

The issue reported in the conversation summary has been RESOLVED!
Users should now be able to:
• Click on investment items from dashboard or investment pages
• Navigate to active plan page without authentication errors
• View investment details and earnings history
• See countdown timers and progress tracking`, 
                    'success'
                );
            } else {
                const passedTests = Object.entries(testResults).filter(([_, passed]) => passed).map(([name, _]) => name);
                const failedTests = Object.entries(testResults).filter(([_, passed]) => !passed && passed !== false).map(([name, _]) => name);
                
                showResult('overallResult', 
                    `⚠️ PARTIAL SUCCESS

Passed: ${passedTests.join(', ')}
Failed: ${failedTests.join(', ')}

Some components are working but the fix may need further adjustment.`, 
                    'warning'
                );
            }
        }

        async function testAuthentication() {
            try {
                showResult('authResult', '🔄 Testing authentication flow...', 'loading');
                
                // Test login
                const loginResponse = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                const loginResult = await loginResponse.json();
                
                if (!loginResult.success) {
                    updateStatus('auth', false);
                    showResult('authResult', `❌ LOGIN FAILED: ${loginResult.error}`, 'error');
                    return;
                }

                // Test session verification
                const balanceResponse = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'get_balance' })
                });

                const balanceResult = await balanceResponse.json();
                
                if (balanceResult.success) {
                    updateStatus('auth', true);
                    showResult('authResult', 
                        `✅ AUTHENTICATION WORKING

Login Status: ✅ Success
User ID: ${loginResult.user?.id}
Email: ${loginResult.user?.email}
Token: ${loginResult.token ? 'Present' : 'Missing'}
Session Check: ✅ Session established
Balance API: ✅ ${balanceResult.balance} USDT

Authentication is working correctly!`, 
                        'success'
                    );
                } else {
                    updateStatus('auth', false);
                    showResult('authResult', 
                        `❌ SESSION VERIFICATION FAILED

Login worked but session authentication failed:
Error: ${balanceResult.error}

This indicates a session persistence issue.`, 
                        'error'
                    );
                }
                
            } catch (error) {
                updateStatus('auth', false);
                showResult('authResult', `❌ AUTHENTICATION TEST ERROR: ${error.message}`, 'error');
            }
        }

        async function testPageAccess() {
            const investmentId = document.getElementById('investmentId').value;
            
            try {
                showResult('pageResult', '🔄 Testing active plan page access...', 'loading');
                
                const response = await fetch(`/user/active_plan.php?id=${investmentId}`, {
                    method: 'GET',
                    credentials: 'same-origin'
                });
                
                const text = await response.text();
                const finalUrl = response.url;
                
                // Check for successful page load
                if (text.includes('Active Investment Plan') || text.includes('active-plan-container')) {
                    updateStatus('page', true);
                    showResult('pageResult', 
                        `✅ ACTIVE PLAN PAGE ACCESS SUCCESSFUL

URL: ${finalUrl}
Status: ${response.status}
Content Length: ${text.length} characters

The page loaded correctly without redirecting to login!
✓ Authentication check passed
✓ Investment ID parameter accepted  
✓ Page content rendered properly`, 
                        'success'
                    );
                } else if (finalUrl.includes('index.php') || text.includes('login')) {
                    updateStatus('page', false);
                    showResult('pageResult', 
                        `❌ PAGE ACCESS DENIED - REDIRECTED TO LOGIN

Final URL: ${finalUrl}
Status: ${response.status}

The page is still redirecting to login.
This means the authentication fix needs further work.`, 
                        'error'
                    );
                } else {
                    updateStatus('page', false);
                    showResult('pageResult', 
                        `⚠️ UNEXPECTED PAGE RESPONSE

URL: ${finalUrl}
Status: ${response.status}
Content: ${text.substring(0, 200)}...

Page didn't redirect to login but content is unexpected.`, 
                        'warning'
                    );
                }
                
            } catch (error) {
                updateStatus('page', false);
                showResult('pageResult', `❌ PAGE ACCESS ERROR: ${error.message}`, 'error');
            }
        }

        async function testAPIs() {
            const investmentId = document.getElementById('investmentId').value;
            
            try {
                showResult('apiResult', '🔄 Testing investment APIs...', 'loading');
                
                // Test investment details API
                const detailsResponse = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_investment_details',
                        id: investmentId
                    })
                });

                const detailsResult = await detailsResponse.json();
                
                // Test investment earnings API
                const earningsResponse = await fetch('/ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'get_investment_earnings',
                        id: investmentId
                    })
                });

                const earningsResult = await earningsResponse.json();
                
                if (detailsResult.success && earningsResult.success) {
                    updateStatus('apis', true);
                    showResult('apiResult', 
                        `✅ INVESTMENT APIs WORKING

Investment Details API:
Status: ✅ Success
Investment ID: ${detailsResult.investment?.id || 'N/A'}
Amount: ${detailsResult.investment?.amount || 'N/A'}
Plan: ${detailsResult.investment?.plan_name || 'N/A'}

Investment Earnings API:  
Status: ✅ Success
Earnings Records: ${earningsResult.earnings?.length || 0}

Both APIs are working correctly!`, 
                        'success'
                    );
                } else {
                    updateStatus('apis', false);
                    showResult('apiResult', 
                        `❌ INVESTMENT APIs FAILED

Investment Details API:
Status: ${detailsResult.success ? '✅' : '❌'}
Error: ${detailsResult.error || 'None'}

Investment Earnings API:
Status: ${earningsResult.success ? '✅' : '❌'}  
Error: ${earningsResult.error || 'None'}

API authentication or backend integration issues detected.`, 
                        'error'
                    );
                }
                
            } catch (error) {
                updateStatus('apis', false);
                showResult('apiResult', `❌ API TEST ERROR: ${error.message}`, 'error');
            }
        }

        async function testNavigation() {
            try {
                showResult('navResult', '🔄 Testing navigation flow...', 'loading');
                
                // Test dashboard access
                const dashboardResponse = await fetch('/user/dashboard.php');
                const dashboardOk = dashboardResponse.ok && !dashboardResponse.url.includes('index.php');
                
                // Test active plan access
                const activePlanResponse = await fetch('/user/active_plan.php?id=1');
                const activePlanOk = activePlanResponse.ok && !activePlanResponse.url.includes('index.php');
                
                if (dashboardOk && activePlanOk) {
                    updateStatus('navigation', true);
                    showResult('navResult', 
                        `✅ NAVIGATION FLOW WORKING

Dashboard Access: ✅ ${dashboardResponse.status}
Dashboard URL: ${dashboardResponse.url}

Active Plan Access: ✅ ${activePlanResponse.status}  
Active Plan URL: ${activePlanResponse.url}

Users can successfully navigate:
Dashboard → Active Plan without authentication issues!`, 
                        'success'
                    );
                } else {
                    updateStatus('navigation', false);
                    showResult('navResult', 
                        `❌ NAVIGATION FLOW BROKEN

Dashboard Access: ${dashboardOk ? '✅' : '❌'} (${dashboardResponse.status})
Active Plan Access: ${activePlanOk ? '✅' : '❌'} (${activePlanResponse.status})

Navigation flow has authentication issues.`, 
                        'error'
                    );
                }
                
            } catch (error) {
                updateStatus('navigation', false);
                showResult('navResult', `❌ NAVIGATION TEST ERROR: ${error.message}`, 'error');
            }
        }

        function openPageDirect() {
            const investmentId = document.getElementById('investmentId').value;
            window.open(`/user/active_plan.php?id=${investmentId}`, '_blank');
        }

        function showPagePreview() {
            const investmentId = document.getElementById('investmentId').value;
            const container = document.getElementById('previewContainer');
            
            container.innerHTML = `
                <h4>Active Plan Page Preview:</h4>
                <iframe src="/user/active_plan.php?id=${investmentId}"></iframe>
                <p><strong>Note:</strong> If you see the login page in the iframe above, the authentication fix needs more work. If you see the active plan content, the fix is working!</p>
            `;
        }

        // Initialize
        console.log('Active Plan Authentication Fix Verification Tool loaded');
        console.log('Run tests in order: Authentication → Page Access → APIs → Navigation');
    </script>
</body>
</html>
