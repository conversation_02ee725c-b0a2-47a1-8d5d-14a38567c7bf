#!/bin/bash

# TLS Crypto Wallet Frontend Deployment Script
# This script helps deploy the frontend to a web server

set -e

echo "=================================="
echo "TLS Crypto Wallet Frontend Setup"
echo "=================================="

# Configuration
DEFAULT_API_URL="http://localhost:3000"
DEFAULT_ENVIRONMENT="development"

# Get user input
read -p "Enter the backend API URL [$DEFAULT_API_URL]: " API_URL
API_URL=${API_URL:-$DEFAULT_API_URL}

read -p "Enter the environment (development/staging/production) [$DEFAULT_ENVIRONMENT]: " ENVIRONMENT
ENVIRONMENT=${ENVIRONMENT:-$DEFAULT_ENVIRONMENT}

read -p "Enter the web server document root [/var/www/html]: " DOCUMENT_ROOT
DOCUMENT_ROOT=${DOCUMENT_ROOT:-/var/www/html}

echo ""
echo "Configuration:"
echo "- API URL: $API_URL"
echo "- Environment: $ENVIRONMENT"
echo "- Document Root: $DOCUMENT_ROOT"
echo ""

read -p "Continue with deployment? (y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

# Create backup if existing files found
if [ -d "$DOCUMENT_ROOT" ] && [ "$(ls -A $DOCUMENT_ROOT)" ]; then
    BACKUP_DIR="$DOCUMENT_ROOT.backup.$(date +%Y%m%d_%H%M%S)"
    echo "Creating backup at $BACKUP_DIR..."
    sudo cp -r "$DOCUMENT_ROOT" "$BACKUP_DIR"
fi

# Copy frontend files
echo "Copying frontend files..."
sudo cp -r ./* "$DOCUMENT_ROOT/"

# Update configuration file
echo "Updating configuration..."
sudo sed -i "s|const API_BASE_URL = 'http://localhost:3000';|const API_BASE_URL = '$API_URL';|" "$DOCUMENT_ROOT/config.php"

# Set production settings if needed
if [ "$ENVIRONMENT" = "production" ]; then
    echo "Applying production settings..."
    sudo sed -i "s|const DEBUG_MODE = true;|const DEBUG_MODE = false;|" "$DOCUMENT_ROOT/config.php"
    sudo sed -i "s|const ENABLE_HTTPS_ONLY = false;|const ENABLE_HTTPS_ONLY = true;|" "$DOCUMENT_ROOT/config.php"
    sudo sed -i "s|const SECURE_COOKIES = false;|const SECURE_COOKIES = true;|" "$DOCUMENT_ROOT/config.php"
fi

# Set permissions
echo "Setting file permissions..."
sudo find "$DOCUMENT_ROOT" -type f -name "*.php" -exec chmod 644 {} \;
sudo find "$DOCUMENT_ROOT" -type f -name "*.html" -exec chmod 644 {} \;
sudo find "$DOCUMENT_ROOT" -type f -name "*.css" -exec chmod 644 {} \;
sudo find "$DOCUMENT_ROOT" -type f -name "*.js" -exec chmod 644 {} \;
sudo find "$DOCUMENT_ROOT" -type d -exec chmod 755 {} \;

# Set ownership (adjust as needed)
if [ "$ENVIRONMENT" = "production" ]; then
    echo "Setting ownership to www-data..."
    sudo chown -R www-data:www-data "$DOCUMENT_ROOT"
fi

# Create necessary directories
echo "Creating necessary directories..."
sudo mkdir -p "$DOCUMENT_ROOT/logs"
sudo mkdir -p "$DOCUMENT_ROOT/tmp"

# Set writable permissions for logs and tmp
sudo chmod 755 "$DOCUMENT_ROOT/logs"
sudo chmod 755 "$DOCUMENT_ROOT/tmp"

# Create .htaccess for Apache (if needed)
if command -v apache2 >/dev/null 2>&1; then
    echo "Creating .htaccess for Apache..."
    sudo tee "$DOCUMENT_ROOT/.htaccess" > /dev/null <<EOF
# TLS Crypto Wallet Frontend .htaccess

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "camera=(), microphone=(), geolocation=()"
</IfModule>

# HTTPS redirect (enable in production)
<IfModule mod_rewrite.c>
    RewriteEngine On
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# PHP security
<FilesMatch "\.php$">
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
    </IfModule>
</FilesMatch>

# Prevent access to sensitive files
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Prevent access to config files
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>
EOF
fi

# Test PHP configuration
echo "Testing PHP configuration..."
php -l "$DOCUMENT_ROOT/index.php" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ PHP syntax check passed"
else
    echo "✗ PHP syntax check failed"
    exit 1
fi

# Check required PHP extensions
echo "Checking required PHP extensions..."
REQUIRED_EXTENSIONS=("curl" "json" "session" "mbstring")

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if php -m | grep -q "^$ext$"; then
        echo "✓ $ext extension found"
    else
        echo "✗ $ext extension missing"
        echo "Please install php-$ext"
        exit 1
    fi
done

# Create systemd service for development server (optional)
if [ "$ENVIRONMENT" = "development" ]; then
    read -p "Create systemd service for PHP development server? (y/N): " CREATE_SERVICE
    if [[ $CREATE_SERVICE =~ ^[Yy]$ ]]; then
        sudo tee "/etc/systemd/system/tls-frontend.service" > /dev/null <<EOF
[Unit]
Description=TLS Crypto Wallet Frontend Development Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=$DOCUMENT_ROOT
ExecStart=/usr/bin/php -S localhost:8080
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

        sudo systemctl daemon-reload
        sudo systemctl enable tls-frontend.service
        echo "✓ Systemd service created. Start with: sudo systemctl start tls-frontend"
    fi
fi

echo ""
echo "=================================="
echo "Deployment completed successfully!"
echo "=================================="
echo ""
echo "Next steps:"
echo "1. Ensure your backend API is running at: $API_URL"
echo "2. Test the frontend by visiting: http://your-domain/index.php"
echo "3. Check the test page at: http://your-domain/test.html"
echo ""

if [ "$ENVIRONMENT" = "production" ]; then
    echo "Production checklist:"
    echo "- ✓ HTTPS configured"
    echo "- ✓ Secure cookies enabled"
    echo "- ✓ Debug mode disabled"
    echo "- ✓ File permissions set"
    echo "- □ SSL certificate installed"
    echo "- □ Firewall configured"
    echo "- □ Database backups scheduled"
    echo "- □ Monitoring configured"
fi

echo ""
echo "For support, check the README.md file or the test page."
