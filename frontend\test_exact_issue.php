<?php
/**
 * Test Exact Issue - Find the precise problem
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

if (!isset($_SESSION['token']) || !isset($_SESSION['user_id'])) {
    echo "❌ Please login first\n";
    exit;
}

echo "🔍 Testing Exact Issue with Sweep Funds\n";
echo "=======================================\n\n";

// Test different URL patterns
$baseUrl = "http://" . $_SERVER['HTTP_HOST'];
$testUrls = [
    "{$baseUrl}/tls/backend/src/index.php/api/sweep-funds",
    "{$baseUrl}/tls/backend/src/api/sweep-funds", 
    "{$baseUrl}/backend/src/index.php/api/sweep-funds",
    "{$baseUrl}/tls/backend/src/index.php?route=sweep-funds"
];

foreach ($testUrls as $i => $url) {
    echo "Test " . ($i + 1) . ": {$url}\n";
    echo str_repeat("-", 50) . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $_SESSION['token']
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP Code: {$httpCode}\n";
    if ($curlError) {
        echo "cURL Error: {$curlError}\n";
    }
    
    echo "Response Length: " . strlen($response) . " bytes\n";
    echo "Response Preview: " . substr($response, 0, 100) . "\n";
    
    // Check if it's JSON
    $jsonData = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ Valid JSON Response\n";
        if (isset($jsonData['error'])) {
            echo "Error: " . $jsonData['error'] . "\n";
        }
        if (isset($jsonData['success'])) {
            echo "Success: " . ($jsonData['success'] ? 'true' : 'false') . "\n";
        }
    } else {
        echo "❌ Invalid JSON: " . json_last_error_msg() . "\n";
        
        // Check for common issues
        if (strpos($response, '404') !== false || strpos($response, 'Not Found') !== false) {
            echo "⚠️ 404 Not Found - URL is incorrect\n";
        }
        if (strpos($response, 'Fatal error') !== false) {
            echo "⚠️ PHP Fatal Error detected\n";
        }
        if (strpos($response, 'Endpoint not found') !== false) {
            echo "⚠️ Router says endpoint not found\n";
        }
    }
    
    echo "\n";
}

// Test the exact URL that ApiService would use
echo "🎯 Testing ApiService URL Pattern\n";
echo "=================================\n";

try {
    require_once 'src/Services/ApiService.php';
    
    // Create ApiService instance to see what URL it generates
    $apiService = new \Simbi\Tls\Frontend\Services\ApiService();
    
    // Use reflection to get the base URL
    $reflection = new ReflectionClass($apiService);
    $baseUrlProperty = $reflection->getProperty('baseUrl');
    $baseUrlProperty->setAccessible(true);
    $detectedBaseUrl = $baseUrlProperty->getValue($apiService);
    
    echo "Detected Base URL: {$detectedBaseUrl}\n";
    echo "Full Sweep URL: {$detectedBaseUrl}/api/sweep-funds\n\n";
    
    // Test this exact URL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $detectedBaseUrl . '/api/sweep-funds');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $_SESSION['token']
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP Code: {$httpCode}\n";
    echo "Response: " . substr($response, 0, 200) . "\n";
    
    $jsonData = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ This URL works! JSON response received.\n";
        print_r($jsonData);
    } else {
        echo "❌ This URL has issues: " . json_last_error_msg() . "\n";
    }
    
} catch (Exception $e) {
    echo "Error testing ApiService: " . $e->getMessage() . "\n";
}

echo "\n🏁 Test Complete!\n";
echo "The working URL should be used in the ApiService.\n";
?>
