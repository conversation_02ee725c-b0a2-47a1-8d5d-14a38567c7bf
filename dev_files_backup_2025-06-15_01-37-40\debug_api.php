<?php
require_once 'vendor/autoload.php';

use Simbi\Tls\Controllers\InvestmentController;

try {
    echo "Debug Investment API...\n";
    echo "======================\n";
    
    $controller = new InvestmentController();
    $result = $controller->getInvestmentPlans();
    
    echo "Raw result:\n";
    var_dump($result);
    
    echo "\nJSON output:\n";
    echo json_encode($result, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
