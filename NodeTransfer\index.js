import pkg from 'tronweb';
const { TronWeb } = pkg;

// --- Setup ---
const tronWeb = new TronWeb({
  fullHost: 'https://api.nileex.io',
  privateKey: 'b6a2bb28110d132b9f74c4db4e72b22b855d7c7b71a1240cd66b88159b65cf57' // Required for sending tokens
});

const usdtContract = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'; // USDT on Nile
const sender = 'TRNRXX7c4dAWNaHF5fYyLUjE7Mn7bgpPMr';       // Your wallet address
const recipient = 'THxzYZEZv6WKX5SjvpK5hm6kX9rV1cVJBt';     // Receiver address

async function transferUSDT() {
  try {
    // --- Check TRX Balance ---
    const trxBalance = await tronWeb.trx.getBalance(sender);
    console.log('TRX Balance:', tronWeb.fromSun(trxBalance), 'TRX');

    // --- Load Contract ---
    const contract = await tronWeb.contract().at(usdtContract);

    // --- Check USDT Balance ---
    const usdtBalance = await contract.balanceOf(sender).call();
    console.log('USDT Balance:', Number(usdtBalance.toString()) / 1e6, 'USDT');

    // --- Transfer USDT ---
    const amount = BigInt(10) * BigInt(1_000_000); // 10 USDT (6 decimals)
    const tx = await contract.transfer(recipient, amount).send();
    console.log('✅ Transfer Successful! TX ID:', tx);
  } catch (err) {
    console.error('❌ Error transferring USDT:', err);
  }
}

transferUSDT();
