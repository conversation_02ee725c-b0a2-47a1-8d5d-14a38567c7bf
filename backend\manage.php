<?php
// CLI Management Tool for TRON Wallet API
require 'vendor/autoload.php';

use Simbi\Tls\Config\Database;
use Simbi\Tls\Config\Config;
use Simbi\Tls\Repositories\UserRepository;
use Simbi\Tls\Repositories\WalletRepository;
use Simbi\Tls\Services\AuthService;

// Load configuration
Config::load();

echo "=== TRON Wallet API Management Tool ===\n\n";

if ($argc < 2) {
    showHelp();
    exit(1);
}

$command = $argv[1];

try {
    switch ($command) {
        case 'setup':
            setupDatabase();
            break;
        case 'user:create':
            createUser($argv);
            break;
        case 'user:list':
            listUsers();
            break;
        case 'wallet:list':
            listWallets();
            break;
        case 'stats':
            showStats();
            break;
        case 'help':
            showHelp();
            break;
        default:
            echo "❌ Unknown command: $command\n";
            showHelp();
            exit(1);
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

function showHelp() {
    echo "Available commands:\n";
    echo "  setup              - Initialize database tables\n";
    echo "  user:create        - Create a new user\n";
    echo "  user:list          - List all users\n";
    echo "  wallet:list        - List all wallets\n";
    echo "  stats              - Show system statistics\n";
    echo "  help               - Show this help message\n\n";
    echo "Examples:\n";
    echo "  php manage.php setup\n";
    echo "  php manage.php user:create <EMAIL> password123\n";
    echo "  php manage.php stats\n";
}

function setupDatabase() {
    echo "Setting up database...\n";
    
    $host = Config::get('DB_HOST');
    $db = Config::get('DB_NAME');
    $user = Config::get('DB_USER');
    $pass = Config::get('DB_PASS');
    
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db`");
    $pdo->exec("USE `$db`");
    
    // Create tables
    $tables = [
        'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email)
            )
        ",
        'wallets' => "
            CREATE TABLE IF NOT EXISTS wallets (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                address VARCHAR(255) NOT NULL UNIQUE,
                private_key TEXT NOT NULL,
                balance DECIMAL(20, 6) DEFAULT 0.000000,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_address (address)
            )
        ",
        'transactions' => "
            CREATE TABLE IF NOT EXISTS transactions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                wallet_id INT NOT NULL,
                transaction_hash VARCHAR(255) NOT NULL,
                type ENUM('deposit', 'withdrawal', 'sweep') NOT NULL,
                amount DECIMAL(20, 6) NOT NULL,
                status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (wallet_id) REFERENCES wallets(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_wallet_id (wallet_id),
                INDEX idx_tx_hash (transaction_hash)
            )
        "
    ];
    
    foreach ($tables as $name => $sql) {
        $pdo->exec($sql);
        echo "✅ Table '$name' created\n";
    }
    
    echo "✅ Database setup completed\n";
}

function createUser($argv) {
    if (count($argv) < 4) {
        echo "Usage: php manage.php user:create <email> <password>\n";
        exit(1);
    }
    
    $email = $argv[2];
    $password = $argv[3];
    
    $pdo = Database::getConnection();
    $userRepository = new UserRepository($pdo);
    $authService = new AuthService($userRepository);
    
    $result = $authService->register($email, $password);
    
    if (isset($result['error'])) {
        echo "❌ Failed to create user: " . $result['error'] . "\n";
    } else {
        echo "✅ User created successfully\n";
        echo "   ID: " . $result['user']['id'] . "\n";
        echo "   Email: " . $result['user']['email'] . "\n";
        echo "   Token: " . substr($result['token'], 0, 20) . "...\n";
    }
}

function listUsers() {
    $pdo = Database::getConnection();
    $stmt = $pdo->query("SELECT id, email, created_at FROM users ORDER BY created_at DESC");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "No users found\n";
        return;
    }
    
    echo "Users:\n";
    echo str_pad("ID", 5) . str_pad("Email", 30) . "Created\n";
    echo str_repeat("-", 60) . "\n";
    
    foreach ($users as $user) {
        echo str_pad($user['id'], 5) . 
             str_pad($user['email'], 30) . 
             $user['created_at'] . "\n";
    }
}

function listWallets() {
    $pdo = Database::getConnection();
    $stmt = $pdo->query("
        SELECT w.id, w.user_id, u.email, w.address, w.balance, w.created_at 
        FROM wallets w 
        JOIN users u ON w.user_id = u.id 
        ORDER BY w.created_at DESC
    ");
    $wallets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($wallets)) {
        echo "No wallets found\n";
        return;
    }
    
    echo "Wallets:\n";
    echo str_pad("ID", 5) . str_pad("User", 25) . str_pad("Address", 45) . str_pad("Balance", 15) . "Created\n";
    echo str_repeat("-", 110) . "\n";
    
    foreach ($wallets as $wallet) {
        echo str_pad($wallet['id'], 5) . 
             str_pad($wallet['email'], 25) . 
             str_pad($wallet['address'], 45) . 
             str_pad($wallet['balance'], 15) . 
             $wallet['created_at'] . "\n";
    }
}

function showStats() {
    $pdo = Database::getConnection();
    
    // Count users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Count wallets
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM wallets");
    $walletCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Count transactions
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM transactions");
    $transactionCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Recent activity
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $recentUsers = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM wallets WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $recentWallets = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "System Statistics:\n";
    echo "==================\n";
    echo "Total Users: $userCount\n";
    echo "Total Wallets: $walletCount\n";
    echo "Total Transactions: $transactionCount\n";
    echo "\n";
    echo "Last 24 hours:\n";
    echo "- New Users: $recentUsers\n";
    echo "- New Wallets: $recentWallets\n";
    echo "\n";
    echo "Environment: " . (Config::get('API_ENV') ?: 'development') . "\n";
    echo "Database: " . Config::get('DB_NAME') . "\n";
    echo "TRON Network: " . (Config::get('TRON_NETWORK') ?: 'testnet') . "\n";
}
